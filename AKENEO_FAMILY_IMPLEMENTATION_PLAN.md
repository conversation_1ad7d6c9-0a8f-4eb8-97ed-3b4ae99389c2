# Implementation Plan: Akeneo Product Family-Aware Transformations

## 1. Objective

To enhance the product import process by making the data transformation step "product family-aware". Currently, when transforming data for Akeneo, the system attempts to process every target attribute for every product. The goal is to intelligently skip transformations for attributes that are not relevant to a product's specific family, thereby saving significant processing time and reducing unnecessary LLM API calls.

## 2. Scope

### In Scope:
- Fetching all product families and their associated attributes from the Akeneo API.
- Caching this family-to-attribute mapping within the application's existing Redis-based configuration system.
- Modifying the **bulk transformation engine** to use this cached mapping. For each product row, the engine will read its designated "family" and only perform transformations on columns that correspond to attributes valid for that family.
- Creating a new section in the application's settings UI to allow a user to manually trigger the sync/caching process.

### Out of Scope:
- This logic will **only** apply to the bulk transformation process. Single-cell transformations will remain unchanged for now.
- The UI will not prevent a user from *mapping* columns that are irrelevant, but the backend will skip processing them during the bulk run.

---

## 3. Detailed Plan & Actionable Steps

### Backend Changes

**Task 1: Solidify Akeneo API Client **
- **File:** `akeneo-importer/src/lib/akeneo/akeneo-client.ts`
- **Action:** The methods `getFamilies()` (with pagination) and `getFamily(code)` have already been implemented and tested. This task is complete.

**Task 2: Enhance the Unified Configuration Service**
- **File:** `akeneo-importer/src/lib/unified-configuration/unified-configuration-service.ts`
- **Action:**
  - Add a new method, `saveAkeneoFamilyAttributes(familyAttributesMap: Record<string, string[]>)`. This method will take a map of `family_code -> [attribute_codes]` and save it to a dedicated key in Redis (e.g., `akeneo:family-attributes`).
  - Add a corresponding getter method, `getAkeneoFamilyAttributes(): Promise<Record<string, string[]> | null>`. This will retrieve and parse the mapping from Redis.

**Task 3: Create a Caching API Endpoint**
- **File to Create:** `akeneo-importer/src/app/api/akeneo/family-attributes/route.ts`
- **Action:**
  - Create a new `POST` route at this path.
  - This endpoint will use the `AkeneoClient` to fetch all product families.
  - It will then iterate through each family, fetch its specific attributes, and build the `familyAttributesMap`.
  - Finally, it will call `saveAkeneoFamilyAttributes` from the `UnifiedConfigurationService` to cache the result.
  - It should return a success or error response to the frontend.

**Task 4: Modify the Bulk Transformation Engine**
- **File:** `akeneo-importer/src/lib/transformation/transformation-engine.ts`
- **Action:**
  - In the primary bulk transformation method, before processing rows, call `getAkeneoFamilyAttributes()` to load the mapping into memory.
  - Identify which column in the source data corresponds to the Akeneo `family` attribute based on the user's mapping.
  - During the row-by-row iteration, for each product:
    1. Read the `productFamily` value from the appropriate column.
    2. If a `productFamily` is present and the attribute map exists, proceed to the next step. Otherwise, transform as normal.
    3. When iterating through the columns to be transformed, check if the target Akeneo attribute code exists in the list of attributes for that `productFamily` (`familyAttributesMap[productFamily]`).
    4. **If the attribute is NOT valid for the family, skip the LLM call and transformation for that cell.**
    5. If it is valid, proceed with the transformation as usual.

### Frontend Changes

**Task 5: Create Akeneo Settings Component**
- **File to Create:** `akeneo-importer/src/components/settings/akeneo-settings.tsx`
- **Action:**
  - Build a new React component.
  - This component will feature a button, e.g., "Sync Product Family Attributes".
  - On click, this button will make a `POST` request to the new `/api/akeneo/family-attributes` endpoint.
  - Implement loading and feedback states (e.g., show a spinner during the sync and a success/error toast message upon completion).
  - *(Optional but recommended)*: Add a small display area to show the last sync date or the number of families cached.

**Task 6: Integrate into Settings Page**
- **File:** `akeneo-importer/src/app/settings/page.tsx`
- **Action:**
  - Import and render the new `AkeneoSettings` component within a new settings card or tab dedicated to Akeneo.

---

## 4. Test Artifact

A standalone test script has been created and verified. It can be used for reference and direct API testing.
- **Script Location:** `akeneo-importer/scripts/fetch-akeneo-family-attributes.js`
- **How to Run:**
  ```bash
  cd akeneo-importer
  npx ts-node scripts/fetch-akeneo-family-attributes.js
  ```
This confirms that our connection to Akeneo and the logic for fetching family attributes is sound. 