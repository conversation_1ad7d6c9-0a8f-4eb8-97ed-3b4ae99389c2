version: '3.8'

services:
  # Next.js Application - Development Configuration
  app:
    build:
      context: ./akeneo-importer
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"  # Expose port for local development
    env_file:
      - ./akeneo-importer/.env  # Load environment variables from .env file
    environment:
      # Redis connection using service discovery (override .env if needed)
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      # Development environment
      - NODE_ENV=development
      # LLM Cache Service configuration
      - LLM_CACHE_SERVICE_URL=http://llm-cache:3001
      - LLM_CACHE_AUTH_TOKEN=llm-cache-default-token-2024
    volumes:
      # Mount entire app directory for robust hot-reloading
      - ./akeneo-importer:/app
      # Use an anonymous volume to prevent host node_modules from overwriting container node_modules
      - /app/node_modules
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Database
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"  # Expose Redis port for local development/debugging
    volumes:
      # Named volume - data persists between container stops/starts
      - redis_data:/data
      # Optional: Use local directory instead (uncomment to use)
      # - ./redis-data:/data
      - type: bind
        source: ./redis.conf
        target: /etc/redis/redis.conf
        read_only: true
    command: redis-server /etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # LLM Cache Service
  llm-cache:
    build:
      context: ./llm-cache-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"  # Expose cache service port
    environment:
      - PORT=3001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LLM_CACHE_AUTH_TOKEN=llm-cache-default-token-2024
      - ALLOWED_ORIGINS=*
      - RATE_LIMIT=1000
      - NODE_ENV=development
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Redis Commander (Development monitoring tool)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"  # Expose Redis Commander for local development
    environment:
      - REDIS_HOSTS=redis:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123  # Simple password for development
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - tools  # Only start when tools profile is enabled

volumes:
  redis_data:
    driver: local 