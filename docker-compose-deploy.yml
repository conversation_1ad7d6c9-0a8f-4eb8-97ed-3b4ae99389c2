version: '3.8'

services:
  app:
    build:
      context: ./akeneo-importer
      dockerfile: Dockerfile.prod
    environment:
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=production
      - LLM_CACHE_SERVICE_URL=${LLM_CACHE_SERVICE_URL:-}
      - LLM_CACHE_AUTH_TOKEN=${LLM_CACHE_AUTH_TOKEN:-}
      - SERVICE_FQDN_APP_3000=${SERVICE_FQDN_APP_3000:-}
    labels:
      - coolify.managed=true
      - coolify.type=application
      - traefik.enable=true
      - traefik.http.routers.pim-app.rule=Host(`${SERVICE_FQDN_APP}`)
      - traefik.http.routers.pim-app.entryPoints=http,https
      - traefik.http.routers.pim-app.tls=true
      - traefik.http.services.pim-app.loadbalancer.server.port=3000
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
      - type: bind
        source: ./redis.conf
        target: /etc/redis/redis.conf
        read_only: true
    command: redis-server /etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - coolify.managed=true
      - coolify.type=service

volumes:
  redis_data:
