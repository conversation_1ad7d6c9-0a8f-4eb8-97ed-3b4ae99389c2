const readline = require('readline');

// --- Helper Functions ---
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m'
};
const colorize = (text, color) => `${colors[color]}${text}${colors.reset}`;
const logSection = (title) => console.log(`\n${'='.repeat(80)}\n${colorize(title, 'cyan')}\n${'='.repeat(80)}`);
const logInstruction = (message) => console.log(colorize(`\n👉 ${message}`, 'yellow'));
const logSuccess = (message) => console.log(colorize(`✅ ${message}`, 'green'));
const logQuestion = (message) => console.log(colorize(`\n❓ ${message}`, 'magenta'));
const logInfo = (message) => console.log(`\n   ${message}`);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const waitForEnter = (prompt) => {
  return new Promise(resolve => rl.question(colorize(`\n${prompt}`, 'bright'), resolve));
};

// --- Main Test Suite ---
async function runInteractiveSuite() {
  logSection('INTERACTIVE TRANSFORMATION VERIFICATION SUITE');

  logInstruction('Please have your application running and open it in your browser.');
  logInfo('Also, open the browser\'s Developer Tools (F12) and go to the "Network" tab.');
  await waitForEnter('Press [Enter] when you are ready to begin...');
  
  // Test 1: API Spam Check
  await testApiSpam();

  // Test 2: Cancellation Check
  await testCancellation();

  // Test 3: Model and Temperature Verification
  await testModelAndTemperature();

  // Test 4: Caching Verification
  await testCaching();

  logSection('INTERACTIVE SUITE COMPLETE');
  rl.close();
}

async function testApiSpam() {
  logSection('Test 1: API Spam Verification');
  logInstruction('Navigate to the final "Data Preview and Transform" step in the Import Wizard.');
  logInstruction('Clear the network log in your browser\'s Developer Tools.');
  await waitForEnter('Press [Enter] once you are on the correct step and have cleared the network log...');

  logInstruction('Now, click the "Transform Selected" button once.');
  logInstruction('Wait for about 10 seconds. Do not click anything else.');
  await waitForEnter('After 10 seconds, press [Enter] to continue...');
  
  logInstruction('Now, click the "Cancel" button in the UI.');
  logQuestion('In the Network tab, how many requests do you see to the "/api/models/available" endpoint?');
  logInfo('A successful test should have 0, 1, or at most 2 requests. A high number indicates the API spam issue persists.');
  await waitForEnter('Press [Enter] to proceed to the next test...');
}

async function testCancellation() {
  logSection('Test 2: Bulk Cancellation Verification');
  logInstruction('Please clear the network log in your browser\'s Developer Tools again.');
  await waitForEnter('Press [Enter] when you are ready...');
  
  logInstruction('Click "Transform Selected" and then immediately click the "Cancel" button.');
  logInfo('After clicking cancel, watch the network log for about 5 seconds.');
  await waitForEnter('After observing for 5 seconds, press [Enter]...');

  logQuestion('Did you see any new requests to "/api/import/ai-transform" appear AFTER you clicked "Cancel"?');
  logInfo('A successful test should show NO new requests after cancellation.');
  await waitForEnter('Press [Enter] to proceed to the next test...');
}

async function testModelAndTemperature() {
  logSection('Test 3: Model and Temperature Verification');
  logInstruction('On the Data Preview page, select a specific model from the dropdown. Let\'s use "gemma2-9b-it".');
  logInstruction('If there is a temperature slider, set it to a unique value, like 0.88.');
  logInstruction('In your terminal where `npm run dev` is running, please get ready to observe the logs.');
  await waitForEnter('Press [Enter] when you have set the model and temperature...');

  logInstruction('Now, click "Transform Selected".');
  logQuestion('Check your terminal output. Do you see log lines indicating that the transformation is using the model "gemma2-9b-it" and a temperature of "0.88"?');
  logInfo('A successful test will show logs confirming the selected parameters were used.');
  await waitForEnter('Press [Enter] to proceed to the next test...');
}

async function testCaching() {
  logSection('Test 4: LLM Cache Verification');
  logInstruction('For this test, we will check the terminal logs for cache hit/miss messages.');
  logInstruction('Run the "Transform Selected" on a specific row for the first time.');
  await waitForEnter('Press [Enter] after the first transformation is complete...');

  logQuestion('In your `npm run dev` terminal, you should see a log indicating a "cache miss" or that the result is being set in the cache. Do you see this?');
  
  logInstruction('Now, run the "Transform Selected" on the EXACT SAME ROW again, with the same settings.');
  await waitForEnter('Press [Enter] after the second transformation is complete...');

  logQuestion('Now, check your terminal logs again. Do you see a log indicating a "cache hit"?');
  logInfo('A successful test will show a cache hit on the second run.');
  await waitForEnter('Press [Enter] to finish the test suite...');
}

runInteractiveSuite(); 