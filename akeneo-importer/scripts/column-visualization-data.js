#!/usr/bin/env node

/**
 * Column Visualization Data Fetcher
 * 
 * This script provides comprehensive visualization of all available columns from multiple data sources
 * with their configuration status, type classification, and mapping details.
 * 
 * ===================================================================================
 * DATA SOURCES & ARCHITECTURE:
 * ===================================================================================
 * 
 * 1. UNIFIED CONFIGURATION API (/api/unified-config)
 *    - Primary source for configured columns
 *    - Returns data from active configuration source (Excel or Google Sheets)
 *    - Handles authentication and caching internally
 *    - Falls back to mock data if source is not authenticated
 * 
 * 2. AKENEO API (/api/akeneo/attributes)
 *    - Returns all available PIM attributes from Akeneo system
 *    - Provides metadata like attribute type, localizability, etc.
 *    - All Akeneo columns are classified as PIM by default
 * 
 * 3. GOOGLE SHEETS API (Direct Access)
 *    - /api/google-sheets/worksheets?name=Main - Main configuration sheet
 *    - /api/google-sheets/mapping-definitions - Mapping definitions
 *    - /api/google-sheets/worksheets - List of available worksheets
 *    - Requires OAuth2 authentication (returns 401 if not authenticated)
 * 
 * ===================================================================================
 * TYPE MAPPING LOGIC (CRITICAL FOR ERP/PIM CLASSIFICATION):
 * ===================================================================================
 * 
 * The system differentiates between ERP and PIM columns based on the "Type" field:
 * 
 * FROM AKENEO API:
 * - All attributes/columns from Akeneo → PIM (Product Information Management)
 * 
 * FROM CONFIGURATION SOURCES (Excel/Google Sheets):
 * - Type = "Navisionvorlage" → ERP (Enterprise Resource Planning)
 * - Type = "Akeneo" → PIM (Product Information Management)
 * 
 * This classification is used throughout the system:
 * - Import wizard filters columns by ERP/PIM
 * - Data transformation applies different rules per type
 * - Export processes handle ERP/PIM columns differently
 * 
 * ===================================================================================
 * AUTHENTICATION & ERROR HANDLING:
 * ===================================================================================
 * 
 * GOOGLE SHEETS AUTHENTICATION:
 * - Uses OAuth2 flow with stored tokens in Redis
 * - If not authenticated: endpoints return 401 Unauthorized
 * - Unified config falls back to mock data (5 columns)
 * - Direct Google Sheets API calls will fail
 * 
 * AKENEO AUTHENTICATION:
 * - Uses API token-based authentication
 * - Independent of Google Sheets authentication
 * - Generally more stable and doesn't require OAuth flow
 * 
 * ERROR HANDLING STRATEGY:
 * - Continue processing even if some sources fail
 * - Provide detailed error messages and fallback data
 * - Log all API calls and response statuses
 * - Graceful degradation (partial data better than no data)
 * 
 * ===================================================================================
 * DATA MERGING & DEDUPLICATION:
 * ===================================================================================
 * 
 * 1. Load configured columns from unified config (highest priority)
 * 2. Load Akeneo columns and mark those already configured
 * 3. Load Google Sheets columns for debugging/comparison
 * 4. Merge without duplicates (configured data takes precedence)
 * 5. Apply consistent type classification across all sources
 * 6. Generate statistics and visualizations
 */

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

/**
 * Centralized fetch function with error handling and logging
 * 
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options (headers, method, etc.)
 * @returns {Object} - Standardized response object with success flag
 * 
 * BEHAVIOR:
 * - Logs all API calls for debugging
 * - Handles HTTP errors gracefully
 * - Returns consistent response format
 * - Continues execution even on failures
 */
async function fetchWithErrorHandling(url, options = {}) {
  try {
    console.log(`📡 Fetching: ${url}`);
    const response = await fetch(url, options);
    
    if (!response.ok) {
      console.error(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      return { success: false, error: `HTTP ${response.status}` };
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`❌ Fetch Error:`, error.message);
    return { success: false, error: error.message };
  }
}

async function fetchAkeneoColumns() {
  console.log('\n🔍 Fetching Akeneo columns...');
  const data = await fetchWithErrorHandling(`${BASE_URL}/api/akeneo/attributes?all=true`);
  
  if (!data.success) {
    console.log('⚠️ Akeneo not available or configured');
    return [];
  }
  
  const columns = (data.attributes || []).map(attr => {
    // Enhanced description extraction from Akeneo API
    let description = '';
    let labelDescription = '';
    let guidelineDescription = '';
    
    // Extract label (typically the display name in German)
    if (attr.labels && attr.labels.de_DE) {
      labelDescription = attr.labels.de_DE;
    }
    
    // Extract guidelines (typically more detailed usage instructions)
    if (attr.guidelines && attr.guidelines.de_DE) {
      guidelineDescription = attr.guidelines.de_DE;
    }
    
    // Combine descriptions intelligently
    if (labelDescription && guidelineDescription) {
      description = `${labelDescription} - ${guidelineDescription}`;
    } else if (labelDescription) {
      description = labelDescription;
    } else if (guidelineDescription) {
      description = guidelineDescription;
    }
    
    return {
      column_name: attr.code,
      display_name: attr.labels?.de_DE || attr.code,
      type: 'Akeneo',
      // TYPE MAPPING LOGIC: Everything from Akeneo API = PIM
      source: 'PIM',
      api_metadata: {
        attribute_type: attr.type,
        localizable: attr.localizable || false,
        scopable: attr.scopable || false,
        group: attr.group || null,
        unique: attr.unique || false,
        // NEW: Store original label and guideline data
        labels: attr.labels || {},
        guidelines: attr.guidelines || {},
        group_labels: attr.group_labels || {}
      },
      // Enhanced description handling
      description: description || null,
      description_source: description ? 'akeneo' : null,
      label_description: labelDescription || null,
      guideline_description: guidelineDescription || null,
      has_configuration: false, // Will be updated when comparing with config
      configured_source: null,
      default_mapping: null,
      prompt: null,
      required: false
    };
  });
  
  console.log(`✅ Fetched ${columns.length} Akeneo columns`);
  
  // NEW: Show description statistics for Akeneo columns
  const withLabels = columns.filter(col => col.label_description).length;
  const withGuidelines = columns.filter(col => col.guideline_description).length;
  const withBoth = columns.filter(col => col.label_description && col.guideline_description).length;
  
  console.log(`📊 Akeneo Description Statistics:`);
  console.log(`  - With labels (display names): ${withLabels}`);
  console.log(`  - With guidelines (detailed descriptions): ${withGuidelines}`);
  console.log(`  - With both labels and guidelines: ${withBoth}`);
  
  return columns;
}

async function fetchGoogleSheetsColumns() {
  console.log('\n🔍 Fetching Google Sheets columns...');
  
  try {
    // Try multiple endpoints to get all the data
    console.log('📡 Fetching Main worksheet...');
    const mainData = await fetchWithErrorHandling(`${BASE_URL}/api/google-sheets/worksheets?name=Main&use_cache=false`);
    
    console.log('📡 Fetching Mapping Definitions...');
    const mappingData = await fetchWithErrorHandling(`${BASE_URL}/api/google-sheets/mapping-definitions?use_cache=false`);
    
    console.log('📡 Fetching All Worksheets List...');
    const worksheetsListData = await fetchWithErrorHandling(`${BASE_URL}/api/google-sheets/worksheets`);
    
    // NEW: Try to load additional data sheets that match column names
    console.log('📡 Fetching Additional Data Sheets...');
    const additionalDataSheets = {};
    
    // If we have a list of worksheets and main data, check for additional data sheets
    if (worksheetsListData.success && mainData.success && mainData.data) {
      const availableWorksheets = worksheetsListData.data || [];
      const mainColumns = mainData.data.map(row => row.Column_name || row.column_name).filter(Boolean);
      
      console.log(`🔍 Checking for additional data sheets for ${mainColumns.length} columns...`);
      
      for (const columnName of mainColumns) {
        if (availableWorksheets.includes(columnName)) {
          console.log(`  📋 Found worksheet for column: ${columnName}`);
          try {
            const sheetData = await fetchWithErrorHandling(`${BASE_URL}/api/google-sheets/worksheets?name=${encodeURIComponent(columnName)}&use_cache=false`);
            if (sheetData.success) {
              additionalDataSheets[columnName] = {
                rowCount: sheetData.count || 0,
                data: sheetData.data || [],
                columns: sheetData.data && sheetData.data.length > 0 ? Object.keys(sheetData.data[0]) : []
              };
              console.log(`    ✅ Loaded ${sheetData.count || 0} rows from ${columnName} worksheet`);
            }
          } catch (error) {
            console.warn(`    ⚠️ Could not load ${columnName} worksheet:`, error.message);
          }
        }
      }
    }
    
    console.log('\n📋 Google Sheets Data Results:');
    console.log('═'.repeat(80));
    
    if (mainData.success) {
      console.log(`✅ Main worksheet: ${mainData.count || 0} rows`);
      if (mainData.data && mainData.data.length > 0) {
        console.log('🔍 First 5 rows from Main worksheet:');
        mainData.data.slice(0, 5).forEach((row, i) => {
          console.log(`  ${i + 1}. ${JSON.stringify(row, null, 2)}`);
        });
        
        // Count types
        const typeCount = {};
        mainData.data.forEach(row => {
          const type = row.Type || 'unknown';
          typeCount[type] = (typeCount[type] || 0) + 1;
        });
        
        console.log('\n📊 Types in Main worksheet:');
        Object.entries(typeCount).forEach(([type, count]) => {
          console.log(`  - ${type}: ${count} columns`);
        });
      }
    } else {
      console.log(`❌ Main worksheet: ${mainData.message || 'Failed'}`);
    }
    
    if (mappingData.success) {
      console.log(`✅ Mapping definitions: ${mappingData.count || 0} rows`);
      if (mappingData.data && mappingData.data.length > 0) {
        console.log('🔍 First 3 mapping definitions:');
        mappingData.data.slice(0, 3).forEach((row, i) => {
          console.log(`  ${i + 1}. ${JSON.stringify(row, null, 2)}`);
        });
      }
    } else {
      console.log(`❌ Mapping definitions: ${mappingData.message || 'Failed'}`);
    }
    
    if (worksheetsListData.success) {
      console.log(`✅ Available worksheets: ${worksheetsListData.count || 0} sheets`);
      console.log(`📋 Sheet names: ${JSON.stringify(worksheetsListData.data || [])}`);
    } else {
      console.log(`❌ Worksheets list: ${worksheetsListData.message || 'Failed'}`);
    }
    
    // Report additional data sheets
    if (Object.keys(additionalDataSheets).length > 0) {
      console.log(`\n✅ Additional Data Sheets Found: ${Object.keys(additionalDataSheets).length} sheets`);
      Object.entries(additionalDataSheets).forEach(([columnName, sheetInfo]) => {
        console.log(`  📊 ${columnName}: ${sheetInfo.rowCount} rows, columns: [${sheetInfo.columns.join(', ')}]`);
      });
    } else {
      console.log(`\n⚠️ No additional data sheets found matching column names`);
    }
    
    console.log('═'.repeat(80));
    
    // Return the main worksheet data formatted for our script
    if (mainData.success && mainData.data) {
      return mainData.data.map(row => {
        const columnName = row.Column_name || row.column_name || '';
        return {
          column_name: columnName,
          display_name: columnName,
          type: row.Type || row.type || 'text',
          // TYPE MAPPING LOGIC:
          // - Everything from Akeneo API = PIM
          // - From configuration (Excel/Google Sheets):
          //   - Type = "Navisionvorlage" → ERP 
          //   - Type = "Akeneo" → PIM
          source: (row.Type || row.type) === 'Navisionvorlage' ? 'ERP' : 'PIM',
          has_configuration: true,
          configured_source: 'google-sheets',
          default_mapping: row.Default_Mapping || row.default_mapping,
          default_mapping_content: row.Custom_Mapping_Prompt_Template || row.Default_Mapping_Content || row.default_mapping_content,
          prompt: row.Prompt || row.prompt,
          required: row.Required === true || row.Required === 'true' || row.Required === 'TRUE' || false,
          output_validation_column: row.Output_Validation_Column || row.output_validation_column,
          // NEW: Add additional data information
          has_additional_data: additionalDataSheets.hasOwnProperty(columnName),
          additional_data_rows: additionalDataSheets[columnName]?.rowCount || 0,
          additional_data_columns: additionalDataSheets[columnName]?.columns || []
        };
      });
    }
    
    return [];
  } catch (error) {
    console.error('❌ Error fetching Google Sheets data:', error);
    return [];
  }
}

async function fetchUnifiedConfiguration() {
  console.log('\n🔍 Fetching unified configuration...');
  const data = await fetchWithErrorHandling(`${BASE_URL}/api/unified-config?showBoth=true`);
  
  if (!data.success) {
    console.log('⚠️ Unified configuration not available');
    return [];
  }
  
  console.log('📋 Raw unified config data structure:');
  console.log(`  - Source: ${data.data?.source}`);
  console.log(`  - Total columns in response: ${data.data?.columns?.length || 0}`);
  
  // Debug: Show first few raw columns
  if (data.data?.columns?.length > 0) {
    console.log('🔍 First 3 raw columns:');
    data.data.columns.slice(0, 3).forEach((col, i) => {
      console.log(`  ${i + 1}. Name: "${col.column_name}", Type: "${col.type}", Required: ${col.required}`);
    });
  }
  
  const allColumns = (data.data?.columns || []).map(col => ({
    column_name: col.column_name,
    display_name: col.column_name,
    type: col.type,
    // TYPE MAPPING LOGIC:
    // - Everything from Akeneo API = PIM
    // - From configuration (Excel/Google Sheets):
    //   - Type = "Navisionvorlage" → ERP 
    //   - Type = "Akeneo" → PIM
    source: col.type === 'Navisionvorlage' ? 'ERP' : 'PIM',
    has_configuration: true,
    configured_source: data.data?.source || 'unknown',
    default_mapping: col.default_mapping,
    default_mapping_content: col.default_mapping_content,
    prompt: col.prompt,
    description: col.description,
    required: col.required || false,
    output_validation_column: col.output_validation_column
  }));
  
  // Filter out invalid/empty columns
  const validColumns = allColumns.filter(col => 
    col.column_name && 
    col.column_name.trim() !== '' && 
    col.column_name !== 'undefined' &&
    col.type && 
    col.type.trim() !== ''
  );
  
  console.log(`📊 Column filtering results:`);
  console.log(`  - Total columns received: ${allColumns.length}`);
  console.log(`  - Valid columns (with names): ${validColumns.length}`);
  console.log(`  - Invalid/empty columns filtered out: ${allColumns.length - validColumns.length}`);
  
  if (validColumns.length > 0) {
    console.log('✅ Valid columns by type:');
    const byType = validColumns.reduce((acc, col) => {
      acc[col.type] = (acc[col.type] || 0) + 1;
      return acc;
    }, {});
    Object.entries(byType).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count} columns`);
    });
  }
  
  console.log(`✅ Fetched ${validColumns.length} valid configured columns from ${data.data?.source}`);
  return validColumns;
}

/**
 * Merge column data from multiple sources with deduplication
 * 
 * @param {Array} configuredColumns - Columns from unified configuration (primary source)
 * @param {Array} akeneoColumns - Columns from Akeneo API
 * @param {Array} googleSheetsColumns - Columns from direct Google Sheets API
 * @returns {Array} - Merged and deduplicated column array
 * 
 * MERGING PRIORITY (highest to lowest):
 * 1. Configured columns (from unified config) - ALWAYS takes precedence
 * 2. Akeneo columns - Added if not already configured
 * 3. Google Sheets columns - Added if not in other sources
 * 
 * DEDUPLICATION STRATEGY:
 * - Uses column_name as unique key
 * - Preserves configuration data over raw API data
 * - Enhances configured columns with API metadata when available
 * - Avoids creating duplicate entries
 * 
 * WHY THIS APPROACH:
 * - Configured columns represent user intent and manual curation
 * - API columns provide comprehensive coverage but may lack configuration
 * - Google Sheets direct access provides debugging/comparison data
 */
function mergeColumnData(configuredColumns, akeneoColumns, googleSheetsColumns) {
  console.log('\n🔄 Merging column data...');
  
  const columnMap = new Map();
  
  // STEP 1: Add configured columns (highest priority)
  // These represent manually curated column configurations with type classification
  configuredColumns.forEach(col => {
    columnMap.set(col.column_name, col);
  });
  
  // STEP 2: Add Akeneo columns that aren't already configured
  // This ensures we capture all available PIM attributes even if not configured
  akeneoColumns.forEach(col => {
    if (!columnMap.has(col.column_name)) {
      columnMap.set(col.column_name, col);
    } else {
      // Enhance existing configured column with API metadata AND description info
      const existing = columnMap.get(col.column_name);
      existing.api_metadata = col.api_metadata;
      
      // NEW: Enhance description information from Akeneo
      // If the configured column doesn't have a description but Akeneo does, use Akeneo's
      if (!existing.description && col.description) {
        existing.description = col.description;
        existing.description_source = 'akeneo';
        existing.label_description = col.label_description;
        existing.guideline_description = col.guideline_description;
      } else if (existing.description && col.description) {
        // If both have descriptions, combine them intelligently
        existing.combined_description = `${existing.description} | Akeneo: ${col.description}`;
        existing.description_source = 'both';
        existing.label_description = col.label_description;
        existing.guideline_description = col.guideline_description;
      }
    }
  });
  
  // STEP 3: Add Google Sheets columns that aren't in other sources
  // Also merge additional data information from Google Sheets columns into existing configured columns
  googleSheetsColumns.forEach(col => {
    if (!columnMap.has(col.column_name)) {
      columnMap.set(col.column_name, col);
    } else {
      // Enhance existing configured column with additional data information from Google Sheets
      const existing = columnMap.get(col.column_name);
      if (col.has_additional_data) {
        existing.has_additional_data = col.has_additional_data;
        existing.additional_data_rows = col.additional_data_rows;
        existing.additional_data_columns = col.additional_data_columns;
      }
    }
  });
  
  const mergedColumns = Array.from(columnMap.values());
  console.log(`✅ Merged data: ${mergedColumns.length} total columns`);
  
  return mergedColumns;
}

function generateStatistics(columns) {
  const stats = {
    total: columns.length,
    erp: columns.filter(col => col.source === 'ERP').length,
    pim: columns.filter(col => col.source === 'PIM').length,
    configured: columns.filter(col => col.has_configuration).length,
    unconfigured: columns.filter(col => !col.has_configuration).length,
    required: columns.filter(col => col.required).length,
    with_ai_prompts: columns.filter(col => col.prompt).length,
    with_descriptions: columns.filter(col => col.description && col.description.trim() !== '').length,
    // NEW: Additional data statistics
    with_additional_data: columns.filter(col => col.has_additional_data).length,
    total_additional_rows: columns.reduce((sum, col) => sum + (col.additional_data_rows || 0), 0),
  };
  
  return stats;
}

function formatAsTable(columns) {
  console.log('\n📊 Column Visualization Table');
  console.log('═'.repeat(180));
  
  if (columns.length === 0) {
    console.log('❌ No columns to display');
    return;
  }
  
  // Header
  const header = `${'Column Name'.padEnd(30)} ${'Type'.padEnd(15)} ${'Source'.padEnd(8)} ${'Config'.padEnd(8)} ${'Required'.padEnd(8)} ${'Mapping'.padEnd(15)} ${'AI Prompt'.padEnd(10)} ${'Description Source'.padEnd(16)} ${'Additional Data'.padEnd(15)}`;
  console.log(header);
  console.log('─'.repeat(180));
  
  // Sort columns by source, then by name
  const sortedColumns = columns.sort((a, b) => {
    if (a.source !== b.source) {
      return a.source === 'ERP' ? -1 : 1;
    }
    return a.column_name.localeCompare(b.column_name);
  });
  
  // Show first 50 columns to avoid overwhelming output
  const displayColumns = sortedColumns.slice(0, 50);
  
  displayColumns.forEach(col => {
    const name = col.column_name.substring(0, 29).padEnd(30);
    const type = (col.type || '').substring(0, 14).padEnd(15);
    const source = col.source.padEnd(8);
    const configured = (col.has_configuration ? '✓' : '✗').padEnd(8);
    const required = (col.required ? '✓' : '✗').padEnd(8);
    const mapping = (col.default_mapping || 'None').substring(0, 14).padEnd(15);
    const hasPrompt = (col.prompt ? '✓' : '✗').padEnd(10);
    
    // NEW: Enhanced description information
    let descriptionSource = 'None';
    if (col.description) {
      if (col.description_source === 'akeneo') {
        descriptionSource = 'Akeneo';
      } else if (col.description_source === 'both') {
        descriptionSource = 'Both';
      } else if (col.configured_source === 'google-sheets') {
        descriptionSource = 'Google Sheets';
      } else {
        descriptionSource = 'Available';
      }
    }
    const descSource = descriptionSource.substring(0, 15).padEnd(16);
    
    // Additional data information
    const additionalDataInfo = col.has_additional_data 
      ? `${col.additional_data_rows} rows` 
      : 'None';
    const additionalData = additionalDataInfo.substring(0, 14).padEnd(15);
    
    console.log(`${name} ${type} ${source} ${configured} ${required} ${mapping} ${hasPrompt} ${descSource} ${additionalData}`);
  });
  
  if (sortedColumns.length > 50) {
    console.log(`... and ${sortedColumns.length - 50} more columns`);
  }
  
  console.log('─'.repeat(180));
  
  // NEW: Add description statistics
  const descStats = generateDescriptionStatistics(sortedColumns);
  console.log('\n📋 Description Statistics:');
  console.log(`  - Google Sheets descriptions: ${descStats.googleSheets}`);
  console.log(`  - Akeneo descriptions: ${descStats.akeneo}`);
  console.log(`  - Both sources: ${descStats.both}`);
  console.log(`  - No description: ${descStats.none}`);
}

/**
 * NEW: Generate detailed description statistics
 */
function generateDescriptionStatistics(columns) {
  return {
    googleSheets: columns.filter(col => col.description && col.configured_source === 'google-sheets' && col.description_source !== 'akeneo').length,
    akeneo: columns.filter(col => col.description && col.description_source === 'akeneo').length,
    both: columns.filter(col => col.description && col.description_source === 'both').length,
    none: columns.filter(col => !col.description).length
  };
}

/**
 * NEW: Show detailed descriptions for specific columns
 */
function showDetailedDescriptions(columns, maxColumns = 10) {
  console.log('\n📝 Detailed Descriptions (First 10 columns with descriptions):');
  console.log('═'.repeat(120));
  
  const columnsWithDescriptions = columns.filter(col => col.description).slice(0, maxColumns);
  
  columnsWithDescriptions.forEach((col, index) => {
    console.log(`\n${index + 1}. ${col.column_name} (${col.source})`);
    console.log(`   Type: ${col.type}`);
    console.log(`   Source: ${col.description_source || 'unknown'}`);
    
    if (col.description) {
      // Wrap long descriptions
      const description = col.description.length > 100 
        ? col.description.substring(0, 100) + '...' 
        : col.description;
      console.log(`   Description: ${description}`);
    }
    
    if (col.label_description && col.guideline_description) {
      console.log(`   Label: ${col.label_description}`);
      const guideline = col.guideline_description.length > 100 
        ? col.guideline_description.substring(0, 100) + '...' 
        : col.guideline_description;
      console.log(`   Guidelines: ${guideline}`);
    }
    
    if (col.combined_description) {
      const combined = col.combined_description.length > 100 
        ? col.combined_description.substring(0, 100) + '...' 
        : col.combined_description;
      console.log(`   Combined: ${combined}`);
    }
  });
  
  console.log('═'.repeat(120));
}

function formatAsJson(columns, stats) {
  const output = {
    statistics: stats,
    columns: columns,
    timestamp: new Date().toISOString(),
    filters: {
      erp_only: columns.filter(col => col.source === 'ERP'),
      pim_only: columns.filter(col => col.source === 'PIM'),
      configured_only: columns.filter(col => col.has_configuration),
      unconfigured_only: columns.filter(col => !col.has_configuration)
    }
  };
  
  return JSON.stringify(output, null, 2);
}

/**
 * Main execution function - orchestrates data fetching, merging, and visualization
 * 
 * EXECUTION FLOW:
 * 1. Parallel data fetching from all sources (performance optimization)
 * 2. Data merging with deduplication and priority handling
 * 3. Statistics generation for system overview
 * 4. Table visualization for human-readable output
 * 5. Optional JSON export for programmatic processing
 * 
 * COMMAND LINE OPTIONS:
 * --json : Output detailed JSON data to console
 * --save : Save data to timestamped JSON file
 * 
 * USAGE EXAMPLES:
 * node column-visualization-data.js
 * node column-visualization-data.js --json
 * node column-visualization-data.js --save
 * node column-visualization-data.js --json --save
 * 
 * ERROR HANDLING:
 * - Individual source failures don't stop the entire process
 * - Partial data is better than no data
 * - Detailed logging for debugging authentication and API issues
 * 
 * PERFORMANCE CONSIDERATIONS:
 * - Uses Promise.all for parallel API calls
 * - Caching handled by individual API endpoints
 * - Memory-efficient processing of large datasets
 */
async function main() {
  console.log('🚀 Starting Column Visualization Data Fetcher');
  console.log('================================================');
  
  try {
    // PARALLEL DATA FETCHING (Performance optimization)
    // Fetch from all sources simultaneously rather than sequentially
    const [configuredColumns, akeneoColumns, googleSheetsColumns] = await Promise.all([
      fetchUnifiedConfiguration(),    // Primary configuration source (Excel/Google Sheets)
      fetchAkeneoColumns(),          // PIM attributes from Akeneo API
      fetchGoogleSheetsColumns()     // Direct Google Sheets access (for debugging/comparison)
    ]);
    
    // DATA MERGING & DEDUPLICATION
    // Combine all sources with priority-based conflict resolution
    const allColumns = mergeColumnData(configuredColumns, akeneoColumns, googleSheetsColumns);
    
    // STATISTICS GENERATION
    // Calculate key metrics for system overview and health check
    const stats = generateStatistics(allColumns);
    
    // DISPLAY RESULTS
    console.log('\n📈 Statistics:');
    console.log(`Total Columns: ${stats.total}`);
    console.log(`ERP Columns: ${stats.erp} (Navisionvorlage type - for ERP system)`);
    console.log(`PIM Columns: ${stats.pim} (Akeneo type + API columns - for PIM system)`);
    console.log(`Configured: ${stats.configured} (have explicit configuration)`);
    console.log(`Unconfigured: ${stats.unconfigured} (API-only, no manual configuration)`);
    console.log(`Required: ${stats.required} (marked as required in configuration)`);
    console.log(`With AI Prompts: ${stats.with_ai_prompts} (have AI transformation prompts)`);
    console.log(`With Descriptions: ${stats.with_descriptions} (have detailed descriptions)`);
    console.log(`With Additional Data: ${stats.with_additional_data} (have dedicated worksheets with ${stats.total_additional_rows} total rows)`);
    
    // HUMAN-READABLE TABLE OUTPUT
    // Shows first 50 columns with key information
    formatAsTable(allColumns);
    
    // COMMAND LINE ARGUMENT PROCESSING
    // Handle optional output formats and file saving
    const args = process.argv.slice(2);
    
    // NEW: Show detailed descriptions if requested
    if (args.includes('--descriptions') || args.includes('--desc')) {
      showDetailedDescriptions(allColumns, 15);
    }
    
    if (args.includes('--json')) {
      console.log('\n📄 JSON Output:');
      console.log(formatAsJson(allColumns, stats));
    }
    
    if (args.includes('--save')) {
      const fs = require('fs');
      const filename = `column-visualization-${new Date().toISOString().split('T')[0]}.json`;
      fs.writeFileSync(filename, formatAsJson(allColumns, stats));
      console.log(`\n💾 Saved data to ${filename}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, fetchAkeneoColumns, fetchUnifiedConfiguration }; 