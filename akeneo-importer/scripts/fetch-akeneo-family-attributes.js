const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const { AkeneoClient } = require('../src/lib/akeneo/akeneo-client');

async function fetchFamilyAttributes() {
  console.log('Attempting to connect to Akeneo...');
  
  if (!process.env.AKENEO_ENDPOINT || !process.env.AKENEO_CLIENT_ID || !process.env.AKENEO_CLIENT_SECRET || !process.env.AKENEO_USERNAME || !process.env.AKENEO_PASSWORD) {
    console.error('Error: Missing one or more required Akeneo environment variables.');
    console.error('Please ensure AKENEO_ENDPOINT, AKENEO_CLIENT_ID, AKENEO_CLIENT_SECRET, AKENEO_USERNAME, and AKENEO_PASSWORD are set in your .env file.');
    return;
  }

  const akeneoClient = new AkeneoClient(
    process.env.AKENEO_CLIENT_ID,
    process.env.AKENEO_CLIENT_SECRET,
    process.env.AKENEO_USERNAME,
    process.env.AKENEO_PASSWORD,
    process.env.AKENEO_ENDPOINT
  );

  try {
    await akeneoClient.authenticate();
    console.log('Authentication successful.');

    console.log('\nFetching all product families...');
    const families = await akeneoClient.getFamilies();
    console.log(`Found ${families.length} product families.`);

    if (families.length === 0) {
      console.log('No product families found. Exiting.');
      return;
    }
    
    console.log('\nFetching attributes for each family...');
    
    const familyAttributesMap = {};

    for (const family of families) {
      const familyCode = family.code;
      console.log(`- Processing family: ${familyCode}`);
      
      const familyDetails = await akeneoClient.getFamily(familyCode);
      
      const attributeCodes = familyDetails.attributes.sort();
      familyAttributesMap[familyCode] = attributeCodes;
      
      console.log(`  - Found ${attributeCodes.length} attributes: [${attributeCodes.join(', ')}]`);
    }

    console.log('\n--- Summary ---');
    console.log('Product families and their associated attributes:');
    console.log(JSON.stringify(familyAttributesMap, null, 2));
    console.log('--- End of Summary ---\n');

  } catch (error) {
    console.error('\nAn error occurred during the process:');
    if (error.response) {
      console.error('Error Status:', error.response.status);
      console.error('Error Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error Message:', error.message);
    }
  }
}

fetchFamilyAttributes(); 