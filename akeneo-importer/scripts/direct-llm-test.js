#!/usr/bin/env node

/**
 * Direct OpenRouter LLM call test with concurrency
 */

const { performance } = require('perf_hooks');

// Check if fetch is available (Node 18+)
let fetch;
try {
  fetch = globalThis.fetch;
} catch {
  try {
    fetch = require('node-fetch');
  } catch {
    console.error('❌ fetch is not available. Please use Node.js 18+ or install node-fetch');
    process.exit(1);
  }
}

const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const MODEL = 'deepseek/deepseek-r1-0528';
const PROMPT = 'Say hi to me and tell me a joke';
const TEST_CALLS = 9; // Number of LLM calls to make
const TEST_CONCURRENCY_LEVELS = [1, 3, 5, 10];

if (!OPENROUTER_API_KEY) {
  console.error('❌ OPENROUTER_API_KEY environment variable is required');
  process.exit(1);
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

const log = (color, message) => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Make a single LLM call
async function makeLLMCall(callId) {
  const messages = [
    {
      role: 'user',
      content: `${PROMPT} (Call #${callId})`
    }
  ];

  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: MODEL,
        messages: messages,
        temperature: 1.0,
        max_tokens: 100
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    const content = result.choices[0]?.message?.content || '';
    
    return {
      callId,
      success: true,
      content: content.substring(0, 100) + '...',
      usage: result.usage
    };
  } catch (error) {
    return {
      callId,
      success: false,
      error: error.message
    };
  }
}

// Test with specific concurrency level
async function testConcurrency(concurrency) {
  log('cyan', `\n🚀 Testing concurrency level: ${concurrency}`);
  log('yellow', `Making ${TEST_CALLS} LLM calls with max ${concurrency} concurrent requests`);
  
  const startTime = performance.now();
  const calls = [];
  
  // Create array of call IDs
  for (let i = 1; i <= TEST_CALLS; i++) {
    calls.push(i);
  }
  
  // Process calls with limited concurrency
  const results = [];
  
  if (concurrency === 1) {
    // Sequential processing
    for (const callId of calls) {
      const result = await makeLLMCall(callId);
      results.push(result);
      log('blue', `   Call ${callId}: ${result.success ? '✅ Success' : '❌ Failed'}`);
    }
  } else {
    // Concurrent processing with batching
    for (let i = 0; i < calls.length; i += concurrency) {
      const batch = calls.slice(i, i + concurrency);
      const batchPromises = batch.map(callId => makeLLMCall(callId));
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        const callId = batch[index];
        if (result.status === 'fulfilled') {
          results.push(result.value);
          log('blue', `   Call ${callId}: ${result.value.success ? '✅ Success' : '❌ Failed'}`);
        } else {
          results.push({ callId, success: false, error: result.reason });
          log('blue', `   Call ${callId}: ❌ Failed`);
        }
      });
    }
  }
  
  const endTime = performance.now();
  const totalTime = Math.round(endTime - startTime);
  
  const successCount = results.filter(r => r.success).length;
  const failCount = results.length - successCount;
  
  log('green', `✅ Concurrency ${concurrency} completed in ${totalTime}ms`);
  log('blue', `   Success: ${successCount}/${TEST_CALLS}, Failed: ${failCount}`);
  
  return {
    concurrency,
    totalTime,
    successCount,
    failCount,
    results
  };
}

// Main test runner
async function runDirectLLMTest() {
  log('bold', '🧪 Direct OpenRouter LLM Concurrency Test');
  log('bold', '=========================================');
  log('yellow', `Model: ${MODEL}`);
  log('yellow', `Prompt: "${PROMPT}"`);
  log('yellow', `Total calls: ${TEST_CALLS}`);
  log('yellow', `Concurrency levels: ${TEST_CONCURRENCY_LEVELS.join(', ')}`);
  
  const testResults = [];
  
  try {
    // Test each concurrency level
    for (const concurrency of TEST_CONCURRENCY_LEVELS) {
      const result = await testConcurrency(concurrency);
      testResults.push(result);
      
      // Wait between tests
      if (concurrency !== TEST_CONCURRENCY_LEVELS[TEST_CONCURRENCY_LEVELS.length - 1]) {
        log('yellow', '⏳ Waiting 2 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // Print summary
    log('bold', '\n📊 Performance Comparison');
    log('bold', '=========================');
    
    const baseline = testResults[0]; // Concurrency 1
    testResults.forEach((result, index) => {
      if (index === 0) {
        log('cyan', `Concurrency ${result.concurrency}: ${result.totalTime}ms (baseline)`);
      } else {
        const improvement = ((baseline.totalTime - result.totalTime) / baseline.totalTime * 100).toFixed(1);
        const speedup = (baseline.totalTime / result.totalTime).toFixed(2);
        
        if (improvement > 0) {
          log('green', `Concurrency ${result.concurrency}: ${result.totalTime}ms (${improvement}% faster, ${speedup}x speedup)`);
        } else {
          log('red', `Concurrency ${result.concurrency}: ${result.totalTime}ms (${Math.abs(improvement)}% slower)`);
        }
      }
      
      log('blue', `  Success rate: ${result.successCount}/${TEST_CALLS} (${(result.successCount/TEST_CALLS*100).toFixed(1)}%)`);
    });
    
    // Show example responses
    log('bold', '\n💬 Sample Responses');
    log('bold', '==================');
    
    const successfulResult = testResults.find(r => r.successCount > 0);
    if (successfulResult) {
      const sampleResponse = successfulResult.results.find(r => r.success);
      if (sampleResponse) {
        log('green', `Sample response: ${sampleResponse.content}`);
      }
    }
    
    log('bold', '\n✅ Test completed!');
    
  } catch (error) {
    log('red', `❌ Test failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the test
runDirectLLMTest().catch(error => {
  log('red', `❌ Fatal error: ${error.message}`);
  console.error(error);
  process.exit(1);
}); 