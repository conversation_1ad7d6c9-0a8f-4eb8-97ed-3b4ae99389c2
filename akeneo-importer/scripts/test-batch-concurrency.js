#!/usr/bin/env node

/**
 * Test script for batch transformation concurrency
 * Tests batch sizes 1, 3, and 5 with deepseek r1 model
 * Bypasses cache to ensure direct OpenRouter calls
 */

const { performance } = require('perf_hooks');

// Check if fetch is available (Node 18+) or use a polyfill
let fetch;
try {
  fetch = globalThis.fetch;
} catch {
  // For older Node versions, you might need: npm install node-fetch
  try {
    fetch = require('node-fetch');
  } catch {
    console.error('❌ fetch is not available. Please use Node.js 18+ or install node-fetch');
    process.exit(1);
  }
}

// Configuration
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const TEST_JOB_ID = 'test_batch_concurrency';
const MODEL = 'deepseek/deepseek-r1-0528';
const TEST_CONCURRENCY_LEVELS = [1, 3, 5, 10];

// Test data - simulate a product data transformation
const TEST_ROWS = [
  {
    id: 1,
    name: 'Red T-Shirt',
    category: 'Clothing',
    price: '25.99',
    description: 'Comfortable cotton t-shirt in red color'
  },
  {
    id: 2,
    name: 'Blue Jeans',
    category: 'Clothing', 
    price: '49.99',
    description: 'Classic denim jeans in blue wash'
  },
  {
    id: 3,
    name: 'Black Sneakers',
    category: 'Footwear',
    price: '79.99', 
    description: 'Comfortable athletic sneakers in black'
  },
  {
    id: 4,
    name: 'White Hoodie',
    category: 'Clothing',
    price: '39.99',
    description: 'Cozy fleece hoodie in white color'
  },
  {
    id: 5,
    name: 'Green Cap',
    category: 'Accessories',
    price: '15.99',
    description: 'Baseball cap in forest green'
  }
];

// Test prompt - create a unique prompt with timestamp to bypass cache
const createTestPrompt = (timestamp) => `
Transform the product data into a professional e-commerce description.
Requirements:
- Create engaging marketing copy
- Include key features and benefits  
- Use persuasive language
- Keep it under 100 words
- Format as JSON with "marketing_description" field
- Test timestamp: ${timestamp}

Product data: @row

Return only valid JSON.
`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

const log = (color, message) => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Setup test job with sample data using the upload-and-grid endpoint
async function setupTestJob() {
  log('blue', '🔧 Setting up test job...');
  
  try {
    // Use the upload-and-grid endpoint which accepts JSON data
    const response = await fetch(`${BASE_URL}/api/import/upload-and-grid`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        job_name: `${TEST_JOB_ID}_${Date.now()}`,
        data: TEST_ROWS,
        source_type: 'json'
      })
    });
    
    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    if (!result.success) {
      throw new Error(`Upload failed: ${result.error}`);
    }
    
    log('green', `✅ Test job created: ${result.job_id || result.data?.job_id}`);
    return result.job_id || result.data?.job_id;
    
  } catch (error) {
    log('red', `❌ Failed to setup test job: ${error.message}`);
    
    // Fallback: try direct Redis data creation
    try {
      log('yellow', '⚠️ Trying fallback method...');
      
      const fallbackResponse = await fetch(`${BASE_URL}/api/import/process-file`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          job_id: `${TEST_JOB_ID}_${Date.now()}`,
          source_data: TEST_ROWS,
          file_type: 'json'
        })
      });
      
      if (fallbackResponse.ok) {
        const fallbackResult = await fallbackResponse.json();
        if (fallbackResult.success) {
          log('green', `✅ Test job created via fallback: ${fallbackResult.job_id}`);
          return fallbackResult.job_id;
        }
      }
    } catch (fallbackError) {
      log('red', `❌ Fallback also failed: ${fallbackError.message}`);
    }
    
    throw error;
  }
}

// Test batch transformation with specific concurrency level
async function testBatchConcurrency(jobId, concurrency) {
  const timestamp = Date.now();
  const testPrompt = createTestPrompt(timestamp);
  
  log('cyan', `\n🚀 Testing concurrency level: ${concurrency}`);
  log('yellow', `Using model: ${MODEL}`);
  log('yellow', `Bypassing cache: YES (timestamp: ${timestamp})`);
  
  const startTime = performance.now();
  
  try {
    const response = await fetch(`${BASE_URL}/api/transform/bulk`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        job_id: jobId,
        column_name: 'marketing_description',
        transformation_mode: 'ai_transform',
        ai_prompt: testPrompt,
        model: MODEL,
        batch_size: 10, // Will be overridden by the engine to use batch size 1
        concurrency: concurrency
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    
    const result = await response.json();
    const endTime = performance.now();
    const totalTime = Math.round(endTime - startTime);
    
    if (result.success) {
      log('green', `✅ Concurrency ${concurrency} completed in ${totalTime}ms`);
      log('blue', `   Task ID: ${result.task_id}`);
      log('blue', `   Processing Time: ${result.processing_time}ms`);
      log('blue', `   Workers: ${result.worker_stats?.totalWorkers || 'N/A'}`);
      log('blue', `   Completed Batches: ${result.worker_stats?.completedBatches || 'N/A'}`);
      log('blue', `   Cache Stats: ${result.stats?.cache_hits || 0} hits, ${result.stats?.cache_misses || 0} misses`);
      
      return {
        concurrency,
        success: true,
        totalTime,
        processingTime: result.processing_time,
        workerStats: result.worker_stats,
        cacheStats: result.stats,
        taskId: result.task_id
      };
    } else {
      log('red', `❌ Concurrency ${concurrency} failed: ${result.message || result.error}`);
      return {
        concurrency,
        success: false,
        error: result.message || result.error,
        totalTime
      };
    }
    
  } catch (error) {
    const endTime = performance.now();
    const totalTime = Math.round(endTime - startTime);
    
    log('red', `❌ Concurrency ${concurrency} error: ${error.message}`);
    return {
      concurrency,
      success: false,
      error: error.message,
      totalTime
    };
  }
}

// Clear cache before testing to ensure fresh results
async function clearCache() {
  log('blue', '🧹 Clearing cache...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/cache/clear`, {
      method: 'POST'
    });
    
    if (response.ok) {
      const result = await response.json();
      log('green', `✅ Cache cleared: ${result.cleared_keys || 0} keys removed`);
    } else {
      log('yellow', '⚠️ Cache clear failed (might not be available)');
    }
  } catch (error) {
    log('yellow', `⚠️ Cache clear error: ${error.message}`);
  }
}

// Main test runner
async function runBatchConcurrencyTests() {
  log('bold', '🧪 Batch Concurrency Test Suite');
  log('bold', '================================');
  log('yellow', `Testing against: ${BASE_URL}`);
  log('yellow', `Model: ${MODEL}`);
  log('yellow', `Concurrency levels: ${TEST_CONCURRENCY_LEVELS.join(', ')}`);
  log('yellow', `Test rows: ${TEST_ROWS.length}`);
  
  const results = [];
  
  try {
    // Clear cache first
    await clearCache();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    
    // Setup test job
    const jobId = await setupTestJob();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds for job to be ready
    
    // Test each concurrency level
    for (const concurrency of TEST_CONCURRENCY_LEVELS) {
      const result = await testBatchConcurrency(jobId, concurrency);
      results.push(result);
      
      // Wait between tests to avoid rate limiting and ensure clear separation
      if (concurrency !== TEST_CONCURRENCY_LEVELS[TEST_CONCURRENCY_LEVELS.length - 1]) {
        log('yellow', '⏳ Waiting 3 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
    // Print summary
    log('bold', '\n📊 Test Results Summary');
    log('bold', '=======================');
    
    results.forEach(result => {
      if (result.success) {
        log('green', `Concurrency ${result.concurrency}: ${result.totalTime}ms total, ${result.processingTime}ms processing`);
        log('blue', `  Workers: ${result.workerStats?.totalWorkers}, Batches: ${result.workerStats?.completedBatches}`);
        log('blue', `  Cache: ${result.cacheStats?.cache_hits || 0} hits, ${result.cacheStats?.cache_misses || 0} misses`);
      } else {
        log('red', `Concurrency ${result.concurrency}: FAILED - ${result.error}`);
      }
    });
    
    // Performance comparison
    const successfulTests = results.filter(r => r.success);
    if (successfulTests.length > 1) {
      log('bold', '\n⚡ Performance Comparison');
      log('bold', '========================');
      
      const baseline = successfulTests[0];
      successfulTests.forEach((result, index) => {
        if (index === 0) {
          log('cyan', `Concurrency ${result.concurrency}: ${result.totalTime}ms (baseline)`);
        } else {
          const improvement = ((baseline.totalTime - result.totalTime) / baseline.totalTime * 100).toFixed(1);
          const color = improvement > 0 ? 'green' : 'red';
          const symbol = improvement > 0 ? '↑' : '↓';
          log(color, `Concurrency ${result.concurrency}: ${result.totalTime}ms (${symbol}${Math.abs(improvement)}%)`);
        }
      });
    }
    
    // Print expected vs actual behavior
    log('bold', '\n🎯 Expected Behavior Verification');
    log('bold', '=================================');
    
    const concurrentTests = successfulTests.filter(t => t.concurrency > 1);
    const sequentialTests = successfulTests.filter(t => t.concurrency === 1);
    
    if (concurrentTests.length > 0 && sequentialTests.length > 0) {
      const sequential = sequentialTests[0];
      const concurrent = concurrentTests[0];
      
      if (concurrent.totalTime < sequential.totalTime) {
        log('green', '✅ Concurrent processing is faster than sequential');
      } else {
        log('yellow', '⚠️ Concurrent processing is not showing expected speedup');
      }
      
      concurrentTests.forEach(test => {
        if (test.workerStats?.totalWorkers === test.concurrency) {
          log('green', `✅ Concurrency ${test.concurrency}: Using expected ${test.concurrency} workers`);
        } else {
          log('red', `❌ Concurrency ${test.concurrency}: Expected ${test.concurrency} workers, got ${test.workerStats?.totalWorkers}`);
        }
      });
    }
    
    log('bold', '\n✅ All tests completed!');
    
  } catch (error) {
    log('red', `❌ Test suite failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runBatchConcurrencyTests().catch(error => {
    log('red', `❌ Fatal error: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = {
  runBatchConcurrencyTests,
  testBatchConcurrency,
  setupTestJob
}; 