const puppeteer = require('puppeteer');

// --- Configuration ---
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const HEADLESS_MODE = process.env.HEADLESS_MODE !== 'false';
const SLOW_MOTION = parseInt(process.env.SLOW_MOTION, 10) || 0;
const TEST_JOB_ID = '802b01f6-e7fa-46f6-9c6e-6ab0dad4834f'; // User-provided Job ID

// --- Test Data ---
const TEST_MODEL = 'gemma2-9b-it';
const TEST_TEMPERATURE = 0.8;

// --- Helper Functions ---
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m'
};
const colorize = (text, color) => `${colors[color]}${text}${colors.reset}`;
const logSection = (title) => console.log(`\n${'='.repeat(80)}\n${colorize(title, 'cyan')}\n${'='.repeat(80)}`);
const logTest = (message) => console.log(colorize(`\n🧪 ${message}`, 'blue'));
const logSuccess = (message) => console.log(colorize(`✅ ${message}`, 'green'));
const logError = (message) => console.log(colorize(`❌ ${message}`, 'red'));
const logInfo = (message) => console.log(colorize(`📋 ${message}`, 'yellow'));

// --- Main Test Suite ---
async function runVerificationSuite() {
  logSection('SINGLE ROW TRANSFORMATION VERIFICATION SUITE');
  const browser = await puppeteer.launch({
    headless: HEADLESS_MODE,
    slowMo: SLOW_MOTION,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--start-maximized']
  });
  const page = await browser.newPage();

  const networkTracker = {
    modelApiRequests: 0,
    transformApiRequests: 0,
    cancellationTriggered: false,
    test3PayloadCorrect: false,
    test4Responses: []
  };

  page.on('request', request => handleRequest(request, networkTracker));
  page.on('response', response => handleResponse(response, networkTracker));

  try {
    await runTestFlow(page, networkTracker);
  } catch (error) {
    logError(`An unexpected error occurred: ${error.message}`);
    console.error(error.stack);
  } finally {
    await browser.close();
    logSection('VERIFICATION SUITE COMPLETE');
  }
}

function handleRequest(request, tracker) {
  if (request.url().includes('/api/models/available')) {
    tracker.modelApiRequests++;
  } else if (request.url().includes('/api/import/ai-transform')) {
    if (tracker.cancellationTriggered) {
      logError('A transform request was made AFTER cancellation was triggered!');
    }
    tracker.transformApiRequests++;

    // For Test 3
    const payload = JSON.parse(request.postData() || '{}');
    if (payload.model === TEST_MODEL && payload.temperature === TEST_TEMPERATURE) {
      tracker.test3PayloadCorrect = true;
    }
  }
}

async function handleResponse(response, tracker) {
  if (response.url().includes('/api/import/ai-transform') && response.ok()) {
    // For Test 4
    const data = await response.json();
    tracker.test4Responses.push(data);
  }
}

async function runTestFlow(page, networkTracker) {
  logTest(`Navigating to the application and selecting Job ID: ${TEST_JOB_ID}`);
  await page.goto(BASE_URL, { waitUntil: 'networkidle2' });

  // Use a more robust selector to find the job card by its text content
  const jobCardXPath = `//div[contains(., '${TEST_JOB_ID}')]`;
  await page.waitForSelector(`xpath/${jobCardXPath}`, { timeout: 10000 });
  const [jobCard] = await page.$x(jobCardXPath);
  
  if (jobCard) {
    await jobCard.click();
    await page.waitForNavigation({ waitUntil: 'networkidle2' });
    logSuccess('Navigated to the first step of the wizard for the selected job.');
  } else {
    throw new Error(`Could not find a job card containing the ID: ${TEST_JOB_ID}`);
  }
  
  // Navigate through the wizard to the last step
  logInfo('Clicking "Next" to get to the data preview step...');
  
  // Step 1 -> Step 2
  await page.click('button:has-text("Next")');
  await page.waitForNavigation({ waitUntil: 'networkidle2' });

  // Step 2 -> Step 3
  await page.click('button:has-text("Next")');
  await page.waitForNavigation({ waitUntil: 'networkidle2' });

  // Step 3 -> Step 4 (Data Preview)
  await page.click('button:has-text("Next")');
  await page.waitForNavigation({ waitUntil: 'networkidle2' });
  
  await page.waitForSelector('button:has-text("Transform All Rows")', { timeout: 15000 });
  logSuccess('Arrived at Data Preview step.');

  const transformButtonSelector = 'button:has-text("Transform Selected")';
  const cancelButtonSelector = 'button:has-text("Cancel")';

  // --- Test 1: API Spam Check ---
  logTest('Test 1: Checking for API spam during single-row transformation');
  networkTracker.modelApiRequests = 0;
  await page.click(transformButtonSelector);
  logInfo('Transformation started. Monitoring for 10 seconds...');
  await page.waitForTimeout(10000);
  if (networkTracker.modelApiRequests > 2) {
    logError(`TEST FAILED: Model API was spammed with ${networkTracker.modelApiRequests} requests.`);
  } else {
    logSuccess(`TEST PASSED: Only ${networkTracker.modelApiRequests} requests made.`);
  }
  await page.click(cancelButtonSelector);
  await page.waitForTimeout(2000);

  // --- Test 2: Cancellation Check ---
  logTest('Test 2: Verifying single-row cancellation');
  networkTracker.transformApiRequests = 0;
  networkTracker.cancellationTriggered = false;
  await page.click(transformButtonSelector);
  await page.waitForTimeout(500);
  networkTracker.cancellationTriggered = true;
  await page.click(cancelButtonSelector);
  logInfo('Cancellation clicked. Monitoring for rogue requests for 5 seconds...');
  const initialRequestCount = networkTracker.transformApiRequests;
  await page.waitForTimeout(5000);
  if (networkTracker.transformApiRequests > initialRequestCount) {
    logError(`TEST FAILED: ${networkTracker.transformApiRequests - initialRequestCount} new requests made after cancellation.`);
  } else {
    logSuccess('TEST PASSED: No new requests made after cancellation.');
  }
  
  // --- Test 3: Model and Temperature Check ---
  logTest('Test 3: Verifying correct model and temperature');
  // UI interaction to set model/temp would go here if selectors were known.
  // For now, we rely on the request listener. This test is a placeholder for payload check.
  logInfo(`Test will check for Model: ${TEST_MODEL} and Temp: ${TEST_TEMPERATURE}`);
  await page.click(transformButtonSelector);
  await page.waitForTimeout(5000);
  if (networkTracker.test3PayloadCorrect) {
    logSuccess('TEST PASSED: Correct model and temperature found in request payload.');
  } else {
    logError('TEST FAILED: Correct model and temperature NOT found in request payload.');
  }
  await page.click(cancelButtonSelector);
  await page.waitForTimeout(2000);

  // --- Test 4: Caching Check ---
  logTest('Test 4: Verifying LLM cache functionality');
  networkTracker.test4Responses = [];
  logInfo('First run (to populate cache)...');
  await page.click(transformButtonSelector);
  await page.waitForTimeout(10000);
  logInfo('Second run (should hit cache)...');
  await page.click(transformButtonSelector);
  await page.waitForTimeout(10000);
  
  if (networkTracker.test4Responses.length >= 2 && networkTracker.test4Responses[1].fromCache) {
    logSuccess('TEST PASSED: Second run successfully used the cache.');
  } else {
    logError(`TEST FAILED: Second run did not use cache. Responses received: ${networkTracker.test4Responses.length}`);
  }
  await page.click(cancelButtonSelector);
}

runVerificationSuite(); 