#!/usr/bin/env node

/**
 * Simple batch concurrency test using existing job
 */

const { performance } = require('perf_hooks');

// Check if fetch is available (Node 18+)
let fetch;
try {
  fetch = globalThis.fetch;
} catch {
  try {
    fetch = require('node-fetch');
  } catch {
    console.error('❌ fetch is not available. Please use Node.js 18+ or install node-fetch');
    process.exit(1);
  }
}

const BASE_URL = 'http://localhost:3000';
const MODEL = 'deepseek/deepseek-r1-0528';
const TEST_JOB_ID = '6dff726a-d2b4-452f-9289-f08f8856e6eb'; // Use existing job
const TEST_CONCURRENCY_LEVELS = [1, 3, 5, 10];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

const log = (color, message) => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Test batch transformation with specific concurrency level
async function testBatchConcurrency(jobId, concurrency) {
  const timestamp = Date.now();
  const testPrompt = `
Transform the product data into a professional e-commerce description.
Requirements:
- Create engaging marketing copy
- Include key features and benefits  
- Use persuasive language
- Keep it under 100 words
- Format as JSON with "marketing_description" field
- Test timestamp: ${timestamp}

Product data: @row

Return only valid JSON.
`;
  
  log('cyan', `\n🚀 Testing concurrency level: ${concurrency}`);
  log('yellow', `Using model: ${MODEL}`);
  log('yellow', `Job ID: ${jobId}`);
  log('yellow', `Unique timestamp: ${timestamp}`);
  
  const startTime = performance.now();
  
  try {
    const response = await fetch(`${BASE_URL}/api/transform/bulk`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        job_id: jobId,
        column_name: 'test_marketing_description',
        transformation_mode: 'ai_transform',
        ai_prompt: testPrompt,
        model: MODEL,
        batch_size: 10,
        concurrency: concurrency
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    
    const result = await response.json();
    const endTime = performance.now();
    const totalTime = Math.round(endTime - startTime);
    
    if (result.success) {
      log('green', `✅ Concurrency ${concurrency} completed in ${totalTime}ms`);
      log('blue', `   Task ID: ${result.task_id}`);
      log('blue', `   Processing Time: ${result.processing_time}ms`);
      log('blue', `   Workers: ${result.worker_stats?.totalWorkers || 'N/A'}`);
      log('blue', `   Completed Batches: ${result.worker_stats?.completedBatches || 'N/A'}`);
      log('blue', `   Cache Stats: ${result.stats?.cache_hits || 0} hits, ${result.stats?.cache_misses || 0} misses`);
      
      return {
        concurrency,
        success: true,
        totalTime,
        processingTime: result.processing_time,
        workerStats: result.worker_stats,
        cacheStats: result.stats,
        taskId: result.task_id
      };
    } else {
      log('red', `❌ Concurrency ${concurrency} failed: ${result.message || result.error}`);
      return {
        concurrency,
        success: false,
        error: result.message || result.error,
        totalTime
      };
    }
    
  } catch (error) {
    const endTime = performance.now();
    const totalTime = Math.round(endTime - startTime);
    
    log('red', `❌ Concurrency ${concurrency} error: ${error.message}`);
    return {
      concurrency,
      success: false,
      error: error.message,
      totalTime
    };
  }
}

// Main test runner
async function runSimpleBatchTest() {
  log('bold', '🧪 Simple Batch Concurrency Test');
  log('bold', '=================================');
  log('yellow', `Testing against: ${BASE_URL}`);
  log('yellow', `Model: ${MODEL}`);
  log('yellow', `Job ID: ${TEST_JOB_ID}`);
  log('yellow', `Concurrency levels: ${TEST_CONCURRENCY_LEVELS.join(', ')}`);
  
  const results = [];
  
  try {
    // Test each concurrency level
    for (const concurrency of TEST_CONCURRENCY_LEVELS) {
      const result = await testBatchConcurrency(TEST_JOB_ID, concurrency);
      results.push(result);
      
      // Wait between tests
      if (concurrency !== TEST_CONCURRENCY_LEVELS[TEST_CONCURRENCY_LEVELS.length - 1]) {
        log('yellow', '⏳ Waiting 3 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
    // Print summary
    log('bold', '\n📊 Test Results Summary');
    log('bold', '=======================');
    
    results.forEach(result => {
      if (result.success) {
        log('green', `Concurrency ${result.concurrency}: ${result.totalTime}ms total, ${result.processingTime}ms processing`);
        log('blue', `  Workers: ${result.workerStats?.totalWorkers}, Batches: ${result.workerStats?.completedBatches}`);
        log('blue', `  Cache: ${result.cacheStats?.cache_hits || 0} hits, ${result.cacheStats?.cache_misses || 0} misses`);
      } else {
        log('red', `Concurrency ${result.concurrency}: FAILED - ${result.error}`);
      }
    });
    
    // Performance comparison
    const successfulTests = results.filter(r => r.success);
    if (successfulTests.length > 1) {
      log('bold', '\n⚡ Speed Improvement Analysis');
      log('bold', '============================');
      
      const baseline = successfulTests[0]; // Concurrency 1
      successfulTests.forEach((result, index) => {
        if (index === 0) {
          log('cyan', `Concurrency ${result.concurrency}: ${result.totalTime}ms (baseline)`);
        } else {
          const improvement = ((baseline.totalTime - result.totalTime) / baseline.totalTime * 100).toFixed(1);
          const speedup = (baseline.totalTime / result.totalTime).toFixed(2);
          
          if (improvement > 0) {
            log('green', `Concurrency ${result.concurrency}: ${result.totalTime}ms (${improvement}% faster, ${speedup}x speedup)`);
          } else {
            log('red', `Concurrency ${result.concurrency}: ${result.totalTime}ms (${Math.abs(improvement)}% slower)`);
          }
        }
      });
      
      // Expected vs actual
      log('bold', '\n🎯 Concurrency Verification');
      log('bold', '===========================');
      
      successfulTests.forEach(test => {
        if (test.workerStats?.totalWorkers === test.concurrency) {
          log('green', `✅ Concurrency ${test.concurrency}: Using expected ${test.concurrency} workers`);
        } else {
          log('red', `❌ Concurrency ${test.concurrency}: Expected ${test.concurrency} workers, got ${test.workerStats?.totalWorkers}`);
        }
      });
    }
    
    log('bold', '\n✅ Test completed!');
    
  } catch (error) {
    log('red', `❌ Test failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the test
runSimpleBatchTest().catch(error => {
  log('red', `❌ Fatal error: ${error.message}`);
  console.error(error);
  process.exit(1);
}); 