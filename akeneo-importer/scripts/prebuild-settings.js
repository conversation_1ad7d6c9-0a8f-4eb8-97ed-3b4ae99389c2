const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Pre-building settings pages for hybrid development mode...');

// Create a temporary Next.js config for selective building
const tempConfigPath = path.join(__dirname, '..', 'next.config.prebuild.js');
const tempConfig = `
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable partial builds for settings pages only
  experimental: {
    partialPrerendering: true,
  },
  
  // Configure to only build settings-related pages
  generateBuildId: () => 'settings-prebuild',
  
  // Webpack configuration for selective building
  webpack: (config, { dev, isServer }) => {
    // Only process settings-related files
    if (!dev) {
      config.entry = async () => {
        const entries = await config.entry();
        const filteredEntries = {};
        
        // Only include settings pages and their dependencies
        Object.keys(entries).forEach(key => {
          if (key.includes('settings') || key.includes('_app') || key.includes('_document')) {
            filteredEntries[key] = entries[key];
          }
        });
        
        return filteredEntries;
      };
    }
    
    return config;
  },
  
  // Only generate static files for settings
  generateStaticParams: async () => {
    return [{ slug: ['settings'] }];
  },
};

module.exports = nextConfig;
`;

try {
  // Write temporary config
  fs.writeFileSync(tempConfigPath, tempConfig);
  
  // Create .next-settings directory for partial build
  const settingsBuildDir = path.join(__dirname, '..', '.next-settings');
  if (!fs.existsSync(settingsBuildDir)) {
    fs.mkdirSync(settingsBuildDir, { recursive: true });
  }
  
  console.log('📦 Building settings pages...');
  
  // Build only settings pages
  execSync('npx next build', {
    cwd: path.join(__dirname, '..'),
    env: {
      ...process.env,
      NEXT_CONFIG_FILE: 'next.config.prebuild.js',
      BUILD_DIR: '.next-settings'
    },
    stdio: 'inherit'
  });
  
  // Copy built settings pages to main build directory
  const mainBuildDir = path.join(__dirname, '..', '.next');
  if (!fs.existsSync(mainBuildDir)) {
    fs.mkdirSync(mainBuildDir, { recursive: true });
  }
  
  // Copy server and static files for settings
  const serverDir = path.join(settingsBuildDir, 'server');
  const staticDir = path.join(settingsBuildDir, 'static');
  const mainServerDir = path.join(mainBuildDir, 'server');
  const mainStaticDir = path.join(mainBuildDir, 'static');
  
  if (fs.existsSync(serverDir)) {
    execSync(`cp -r "${serverDir}/pages/settings" "${mainServerDir}/pages/" 2>/dev/null || true`, { shell: true });
  }
  
  if (fs.existsSync(staticDir)) {
    execSync(`cp -r "${staticDir}" "${mainBuildDir}/" 2>/dev/null || true`, { shell: true });
  }
  
  console.log('✅ Settings pages pre-built successfully!');
  console.log('🔥 Hot reloading will be available for all other pages');
  
} catch (error) {
  console.warn('⚠️  Settings pre-build failed, continuing with normal dev mode:', error.message);
} finally {
  // Clean up temporary files
  if (fs.existsSync(tempConfigPath)) {
    fs.unlinkSync(tempConfigPath);
  }
}

console.log('🚀 Ready for hybrid development mode!'); 