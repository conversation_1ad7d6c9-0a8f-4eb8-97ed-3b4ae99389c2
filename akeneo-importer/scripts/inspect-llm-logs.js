#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// ANSI color codes for better output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  gray: '\x1b[90m'
};

function log(color, message) {
  console.log(colors[color] + message + colors.reset);
}

function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString();
}

function formatDuration(duration) {
  if (!duration) return 'N/A';
  return `${duration}ms`;
}

function showUsage() {
  log('bright', '🔍 LLM Log Inspector');
  log('bright', '===================');
  console.log();
  log('cyan', 'Usage:');
  console.log('  node inspect-llm-logs.js [callId]          # Show logs for specific call ID');
  console.log('  node inspect-llm-logs.js --recent [limit]  # Show recent calls (default: 20)');
  console.log('  node inspect-llm-logs.js --help           # Show this help');
  console.log();
  log('cyan', 'Examples:');
  console.log('  node inspect-llm-logs.js A1B2C3D4         # Show all logs for call A1B2C3D4');
  console.log('  node inspect-llm-logs.js --recent 10      # Show last 10 LLM calls');
  console.log('  node inspect-llm-logs.js --recent         # Show last 20 LLM calls');
}

function getLogFiles() {
  const logsDir = path.join(process.cwd(), '..', '..', 'logs');
  
  if (!fs.existsSync(logsDir)) {
    log('red', '❌ Logs directory not found. Make sure you run this from the akeneo-importer/scripts directory.');
    process.exit(1);
  }

  const files = fs.readdirSync(logsDir)
    .filter(file => file.startsWith('llm-calls-') && file.endsWith('.jsonl'))
    .sort()
    .reverse(); // Most recent first

  return files.map(file => path.join(logsDir, file));
}

function readLogEntries(files) {
  const entries = [];
  
  for (const file of files) {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          entries.push(JSON.parse(line));
        } catch (e) {
          // Skip invalid JSON lines
        }
      }
    }
  }
  
  return entries;
}

function showCallDetails(callId) {
  log('bright', `🔍 Detailed logs for call ID: ${callId}`);
  log('bright', '='.repeat(50));
  
  const files = getLogFiles();
  const entries = readLogEntries(files);
  const callEntries = entries.filter(entry => entry.id === callId);
  
  if (callEntries.length === 0) {
    log('red', `❌ No logs found for call ID: ${callId}`);
    return;
  }
  
  // Sort by timestamp
  callEntries.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  
  for (const entry of callEntries) {
    console.log();
    log('cyan', `📅 ${formatTimestamp(entry.timestamp)}`);
    
    if (entry.error) {
      log('red', '❌ LLM CALL ERROR:');
      log('red', `Error: ${entry.error.message || entry.error}`);
      log('gray', `Model: ${entry.model}`);
      log('gray', `Provider: ${entry.provider}`);
      log('gray', `Duration: ${formatDuration(entry.duration)}`);
      if (entry.temperature) log('gray', `Temperature: ${entry.temperature}`);
    } else if (entry.response || entry.answer) {
      log('green', '✅ LLM CALL SUCCESS:');
      log('gray', `Model: ${entry.model}`);
      log('gray', `Provider: ${entry.provider}`);
      log('gray', `Duration: ${formatDuration(entry.duration)}`);
      log('gray', `From Cache: ${entry.fromCache ? 'Yes' : 'No'}`);
      if (entry.temperature) log('gray', `Temperature: ${entry.temperature}`);
      
      // Show system and user prompts from metadata
      if (entry.metadata?.systemPrompt) {
        console.log();
        log('magenta', 'System Prompt:');
        console.log(entry.metadata.systemPrompt);
      }
      
      if (entry.metadata?.userPrompt) {
        console.log();
        log('magenta', 'User Prompt:');
        console.log(entry.metadata.userPrompt);
      }
      
      if (entry.reasoning) {
        console.log();
        log('magenta', 'Reasoning:');
        console.log(entry.reasoning);
      }
      
      console.log();
      log('magenta', 'Response:');
      console.log(entry.response || entry.answer);
      
      if (entry.metadata && Object.keys(entry.metadata).length > 0) {
        console.log();
        log('gray', 'Additional Metadata:');
        const filteredMeta = { ...entry.metadata };
        delete filteredMeta.systemPrompt;
        delete filteredMeta.userPrompt;
        console.log(JSON.stringify(filteredMeta, null, 2));
      }
    }
    
    log('gray', '-'.repeat(60));
  }
}

function showRecentCalls(limit = 20) {
  log('bright', `📊 Recent LLM calls (last ${limit})`);
  log('bright', '='.repeat(40));
  
  const files = getLogFiles();
  const entries = readLogEntries(files);
  
  // Filter to main LLM call entries only
  const llmCalls = entries
    .filter(entry => entry.id && (entry.response || entry.error || entry.answer))
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, limit);
  
  if (llmCalls.length === 0) {
    log('red', '❌ No LLM calls found in logs');
    return;
  }
  
  console.log();
  for (const entry of llmCalls) {
    const status = entry.error ? '❌' : (entry.fromCache ? '💾' : '🤖');
    const provider = (entry.provider || 'unknown').toUpperCase();
    const duration = formatDuration(entry.duration);
    const temperature = entry.temperature ? ` T=${entry.temperature}` : '';
    const timestamp = formatTimestamp(entry.timestamp);
    
    log('cyan', `${status} [${entry.id}] ${provider} (${duration}${temperature}) - ${timestamp}`);
    
    if (entry.error) {
      log('red', `   Error: ${entry.error.message || entry.error}`);
    } else {
      const response = entry.response || entry.answer || '';
      const preview = response.length > 60 ? response.substring(0, 60) + '...' : response;
      log('gray', `   ${preview}`);
    }
    console.log();
  }
  
  log('yellow', `💡 To see full details for a call, use: node inspect-llm-logs.js [CALL_ID]`);
}

// Main execution
const args = process.argv.slice(2);

if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
  showUsage();
} else if (args[0] === '--recent') {
  const limit = args[1] ? parseInt(args[1]) : 20;
  showRecentCalls(limit);
} else {
  // Assume it's a call ID
  const callId = args[0];
  showCallDetails(callId);
} 