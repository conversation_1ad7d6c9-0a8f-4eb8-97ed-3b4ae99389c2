#!/usr/bin/env node

/**
 * Direct Groq LLM call test with concurrency and rate limit tracking
 */

const { performance } = require('perf_hooks');

// Check if fetch is available (Node 18+)
let fetch;
try {
  fetch = globalThis.fetch;
} catch {
  try {
    fetch = require('node-fetch');
  } catch {
    console.error('❌ fetch is not available. Please use Node.js 18+ or install node-fetch');
    process.exit(1);
  }
}

const GROQ_API_KEY = process.env.GROQ_API_KEY;
const MODEL = 'deepseek-r1-distill-llama-70b';
const PROMPT = 'Say hi to me and tell me a joke';
const TEST_CALLS = 9; // Number of LLM calls to make
const TEST_CONCURRENCY_LEVELS = [1, 3, 5, 10];

// Rate limits from Groq docs for deepseek-r1-distill-llama-70b:
// RPM: 30, RPD: 1000, TPM: 6000
const RATE_LIMITS = {
  rpm: 30,    // requests per minute
  rpd: 1000,  // requests per day  
  tpm: 6000   // tokens per minute
};

if (!GROQ_API_KEY) {
  console.error('❌ GROQ_API_KEY environment variable is required');
  process.exit(1);
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
  magenta: '\x1b[35m'
};

const log = (color, message) => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Track rate limiting
const rateLimitTracker = {
  rateLimitHits: 0,
  totalRequests: 0,
  requestTimes: [],
  
  recordRequest(startTime, endTime, success, rateLimited = false) {
    this.totalRequests++;
    this.requestTimes.push({
      startTime,
      endTime,
      duration: endTime - startTime,
      success,
      rateLimited
    });
    
    if (rateLimited) {
      this.rateLimitHits++;
    }
  },
  
  getStats() {
    const successfulRequests = this.requestTimes.filter(r => r.success);
    const avgDuration = successfulRequests.length > 0 
      ? successfulRequests.reduce((sum, r) => sum + r.duration, 0) / successfulRequests.length 
      : 0;
    
    return {
      totalRequests: this.totalRequests,
      rateLimitHits: this.rateLimitHits,
      rateLimitRate: (this.rateLimitHits / this.totalRequests * 100).toFixed(1),
      avgResponseTime: Math.round(avgDuration),
      requestsPerMinute: this.calculateRPM()
    };
  },
  
  calculateRPM() {
    if (this.requestTimes.length < 2) return 0;
    
    const firstRequest = this.requestTimes[0].startTime;
    const lastRequest = this.requestTimes[this.requestTimes.length - 1].endTime;
    const timeSpanMinutes = (lastRequest - firstRequest) / (1000 * 60);
    
    return timeSpanMinutes > 0 ? Math.round(this.totalRequests / timeSpanMinutes) : 0;
  }
};

// Make a single LLM call to Groq
async function makeLLMCall(callId) {
  const startTime = performance.now();
  
  const messages = [
    {
      role: 'user',
      content: `${PROMPT} (Call #${callId})`
    }
  ];

  try {
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${GROQ_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: MODEL,
        messages: messages,
        temperature: 0.6,
        max_tokens: 200, // Keep it reasonable for testing
        top_p: 0.95,
        stream: false // Use non-streaming for simpler testing
      })
    });

    const endTime = performance.now();
    
    // Check for rate limiting
    const isRateLimited = response.status === 429;
    
    if (isRateLimited) {
      const retryAfter = response.headers.get('retry-after');
      log('magenta', `   ⚠️ Rate limited! Retry after: ${retryAfter || 'unknown'} seconds`);
      
      rateLimitTracker.recordRequest(startTime, endTime, false, true);
      
      return {
        callId,
        success: false,
        rateLimited: true,
        retryAfter: retryAfter,
        error: `Rate limited (HTTP 429)`
      };
    }
    
    if (!response.ok) {
      rateLimitTracker.recordRequest(startTime, endTime, false, false);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    const content = result.choices[0]?.message?.content || '';
    
    rateLimitTracker.recordRequest(startTime, endTime, true, false);
    
    return {
      callId,
      success: true,
      content: content.substring(0, 100) + '...',
      usage: result.usage,
      responseTime: Math.round(endTime - startTime)
    };
  } catch (error) {
    const endTime = performance.now();
    rateLimitTracker.recordRequest(startTime, endTime, false, false);
    
    return {
      callId,
      success: false,
      error: error.message,
      responseTime: Math.round(endTime - startTime)
    };
  }
}

// Test with specific concurrency level
async function testConcurrency(concurrency) {
  log('cyan', `\n🚀 Testing concurrency level: ${concurrency}`);
  log('yellow', `Making ${TEST_CALLS} LLM calls with max ${concurrency} concurrent requests`);
  log('blue', `Rate limits: ${RATE_LIMITS.rpm} RPM, ${RATE_LIMITS.tpm} TPM`);
  
  const startTime = performance.now();
  const calls = [];
  
  // Create array of call IDs
  for (let i = 1; i <= TEST_CALLS; i++) {
    calls.push(i);
  }
  
  // Process calls with limited concurrency
  const results = [];
  
  if (concurrency === 1) {
    // Sequential processing
    for (const callId of calls) {
      const result = await makeLLMCall(callId);
      results.push(result);
      
      const status = result.success ? '✅ Success' : 
                    result.rateLimited ? '🚫 Rate Limited' : '❌ Failed';
      const timing = result.responseTime ? ` (${result.responseTime}ms)` : '';
      log('blue', `   Call ${callId}: ${status}${timing}`);
      
      // Add small delay between sequential calls to be respectful
      if (callId < calls.length) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
  } else {
    // Concurrent processing with batching
    for (let i = 0; i < calls.length; i += concurrency) {
      const batch = calls.slice(i, i + concurrency);
      const batchPromises = batch.map(callId => makeLLMCall(callId));
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        const callId = batch[index];
        if (result.status === 'fulfilled') {
          results.push(result.value);
          const status = result.value.success ? '✅ Success' : 
                        result.value.rateLimited ? '🚫 Rate Limited' : '❌ Failed';
          const timing = result.value.responseTime ? ` (${result.value.responseTime}ms)` : '';
          log('blue', `   Call ${callId}: ${status}${timing}`);
        } else {
          results.push({ callId, success: false, error: result.reason });
          log('blue', `   Call ${callId}: ❌ Failed`);
        }
      });
      
      // Add delay between batches
      if (i + concurrency < calls.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }
  
  const endTime = performance.now();
  const totalTime = Math.round(endTime - startTime);
  
  const successCount = results.filter(r => r.success).length;
  const rateLimitedCount = results.filter(r => r.rateLimited).length;
  const failCount = results.length - successCount - rateLimitedCount;
  
  log('green', `✅ Concurrency ${concurrency} completed in ${totalTime}ms`);
  log('blue', `   Success: ${successCount}/${TEST_CALLS}, Rate Limited: ${rateLimitedCount}, Failed: ${failCount}`);
  
  return {
    concurrency,
    totalTime,
    successCount,
    rateLimitedCount,
    failCount,
    results
  };
}

// Main test runner
async function runGroqLLMTest() {
  log('bold', '🧪 Groq LLM Concurrency Test with Rate Limit Tracking');
  log('bold', '===================================================');
  log('yellow', `Model: ${MODEL}`);
  log('yellow', `Prompt: "${PROMPT}"`);
  log('yellow', `Total calls: ${TEST_CALLS}`);
  log('yellow', `Concurrency levels: ${TEST_CONCURRENCY_LEVELS.join(', ')}`);
  log('yellow', `Rate limits: ${RATE_LIMITS.rpm} RPM, ${RATE_LIMITS.rpd} RPD, ${RATE_LIMITS.tpm} TPM`);
  
  const testResults = [];
  
  try {
    // Test each concurrency level
    for (const concurrency of TEST_CONCURRENCY_LEVELS) {
      const result = await testConcurrency(concurrency);
      testResults.push(result);
      
      // Wait between tests to avoid rate limiting
      if (concurrency !== TEST_CONCURRENCY_LEVELS[TEST_CONCURRENCY_LEVELS.length - 1]) {
        log('yellow', '⏳ Waiting 5 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    // Print performance summary
    log('bold', '\n📊 Performance Comparison');
    log('bold', '=========================');
    
    const baseline = testResults[0]; // Concurrency 1
    testResults.forEach((result, index) => {
      if (index === 0) {
        log('cyan', `Concurrency ${result.concurrency}: ${result.totalTime}ms (baseline)`);
      } else {
        const improvement = ((baseline.totalTime - result.totalTime) / baseline.totalTime * 100).toFixed(1);
        const speedup = (baseline.totalTime / result.totalTime).toFixed(2);
        
        if (improvement > 0) {
          log('green', `Concurrency ${result.concurrency}: ${result.totalTime}ms (${improvement}% faster, ${speedup}x speedup)`);
        } else {
          log('red', `Concurrency ${result.concurrency}: ${result.totalTime}ms (${Math.abs(improvement)}% slower)`);
        }
      }
      
      const successRate = (result.successCount / TEST_CALLS * 100).toFixed(1);
      const rateLimitRate = (result.rateLimitedCount / TEST_CALLS * 100).toFixed(1);
      log('blue', `  Success rate: ${result.successCount}/${TEST_CALLS} (${successRate}%)`);
      if (result.rateLimitedCount > 0) {
        log('magenta', `  Rate limited: ${result.rateLimitedCount}/${TEST_CALLS} (${rateLimitRate}%)`);
      }
    });
    
    // Rate limiting analysis
    log('bold', '\n🚫 Rate Limiting Analysis');
    log('bold', '=========================');
    
    const stats = rateLimitTracker.getStats();
    log('cyan', `Total requests: ${stats.totalRequests}`);
    log('cyan', `Rate limit hits: ${stats.rateLimitHits} (${stats.rateLimitRate}%)`);
    log('cyan', `Average response time: ${stats.avgResponseTime}ms`);
    log('cyan', `Actual RPM: ${stats.requestsPerMinute} (limit: ${RATE_LIMITS.rpm})`);
    
    if (stats.rateLimitHits > 0) {
      log('magenta', '⚠️ Rate limiting detected! Consider lower concurrency or longer delays.');
    } else {
      log('green', '✅ No rate limiting encountered.');
    }
    
    // Show example responses
    log('bold', '\n💬 Sample Responses');
    log('bold', '==================');
    
    const successfulResult = testResults.find(r => r.successCount > 0);
    if (successfulResult) {
      const sampleResponse = successfulResult.results.find(r => r.success);
      if (sampleResponse) {
        log('green', `Sample response: ${sampleResponse.content}`);
      }
    }
    
    log('bold', '\n✅ Test completed!');
    
  } catch (error) {
    log('red', `❌ Test failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the test
runGroqLLMTest().catch(error => {
  log('red', `❌ Fatal error: ${error.message}`);
  console.error(error);
  process.exit(1);
});