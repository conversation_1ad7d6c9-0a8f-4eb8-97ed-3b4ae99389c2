#!/usr/bin/env node

// Test script to validate transformation fixes
const fetch = require('node-fetch');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const TEST_JOB_ID = process.env.TEST_JOB_ID;

if (!TEST_JOB_ID) {
  console.error('❌ Please set TEST_JOB_ID environment variable');
  process.exit(1);
}

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function logSection(title) {
  console.log('\n' + '='.repeat(70));
  console.log(colorize(`🔍 ${title}`, 'cyan'));
  console.log('='.repeat(70));
}

async function testTemplateRendering() {
  logSection('TEST 1: Template Rendering with Placeholders');
  
  const template = `Transform product data for column @column_name.
Description: @description
Notes: @notes
Row data: @row
Additional data: @prompt_additional_data
Output format: @output_validation_field`;

  try {
    const response = await fetch(`${BASE_URL}/api/import/ai-transform`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        job_id: TEST_JOB_ID,
        column_name: 'product_type',
        prompt_template: template,
        row_index: 0,
        job_notes: 'Test job notes for validation',
        worksheet_data: [{ example: 'data' }],
        column_configuration: {
          column_name: 'product_type',
          description: 'Product category classification',
          output_validation_column: 'product_type'
        },
        is_test: false,
        bypass_cache: true
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(colorize('✅ Template rendering successful', 'green'));
      
      // Check if placeholders were replaced
      if (result.rendered_prompt && result.rendered_prompt.includes('@')) {
        console.log(colorize('❌ ISSUE: Unreplaced placeholders found in rendered prompt', 'red'));
        console.log('Rendered prompt snippet:', result.rendered_prompt.substring(0, 200) + '...');
        return false;
      } else {
        console.log(colorize('✅ All placeholders were replaced', 'green'));
      }
      
      // Check structured output
      if (result.structured_output) {
        console.log(colorize('✅ Structured output used', 'green'));
        
        if (result.reasoning) {
          console.log(colorize('✅ Reasoning extracted successfully', 'green'));
          console.log('Reasoning preview:', result.reasoning.substring(0, 150) + '...');
        } else {
          console.log(colorize('⚠️ No reasoning in structured output', 'yellow'));
        }
        
        if (result.answer) {
          console.log(colorize('✅ Clean answer extracted', 'green'));
          console.log('Answer:', result.answer);
        } else {
          console.log(colorize('❌ No answer in structured output', 'red'));
        }
      } else {
        console.log(colorize('⚠️ Not using structured output', 'yellow'));
        
        // Fallback checks for non-structured output
        if (result.result && result.result.includes('<think>')) {
          console.log(colorize('❌ ISSUE: Response contains thinking tokens', 'red'));
          console.log('Raw result:', result.result.substring(0, 100) + '...');
        }
        
        if (result.answer) {
          console.log(colorize('✅ Parsed answer available', 'green'));
          console.log('Answer:', result.answer);
        }
      }
      
      return true;
    } else {
      console.log(colorize(`❌ Template rendering failed: ${result.error}`, 'red'));
      return false;
    }
  } catch (error) {
    console.log(colorize(`❌ Template rendering error: ${error.message}`, 'red'));
    return false;
  }
}

async function testMultipleColumns() {
  logSection('TEST 2: Multiple Column Transformations (Different Prompts)');
  
  const columns = ['product_type', 'brand', 'category'];
  const results = [];
  
  for (const column of columns) {
    const template = `Transform data for @column_name column.
Context: @notes
Row: @row
Format as: @output_validation_field`;

    try {
      console.log(`\n🔄 Testing column: ${column}`);
      
      const response = await fetch(`${BASE_URL}/api/import/ai-transform`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_id: TEST_JOB_ID,
          column_name: column,
          prompt_template: template,
          row_index: 0,
          job_notes: `Notes for ${column}`,
          worksheet_data: [],
          column_configuration: {
            column_name: column,
            description: `${column} field`,
            output_validation_column: column
          },
          is_test: false,
          bypass_cache: true
        })
      });

      const result = await response.json();
      
      if (result.success) {
        console.log(colorize(`✅ ${column}: Success`, 'green'));
        
        // Check if rendered prompts are different
        results.push({
          column,
          rendered_prompt: result.rendered_prompt,
          answer: result.answer || result.result
        });
      } else {
        console.log(colorize(`❌ ${column}: Failed - ${result.error}`, 'red'));
        results.push({ column, error: result.error });
      }
    } catch (error) {
      console.log(colorize(`❌ ${column}: Error - ${error.message}`, 'red'));
      results.push({ column, error: error.message });
    }
  }
  
  // Validate that prompts are different (not cached incorrectly)
  const uniquePrompts = new Set();
  let allDifferent = true;
  
  results.forEach(r => {
    if (r.rendered_prompt) {
      uniquePrompts.add(r.rendered_prompt);
    }
  });
  
  if (uniquePrompts.size === results.filter(r => r.rendered_prompt).length) {
    console.log(colorize('\n✅ All rendered prompts are unique (no incorrect caching)', 'green'));
  } else {
    console.log(colorize('\n❌ ISSUE: Some rendered prompts are identical (possible caching issue)', 'red'));
    allDifferent = false;
  }
  
  return allDifferent && results.every(r => !r.error);
}

async function testCacheValidation() {
  logSection('TEST 3: Cache Validation (Same Request Twice)');
  
  const template = `Test cache for @column_name: @description`;
  const requestBody = {
    job_id: TEST_JOB_ID,
    column_name: 'test_column',
    prompt_template: template,
    row_index: 0,
    job_notes: 'Cache test',
    worksheet_data: [],
    column_configuration: {
      column_name: 'test_column',
      description: 'Cache test column',
      output_validation_column: 'test_column'
    },
    is_test: false,
    bypass_cache: false
  };

  try {
    console.log('🔄 First request (should hit LLM)...');
    const start1 = Date.now();
    const response1 = await fetch(`${BASE_URL}/api/import/ai-transform`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });
    const result1 = await response1.json();
    const time1 = Date.now() - start1;
    
    console.log('🔄 Second request (should use cache)...');
    const start2 = Date.now();
    const response2 = await fetch(`${BASE_URL}/api/import/ai-transform`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });
    const result2 = await response2.json();
    const time2 = Date.now() - start2;
    
    if (result1.success && result2.success) {
      console.log(colorize('✅ Both requests successful', 'green'));
      console.log(`First request: ${time1}ms, fromCache: ${result1.fromCache || false}`);
      console.log(`Second request: ${time2}ms, fromCache: ${result2.fromCache || false}`);
      
      if (result2.fromCache && time2 < time1 * 0.5) {
        console.log(colorize('✅ Cache working correctly (2nd request faster and marked as cached)', 'green'));
        return true;
      } else {
        console.log(colorize('❌ Cache not working properly', 'red'));
        return false;
      }
    } else {
      console.log(colorize('❌ One or both requests failed', 'red'));
      return false;
    }
  } catch (error) {
    console.log(colorize(`❌ Cache test error: ${error.message}`, 'red'));
    return false;
  }
}

async function testStructuredOutputWithReasoning() {
  logSection('TEST 4: Structured Output with Reasoning (qwen model)');
  
  const template = `Analyze this product and classify it for @column_name.
Product data: @row
Description: @description
Output validation: @output_validation_field

Provide detailed reasoning about your classification.`;

  try {
    const response = await fetch(`${BASE_URL}/api/import/ai-transform`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        job_id: TEST_JOB_ID,
        column_name: 'product_category',
        prompt_template: template,
        row_index: 0,
        job_notes: 'Test structured output',
        worksheet_data: [],
        column_configuration: {
          column_name: 'product_category',
          description: 'Product category classification',
          output_validation_column: 'product_category'
        },
        use_structured_output: true,
        show_reasoning: true,
        is_test: false,
        bypass_cache: true
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(colorize('✅ Structured output test successful', 'green'));
      
      if (result.structured_output) {
        console.log(colorize('✅ Using structured output format', 'green'));
      } else {
        console.log(colorize('⚠️ Not using structured output (fallback mode)', 'yellow'));
      }
      
      if (result.reasoning && result.reasoning.length > 10) {
        console.log(colorize('✅ Detailed reasoning provided', 'green'));
        console.log('Reasoning length:', result.reasoning.length, 'characters');
        console.log('Reasoning preview:', result.reasoning.substring(0, 200) + '...');
      } else {
        console.log(colorize('⚠️ Limited or no reasoning provided', 'yellow'));
      }
      
      if (result.answer && result.answer.length > 0 && result.answer.length < 50) {
        console.log(colorize('✅ Clean, concise answer provided', 'green'));
        console.log('Answer:', result.answer);
      } else {
        console.log(colorize('⚠️ Answer not in expected format', 'yellow'));
        console.log('Answer:', result.answer);
      }
      
      return true;
    } else {
      console.log(colorize(`❌ Structured output test failed: ${result.error}`, 'red'));
      return false;
    }
  } catch (error) {
    console.log(colorize(`❌ Structured output test error: ${error.message}`, 'red'));
    return false;
  }
}

async function runAllTests() {
  console.log(colorize('🚀 TRANSFORMATION FIXES VALIDATION SUITE', 'bright'));
  console.log(colorize(`Testing against job: ${TEST_JOB_ID}`, 'blue'));
  console.log(colorize('🆕 Now testing qwen/qwen3-32b with structured output', 'cyan'));
  
  const results = [];
  
  results.push(await testTemplateRendering());
  results.push(await testMultipleColumns());
  results.push(await testCacheValidation());
  results.push(await testStructuredOutputWithReasoning());
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  logSection('FINAL RESULTS');
  
  if (passed === total) {
    console.log(colorize(`🎉 ALL TESTS PASSED! (${passed}/${total})`, 'green'));
    console.log(colorize('✅ Template rendering working', 'green'));
    console.log(colorize('✅ Response parsing working', 'green'));
    console.log(colorize('✅ Multiple columns working', 'green'));
    console.log(colorize('✅ Cache working correctly', 'green'));
    console.log(colorize('✅ Structured output with reasoning', 'green'));
    console.log(colorize('\n🎯 qwen/qwen3-32b SYSTEM READY FOR PRESENTATION!', 'bright'));
  } else {
    console.log(colorize(`❌ ${total - passed} TESTS FAILED (${passed}/${total} passed)`, 'red'));
    console.log(colorize('\n⚠️ ISSUES NEED TO BE FIXED BEFORE PRESENTATION', 'yellow'));
  }
  
  return passed === total;
}

// Run the test suite
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error(colorize(`💥 Test suite crashed: ${error.message}`, 'red'));
  process.exit(1);
}); 