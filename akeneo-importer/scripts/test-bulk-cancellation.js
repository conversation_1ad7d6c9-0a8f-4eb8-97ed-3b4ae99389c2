#!/usr/bin/env node

/**
 * Test script to verify bulk operation cancellation functionality
 * This script tests the cancellation mechanism for bulk AI transformations
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_JOB_ID = 'test-job-' + Date.now();
const TEST_OPERATION_ID = 'test-op-' + Date.now();

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testBulkCancellation() {
  console.log('🧪 Testing bulk operation cancellation...');
  
  try {
    // Step 1: Create a test job (this would normally be done through file upload)
    console.log('📝 Creating test job...');
    
    // Step 2: Start a bulk AI transformation operation
    console.log('🚀 Starting bulk AI transformation...');
    const startResponse = await axios.post(`${BASE_URL}/api/grid/bulk-operations`, {
      jobId: TEST_JOB_ID,
      operationId: TEST_OPERATION_ID,
      type: 'ai-transform',
      config: {
        columns: ['test_column'],
        rowIds: ['row1', 'row2', 'row3', 'row4', 'row5'],
        aiPrompt: 'Transform this value to uppercase'
      }
    });
    
    if (startResponse.data.success) {
      console.log('✅ Bulk operation started successfully');
      console.log('Operation ID:', startResponse.data.operationId);
    } else {
      console.error('❌ Failed to start bulk operation:', startResponse.data.error);
      return;
    }
    
    // Step 3: Wait a moment to let the operation start
    console.log('⏳ Waiting 2 seconds for operation to start...');
    await sleep(2000);
    
    // Step 4: Cancel the operation
    console.log('🛑 Cancelling bulk operation...');
    const cancelResponse = await axios.post(`${BASE_URL}/api/grid/bulk-operations/${TEST_OPERATION_ID}/cancel`);
    
    if (cancelResponse.data.success) {
      console.log('✅ Bulk operation cancelled successfully');
    } else {
      console.error('❌ Failed to cancel bulk operation:', cancelResponse.data.error);
    }
    
    // Step 5: Check operation status
    console.log('📊 Checking operation status...');
    await sleep(1000);
    
    const statusResponse = await axios.get(`${BASE_URL}/api/grid/bulk-operations/${TEST_OPERATION_ID}/status`);
    
    if (statusResponse.data.success) {
      console.log('📈 Operation Status:', statusResponse.data.operation.status);
      console.log('📊 Progress:', statusResponse.data.operation.progress + '%');
      console.log('🔢 Processed rows:', statusResponse.data.operation.processedRows);
      
      if (statusResponse.data.operation.status === 'cancelled') {
        console.log('✅ SUCCESS: Operation was properly cancelled!');
      } else {
        console.log('⚠️  WARNING: Operation status is not "cancelled". Current status:', statusResponse.data.operation.status);
      }
    } else {
      console.error('❌ Failed to get operation status:', statusResponse.data.error);
    }
    
  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

async function testLLMCacheLogging() {
  console.log('\n🧪 Testing LLM Cache logging...');
  
  try {
    // Test a simple cache request to verify logging
    console.log('📝 Making test request to LLM cache service...');
    
    const testResponse = await axios.get('http://localhost:3001/api/health');
    
    if (testResponse.status === 200) {
      console.log('✅ LLM Cache health check successful');
      console.log('📊 Response data:', testResponse.data);
      console.log('📝 Check the LLM cache Docker container logs to verify request logging is working');
    }
    
  } catch (error) {
    console.error('❌ LLM Cache test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the LLM cache service is running on port 3001');
    }
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting bulk operation cancellation and logging tests...\n');
  
  await testBulkCancellation();
  await testLLMCacheLogging();
  
  console.log('\n✅ Tests completed!');
  console.log('📝 To verify LLM cache logging, check the Docker container logs with:');
  console.log('   docker logs llm-cache-dev');
}

runTests().catch(console.error); 