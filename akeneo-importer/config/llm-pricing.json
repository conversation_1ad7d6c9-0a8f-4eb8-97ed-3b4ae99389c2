{"models": {"llama-4-scout-17bx16e": {"name": "Llama 4 Scout (17Bx16E)", "provider": "groq", "speed_tokens_per_second": 460, "input_price_per_million": 0.11, "output_price_per_million": 0.34, "context_window": "128k", "model_id": "meta-llama/llama-4-scout-17b-16e-instruct", "capabilities": {"tool_use": true, "parallel_tool_use": true, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "llama-4-maverick-17bx128e": {"name": "Llama 4 Maverick (17Bx128E)", "provider": "groq", "speed_tokens_per_second": 581, "input_price_per_million": 0.2, "output_price_per_million": 0.6, "context_window": "128k", "model_id": "meta-llama/llama-4-maverick-17b-128e-instruct", "capabilities": {"tool_use": true, "parallel_tool_use": true, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "llama-guard-4-12b-128k": {"name": "Llama Guard 4 12B 128k", "provider": "groq", "speed_tokens_per_second": 325, "input_price_per_million": 0.2, "output_price_per_million": 0.2, "context_window": "128k", "model_id": "llama-guard-4-12b-128k", "capabilities": {"tool_use": false, "parallel_tool_use": false, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "deepseek-r1-distill-llama-70b": {"name": "DeepSeek R1 Distill Llama 70B", "provider": "groq", "speed_tokens_per_second": 275, "input_price_per_million": 0.75, "output_price_per_million": 0.99, "context_window": "128k", "model_id": "deepseek-r1-distill-llama-70b", "capabilities": {"tool_use": true, "parallel_tool_use": true, "json_mode": true, "reasoning_format": true, "reasoning_effort": false}}, "qwen3-32b-131k": {"name": "Qwen3 32B 131k", "provider": "groq", "speed_tokens_per_second": 491, "input_price_per_million": 0.29, "output_price_per_million": 0.59, "context_window": "131k", "model_id": "qwen/qwen3-32b", "capabilities": {"tool_use": false, "parallel_tool_use": false, "json_mode": true, "reasoning_format": true, "reasoning_effort": true}}, "qwen-qwq-32b-preview-128k": {"name": "Qwen QwQ 32B (Preview) 128k", "provider": "groq", "speed_tokens_per_second": 400, "input_price_per_million": 0.29, "output_price_per_million": 0.39, "context_window": "128k", "model_id": "qwen-qwq-32b", "capabilities": {"tool_use": true, "parallel_tool_use": true, "json_mode": true, "reasoning_format": true, "reasoning_effort": false}}, "mistral-saba-24b": {"name": "Mistral Saba 24B", "provider": "groq", "speed_tokens_per_second": 330, "input_price_per_million": 0.79, "output_price_per_million": 0.79, "context_window": "128k", "model_id": "mistral-saba-24b", "capabilities": {"tool_use": false, "parallel_tool_use": false, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "llama-3.3-70b-versatile-128k": {"name": "Llama 3.3 70B Versatile 128k", "provider": "groq", "speed_tokens_per_second": 275, "input_price_per_million": 0.59, "output_price_per_million": 0.79, "context_window": "128k", "model_id": "llama-3.3-70b-versatile", "capabilities": {"tool_use": true, "parallel_tool_use": true, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "llama-3.1-8b-instant-128k": {"name": "Llama 3.1 8B Instant 128k", "provider": "groq", "speed_tokens_per_second": 750, "input_price_per_million": 0.05, "output_price_per_million": 0.08, "context_window": "128k", "model_id": "llama-3.1-8b-instant", "capabilities": {"tool_use": true, "parallel_tool_use": true, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "llama-3-70b-8k": {"name": "Llama 3 70B 8k", "provider": "groq", "speed_tokens_per_second": 330, "input_price_per_million": 0.59, "output_price_per_million": 0.79, "context_window": "8k", "model_id": "llama-3-70b-8k", "capabilities": {"tool_use": false, "parallel_tool_use": false, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "llama-3-8b-8k": {"name": "Llama 3 8B 8k", "provider": "groq", "speed_tokens_per_second": 1250, "input_price_per_million": 0.05, "output_price_per_million": 0.08, "context_window": "8k", "model_id": "llama-3-8b-8k", "capabilities": {"tool_use": false, "parallel_tool_use": false, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "gemma-2-9b-8k": {"name": "Gemma 2 9B 8k", "provider": "groq", "speed_tokens_per_second": 500, "input_price_per_million": 0.2, "output_price_per_million": 0.2, "context_window": "8k", "model_id": "gemma2-9b-it", "capabilities": {"tool_use": true, "parallel_tool_use": false, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}, "llama-guard-3-8b-8k": {"name": "Llama Guard 3 8B 8k", "provider": "groq", "speed_tokens_per_second": 765, "input_price_per_million": 0.2, "output_price_per_million": 0.2, "context_window": "8k", "model_id": "llama-guard-3-8b-8k", "capabilities": {"tool_use": false, "parallel_tool_use": false, "json_mode": true, "reasoning_format": false, "reasoning_effort": false}}}, "tts_models": {"playai-dialog-v1": {"name": "PlayAI Dialog v1.0", "provider": "groq", "characters_per_second": 140, "price_per_million_characters": 50.0}}, "asr_models": {"whisper-v3-large": {"name": "Whisper V3 Large", "provider": "groq", "speed_factor": 189, "price_per_hour": 0.111, "minimum_seconds": 10}, "whisper-large-v3-turbo": {"name": "Whisper Large v3 Turbo", "provider": "groq", "speed_factor": 216, "price_per_hour": 0.04, "minimum_seconds": 10}, "distil-whisper": {"name": "Distil-Whisper", "provider": "groq", "speed_factor": 250, "price_per_hour": 0.02, "minimum_seconds": 10}}}