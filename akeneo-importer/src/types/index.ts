export interface JobMetadata {
  id: string;
  name: string;
  status: 'draft' | 'data-uploaded' | 'processing' | 'completed' | 'error';
  original_filename?: string;
  source_type?: 'file_upload' | 'google_sheets';
  created_at: string;
  updated_at: string;
  last_accessed?: string;
  row_count: number;
  column_count: number;
  processed_rows: number;
  columns?: string[];
  progress: {
    total_cells: number;
    processed_cells: number;
    failed_cells: number;
    estimated_completion: string | null;
  };
  processing_options?: {
    chunk_size: number;
    total_chunks: number;
  };
}

export interface MappingDefinition {
  column_name: string;
  display_name: string;
  data_type: string;
  required: boolean;
  validation_config?: ValidationConfig;
  description?: string;
}

export interface ValidationConfig {
  type: 'dropdown' | 'text' | 'number' | 'boolean';
  options?: string[];
  worksheet_name?: string;
  column_name?: string;
  min_length?: number;
  max_length?: number;
  pattern?: string;
}

export interface ColumnMapping {
  source_column: string;
  target_column: string;
  confidence?: number;
}

export interface RowData {
  [key: string]: string | number | boolean | null;
}

export interface DataPage {
  rows: RowData[];
  total_rows: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface AkeneoProduct {
  uuid: string;
  identifier?: string;
  family?: string;
  categories?: string[];
  enabled: boolean;
  values: {
    [key: string]: unknown;
  };
  created: string;
  updated: string;
  associations?: Record<string, unknown>;
  quantified_associations?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

export interface AkeneoFamily {
  code: string;
  attributes: string[];
  attribute_as_label: string;
  attribute_as_image?: string;
  labels: {
    [locale: string]: string;
  };
}

export interface AkeneoAttribute {
  code: string;
  type: string;
  group: string;
  unique: boolean;
  useable_as_grid_filter: boolean;
  allowed_extensions?: string[];
  metric_family?: string;
  default_metric_unit?: string;
  reference_data_name?: string;
  available_locales?: string[];
  max_characters?: number;
  validation_rule?: string;
  validation_regexp?: string;
  wysiwyg_enabled?: boolean;
  number_min?: number;
  number_max?: number;
  decimals_allowed?: boolean;
  negative_allowed?: boolean;
  date_min?: string;
  date_max?: string;
  max_file_size?: string;
  sort_order?: number;
  localizable: boolean;
  scopable: boolean;
  labels: {
    [locale: string]: string;
  };
  guidelines?: {
    [locale: string]: string;
  };
}

export interface AkeneoCategory {
  code: string;
  parent?: string;
  labels: {
    [locale: string]: string;
  };
}

export interface SupplierConfig {
  [familyCode: string]: {
    [attributeCode: string]: boolean;
  };
}

export interface LLMRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface LLMResponse {
  content: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Structured LLM Response for reasoning + answer separation
export interface StructuredLLMAPIResponse {
  success: boolean;
  message: string;
  result: string;
  // Structured output fields
  structured_output: boolean;
  reasoning: string;
  answer: string;
  column_name: string;
  transformed_rows: number;
  fromCache: boolean;
  cache_bypassed: boolean;
  cache_stats: any;
  provider: 'openrouter' | 'groq';
  debug_info: {
    system_prompt: string;
    user_prompt: string;
    enhanced_user_prompt: string;
    prompt_structure: string;
    cache_setting: string;
    structured_output_used: boolean;
    show_reasoning: boolean;
    provider: string;
    messages_preview: Array<{
      role: string;
      content_length: number;
      content_preview: string;
    }>;
  };
}

// Prompt Management Types
export interface PromptTemplate {
  id: string;
  name: string;
  description?: string;
  template: string;
  placeholders: string[];
  created_at: string;
  updated_at: string;
  usage_count: number;
}

export interface PlaceholderData {
  row?: any;
  prompt?: string;
  notes?: string;
  context?: string;
  taskDescription?: string;
  examples?: string;
  styleGuide?: string;
  constraints?: string;
  format?: string;
  audience?: string;
  prompt_additional_data?: string;
  prompt_additional_data_column?: string;
  [key: string]: any;
}

export interface PlaceholderProcessingResult {
  processedTemplate: string;
  placeholdersUsed: string[];
  placeholdersMissing: string[];
  linesHidden: number;
  errors: string[];
}

export interface ProgressUpdate {
  status: string;
  percent: number;
  current_operation: string;
  current_row?: number;
  total_rows?: number;
  errors?: string[];
}

export interface BulkTransformRequest {
  job_id: string;
  logic: string;
  column_name: string;
  batch_size: number;
  page: number;
  max_total_rows?: number;
  references: References;
  validation_config?: ValidationConfig;
  model_name?: string;
}

export interface References {
  validation_data?: {
    [key: string]: string[];
  };
  akeneo_families?: AkeneoFamily[];
  akeneo_attributes?: AkeneoAttribute[];
  akeneo_categories?: AkeneoCategory[];
}

export interface FileUploadResponse {
  success: boolean;
  row_count: number;
  column_count: number;
  source_columns: string[];
  error?: string;
}

export interface ExportOptions {
  format: 'excel' | 'csv';
  include_empty_columns?: boolean;
  supplier_view?: boolean;
  family_filter?: string;
}

// Google Sheets Integration
export interface GoogleSheetsConfig {
  client_id: string;
  client_secret: string;
  redirect_uri: string;
}

export interface GoogleSheetsAuth {
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
}

// Redis Database Types
export type RedisDatabase = 0 | 1 | 2 | 3 | 4;

export const REDIS_DB = {
  JOBS: 0 as RedisDatabase,        // Jobs & Data
  LLM_CACHE: 1 as RedisDatabase,   // LLM Cache
  SESSIONS: 2 as RedisDatabase,    // Sessions
  TASKS: 3 as RedisDatabase,       // Background Tasks
  GOOGLE_SHEETS: 4 as RedisDatabase // Google Sheets OAuth tokens
} as const;

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Import Wizard State
export interface ImportWizardState {
  currentStep: number;
  jobId: string | null;
  jobName: string | null;
  sourceColumns: string[];
  mappingDefinitions: MappingDefinition[];
  columnMappings: Record<string, string>;
  validationConfigs: Record<string, ValidationConfig>;
  selectedTargetColumns: string[];
  selectedTargetSystemType: "navision" | "akeneo" | "ebay" | "amazon" | null;
  oneToOneMappings: Record<string, string>;
  aiTransformMappings: Record<string, string>;
  isProcessing: boolean;
  error: string | null;
}

// Job Processing Types
export interface JobProcessingState {
  isProcessing: boolean;
  progress: ProgressUpdate | null;
  canCancel: boolean;
}

// Memory Management Types
export interface MemoryStats {
  used: number;
  total: number;
  percentage: number;
  redis_memory: number;
  active_connections: number;
}

// Enhanced Column Type System & Unified Configuration Management
export interface ConfigurationRow {
  Column_name: string;
  Type: "Navisionvorlage" | "Akeneo";
  Required: boolean;
  Default_Mapping: "Deactivated" | "String" | "AI" | null;
  /** @deprecated Use Custom_Mapping_Prompt_Template instead */
  Default_Mapping_Content: string | null;
  /** Custom prompt template that overrides the default template for this column */
  Custom_Mapping_Prompt_Template?: string | null;
  Prompt: string | null;
  Output_Validation_Column: string | null;
}

export interface LookupTable {
  Code: string;
  Beschreibung: string;
  [additional_columns: string]: any;
}

export interface ColumnTypeFilter {
  showERP: boolean;
  showPIM: boolean;
  showBoth: boolean;
}

export interface AkeneoColumnMetadata {
  attribute_type: string;
  localizable: boolean;
  scopable: boolean;
  group: string;
}

export interface UnifiedColumn {
  column_name: string;
  type: "Navisionvorlage" | "Akeneo";
  source: "config-only" | "api-only" | "api-with-config";
  required: boolean;
  default_mapping?: "Deactivated" | "String" | "AI" | null;
  default_mapping_content?: string | null;
  prompt?: string | null;
  output_validation_column?: string | null;
  api_metadata?: AkeneoColumnMetadata;
  has_configuration: boolean;
}

export interface UnifiedColumnStatistics {
  totalERPColumns: number;
  totalPIMApiColumns: number;
  totalPIMConfigured: number;
  totalPIMUnconfigured: number;
  configurationCoverage: number;
}

export interface UnifiedColumnManagement {
  erpColumns: ConfigurationRow[];
  pimApiColumns: AkeneoAttribute[];
  pimConfiguredColumns: ConfigurationRow[];
  unifiedColumns: UnifiedColumn[];
  statistics: UnifiedColumnStatistics;
  lastRefresh: string;
}

export interface GoogleSheetsData {
  mappingDefinitions: ConfigurationRow[];
  worksheetNames: string[];
  validationData: Record<string, LookupTable[]>;
  lastRefresh: string;
}

// Import Wizard Enhanced Types
export interface EnhancedImportWizardState {
  currentStep: 'upload' | 'mapping' | 'transformation' | 'export';
  jobId: string | null;
  configurationLoaded: boolean;
  targetColumns: ConfigurationRow[];
  sourceColumns: string[];
  columnMappings: Record<string, ColumnMapping>;
  columnTypeFilter: ColumnTypeFilter;
  sidebarCollapsed: boolean;
  unifiedConfig: UnifiedColumnManagement | null;
}

export interface ColumnMappingEnhanced {
  targetColumn: string;
  mappingMode: 'unmapped' | '1to1' | 'ai_transform' | 'string_overwrite' | 'deactivated';
  sourceColumn: string | null;
  staticValue: string;
  aiPrompt: string;
  validationColumn: string | null;
  applied: boolean;
  configurationBased?: boolean;
  aiSuggested?: boolean;
  confidence?: number;
}

// OpenRouter Model Management
export interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  pricing?: {
    prompt: string;
    completion: string;
  };
  context_length?: number;
  architecture?: string;
  provider?: 'openrouter';
}

export interface GroqModel {
  id: string;
  name: string;
  description?: string;
  object: string;
  created: number;
  owned_by: string;
  active: boolean;
  context_window?: number;
  provider?: 'groq';
}

export type LLMProvider = 'openrouter' | 'groq';

export interface LLMProviderConfig {
  provider: LLMProvider;
  models: OpenRouterModel[] | GroqModel[];
  defaultModel: string;
  enabledModels: string[];
}

export interface OpenRouterModelWithStatus extends OpenRouterModel {
  enabled: boolean;
  isDefault: boolean;
}

export interface GroqModelWithStatus extends GroqModel {
  enabled: boolean;
  isDefault: boolean;
}

export type LLMModelWithStatus = OpenRouterModelWithStatus | GroqModelWithStatus;

export interface ModelManagementState {
  availableModels: (OpenRouterModel | GroqModel)[];
  enabledModels: string[];
  defaultModel: string;
  loading: boolean;
  searchTerm: string;
  currentProvider: LLMProvider;
}

export interface GroqUsageStats {
  provider: 'groq';
  success: boolean;
  error?: string;
  rateLimits?: {
    requestsPerMinute?: number;
    requestsPerDay?: number;
    tokensPerMinute?: number;
    tokensPerDay?: number;
  };
  message?: string;
}

export interface OpenRouterUsageStats {
  provider: 'openrouter';
  success: boolean;
  error?: string;
  credits?: {
    totalCredits: number;
    totalUsage: number;
    remaining: number;
  };
  keyInfo?: {
    label: string;
    usage: number;
    limit: number | null;
    isFreeTier: boolean;
  };
}

export interface UsageStatsResponse {
  groq?: GroqUsageStats;
  openrouter?: OpenRouterUsageStats;
}

export interface OpenRouterStatus {
  configured: boolean;
  api_key_present: boolean;
  base_url: string;
  connectivity: boolean;
  models_count?: number;
  error?: string;
  provider: 'openrouter';
}

export interface GroqStatus {
  configured: boolean;
  api_key_present: boolean;
  base_url: string;
  connectivity: boolean;
  models_count?: number;
  error?: string;
  provider: 'groq';
}

export type LLMProviderStatus = OpenRouterStatus | GroqStatus; 