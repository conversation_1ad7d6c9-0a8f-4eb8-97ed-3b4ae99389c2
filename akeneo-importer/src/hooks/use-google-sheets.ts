import { useState, useEffect, useCallback } from 'react';
import { MappingDefinition } from '@/types';

interface GoogleSheetsStatus {
  authenticated: boolean;
  loading: boolean;
  error: string | null;
}

interface GoogleSheetsData {
  mappingDefinitions: MappingDefinition[];
  worksheetNames: string[];
  validationData: Record<string, string[]>;
}

export function useGoogleSheets() {
  const [status, setStatus] = useState<GoogleSheetsStatus>({
    authenticated: false,
    loading: true,
    error: null
  });

  const [data, setData] = useState<GoogleSheetsData>({
    mappingDefinitions: [],
    worksheetNames: [],
    validationData: {}
  });

  // Check authentication status
  const checkAuthStatus = useCallback(async () => {
    try {
      setStatus(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await fetch('/api/google-sheets/auth/status');
      const result = await response.json();
      
      setStatus(prev => ({
        ...prev,
        authenticated: result.authenticated,
        loading: false,
        error: result.success ? null : result.message
      }));
      
      return result.authenticated;
    } catch (error) {
      setStatus(prev => ({
        ...prev,
        authenticated: false,
        loading: false,
        error: `Failed to check auth status: ${error}`
      }));
      return false;
    }
  }, []);

  // Authenticate with Google Sheets
  const authenticate = useCallback(() => {
    const authWindow = window.open(
      '/api/google-sheets/auth/login',
      'google_auth',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    );

    return new Promise<boolean>((resolve) => {
      const handleMessage = (event: MessageEvent) => {
        if (event.data.type === 'google_auth_callback') {
          window.removeEventListener('message', handleMessage);
          authWindow?.close();
          
          if (event.data.success) {
            setStatus(prev => ({ ...prev, authenticated: true, error: null }));
            resolve(true);
          } else {
            setStatus(prev => ({ ...prev, error: event.data.message }));
            resolve(false);
          }
        }
      };

      window.addEventListener('message', handleMessage);
      
      // Check if popup was blocked or closed
      const checkClosed = setInterval(() => {
        if (authWindow?.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          resolve(false);
        }
      }, 1000);
    });
  }, []);

  // Logout from Google Sheets
  const logout = useCallback(async () => {
    try {
      const response = await fetch('/api/google-sheets/auth/logout', {
        method: 'POST'
      });
      
      const result = await response.json();
      
      if (result.success) {
        setStatus(prev => ({ ...prev, authenticated: false }));
        setData({
          mappingDefinitions: [],
          worksheetNames: [],
          validationData: {}
        });
      }
      
      return result.success;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    }
  }, []);

  // Load mapping definitions
  const loadMappingDefinitions = useCallback(async (useCache = true) => {
    try {
      const response = await fetch(`/api/google-sheets/mapping-definitions?use_cache=${useCache}`);
      
      // Check if response is ok
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType?.includes('application/json')) {
        const text = await response.text();
        throw new Error(`Expected JSON response, got: ${text.substring(0, 100)}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        setData(prev => ({ ...prev, mappingDefinitions: result.data }));
        return result.data;
      } else {
        throw new Error(result.message || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error loading mapping definitions:', error);
      setStatus(prev => ({ ...prev, error: `Failed to load mapping definitions: ${error}` }));
      return [];
    }
  }, []);

  // Load worksheet names
  const loadWorksheetNames = useCallback(async () => {
    try {
      const response = await fetch('/api/google-sheets/worksheets');
      
      // Check if response is ok
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType?.includes('application/json')) {
        const text = await response.text();
        throw new Error(`Expected JSON response, got: ${text.substring(0, 100)}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        setData(prev => ({ ...prev, worksheetNames: result.data }));
        return result.data;
      } else {
        throw new Error(result.message || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error loading worksheet names:', error);
      setStatus(prev => ({ ...prev, error: `Failed to load worksheet names: ${error}` }));
      return [];
    }
  }, []);

  // Load validation data for a specific worksheet/column
  const loadValidationData = useCallback(async (worksheetName: string, columnName: string, useCache = true) => {
    try {
      const response = await fetch(
        `/api/google-sheets/validation-data?worksheet=${encodeURIComponent(worksheetName)}&column=${encodeURIComponent(columnName)}&use_cache=${useCache}`
      );
      const result = await response.json();
      
      if (result.success) {
        const key = `${worksheetName}:${columnName}`;
        setData(prev => ({
          ...prev,
          validationData: { ...prev.validationData, [key]: result.data }
        }));
        return result.data;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error(`Error loading validation data for ${worksheetName}:${columnName}:`, error);
      return [];
    }
  }, []);

  // Load all validation data for mapping definitions
  const loadAllValidationData = useCallback(async (mappingDefinitions: MappingDefinition[]) => {
    const validationPromises = mappingDefinitions
      .filter(def => def.validation_config?.worksheet_name && def.validation_config?.column_name)
      .map(def => 
        loadValidationData(
          def.validation_config!.worksheet_name!,
          def.validation_config!.column_name!
        )
      );

    await Promise.allSettled(validationPromises);
  }, [loadValidationData]);

  // Refresh all cached data
  const refreshCache = useCallback(async () => {
    try {
      setStatus(prev => ({ ...prev, loading: true }));
      
      const response = await fetch('/api/google-sheets/refresh-cache', {
        method: 'POST'
      });
      const result = await response.json();
      
      if (result.success) {
        // Reload all data after cache refresh
        await loadMappingDefinitions(false);
        await loadWorksheetNames();
      }
      
      setStatus(prev => ({ ...prev, loading: false }));
      return result;
    } catch (error) {
      console.error('Error refreshing cache:', error);
      setStatus(prev => ({ 
        ...prev, 
        loading: false, 
        error: `Failed to refresh cache: ${error}` 
      }));
      return { success: false, message: `Failed to refresh cache: ${error}` };
    }
  }, [loadMappingDefinitions, loadWorksheetNames]);

  // Initialize on mount
  useEffect(() => {
    checkAuthStatus().then(authenticated => {
      if (authenticated) {
        loadMappingDefinitions().then(mappingDefs => {
          loadWorksheetNames();
          if (mappingDefs.length > 0) {
            loadAllValidationData(mappingDefs);
          }
        });
      }
    });
  }, [checkAuthStatus, loadMappingDefinitions, loadWorksheetNames, loadAllValidationData]);

  return {
    // Status
    ...status,
    
    // Data
    ...data,
    
    // Actions
    authenticate,
    logout,
    checkAuthStatus,
    loadMappingDefinitions,
    loadWorksheetNames,
    loadValidationData,
    loadAllValidationData,
    refreshCache,
    
    // Utility
    getValidationData: (worksheetName: string, columnName: string) => {
      const key = `${worksheetName}:${columnName}`;
      return data.validationData[key] || [];
    }
  };
}