import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface ConfiguredColumn {
  columnName: string;
  promptTemplate: string;
  selectedModel: string | null;
  isConfigured: boolean;
  isSaved: boolean;
}

export interface ColumnConfiguration {
  column_name: string;
  type: string;
  prompt: string | null;
  required: boolean;
  validation_data?: string[] | null;
  output_validation_column?: string | null;
  default_mapping?: string | null;
  default_mapping_content?: string | null;
  description?: string | null;
}

export interface LLMResponse {
  raw: string;
  extracted: string;
  fromCache?: boolean;
  cacheBypassed?: boolean;
  cacheStats?: any;
  structured_output?: boolean;
  reasoning?: string;
  answer?: string;
  provider?: 'openrouter' | 'groq';
}

export interface UseAITransformColumnReturn {
  // Column state
  configuredColumns: Record<string, ConfiguredColumn>;
  currentColumn: string | null;
  currentColumnIndex: number;
  totalColumns: number;
  progressPercentage: number;
  allColumnsConfigured: boolean;
  
  // Current column details
  columnConfiguration: ColumnConfiguration | null;
  currentPromptTemplate: string;
  currentColumnModel: string | null;
  
  // Testing state
  isTestingLLM: boolean;
  llmResponse: LLMResponse | null;
  renderedPrompt: string;
  isPromptRendered: boolean;
  
  // Loading states
  loadingConfiguration: boolean;
  isSavingTemplate: boolean;
  
  // Actions
  setCurrentColumnIndex: (index: number) => void;
  setCurrentPromptTemplate: (template: string) => void;
  setCurrentColumnModel: (model: string | null) => void;
  saveCurrentTemplate: () => Promise<void>;
  saveAllTemplates: () => Promise<void>;
  clearAllMappings: () => Promise<void>;
  testPromptWithLLM: (rowData: any, rowIndex: number) => Promise<void>;
  renderPromptWithPlaceholders: (rowData: any, rowIndex: number, worksheetData?: any[], jobNotes?: string) => Promise<void>;
  goToNextColumn: () => void;
  goToPreviousColumn: () => void;
}

export function useAITransformColumn(
  aiTransformColumns: string[],
  jobId: string | null,
  selectedLlmModel: string | null
): UseAITransformColumnReturn {
  // Core state
  const [configuredColumns, setConfiguredColumns] = useState<Record<string, ConfiguredColumn>>({});
  const [currentColumnIndex, setCurrentColumnIndex] = useState(0);
  const [columnConfiguration, setColumnConfiguration] = useState<ColumnConfiguration | null>(null);
  const [currentPromptTemplate, setCurrentPromptTemplate] = useState('');
  const [currentColumnModel, setCurrentColumnModel] = useState<string | null>(null);
  
  // Testing state
  const [isTestingLLM, setIsTestingLLM] = useState(false);
  const [llmResponse, setLlmResponse] = useState<LLMResponse | null>(null);
  const [renderedPrompt, setRenderedPrompt] = useState('');
  const [isPromptRendered, setIsPromptRendered] = useState(false);
  
  // Loading states
  const [loadingConfiguration, setLoadingConfiguration] = useState(false);
  const [isSavingTemplate, setIsSavingTemplate] = useState(false);
  
  // Computed values
  const currentColumn = aiTransformColumns[currentColumnIndex] || null;
  const totalColumns = aiTransformColumns.length;
  const configuredCount = Object.values(configuredColumns).filter(col => col.isConfigured && col.isSaved).length;
  const progressPercentage = totalColumns > 0 ? (configuredCount / totalColumns) * 100 : 0;
  const allColumnsConfigured = configuredCount === totalColumns && totalColumns > 0;

  // Initialize configured columns tracking
  useEffect(() => {
    const loadSavedState = async () => {
      const initialConfigured: Record<string, ConfiguredColumn> = {};
      
      try {
        if (jobId) {
          console.log('📖 Loading AI transform state from Redis for job:', jobId);
          const response = await fetch(`/api/import/ai-transform-state?job_id=${jobId}`);
          if (response.ok) {
            const result = await response.json();
            if (result.success && result.data.columnStates && Object.keys(result.data.columnStates).length > 0) {
              console.log('✅ AI Transform: Restored state from Redis');
              setConfiguredColumns(result.data.columnStates);
              return;
            }
          }
        }
      } catch (error) {
        console.warn('Failed to load saved AI transform state:', error);
      }
      
      // Initialize with defaults
      aiTransformColumns.forEach(col => {
        initialConfigured[col] = {
          columnName: col,
          promptTemplate: '',
          selectedModel: null,
          isConfigured: false,
          isSaved: false
        };
      });
      
      setConfiguredColumns(initialConfigured);
      console.log('AI Transform: Initialized', aiTransformColumns.length, 'columns for configuration');
    };
    
    if (aiTransformColumns.length > 0) {
      loadSavedState();
    }
  }, [aiTransformColumns, jobId]);

  // Save state to Redis whenever it changes
  useEffect(() => {
    const saveStateToRedis = async () => {
      if (!jobId) return;
      
      const hasConfiguredColumns = Object.values(configuredColumns).some(col => col.isConfigured || col.isSaved);
      if (!hasConfiguredColumns && Object.keys(configuredColumns).length > 0) {
        return;
      }
      
      try {
        const response = await fetch('/api/import/ai-transform-state', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            job_id: jobId,
            column_states: configuredColumns
          }),
        });

        if (response.ok) {
          console.log('✅ AI transform state saved to Redis');
        }
      } catch (error) {
        console.warn('Failed to save AI transform state to Redis:', error);
      }
    };

    const timeoutId = setTimeout(saveStateToRedis, 500);
    return () => clearTimeout(timeoutId);
  }, [configuredColumns, jobId]);

  // Load column configuration when current column changes
  useEffect(() => {
    const loadColumnConfiguration = async () => {
      if (!currentColumn) return;
      
      setLoadingConfiguration(true);
      
      try {
        const response = await fetch('/api/unified-config');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        
        const result = await response.json();
        if (!result.success) throw new Error(result.error);
        
        const columnConfig = result.data.columns.find((col: any) => col.column_name === currentColumn);
        
        if (columnConfig) {
          setColumnConfiguration(columnConfig);
          
          // Set initial prompt template if not already configured
          if (!configuredColumns[currentColumn]?.promptTemplate) {
            if (columnConfig.default_mapping_content?.trim()) {
              console.log('✅ Using custom mapping prompt template for column:', currentColumn);
              setCurrentPromptTemplate(columnConfig.default_mapping_content);
            } else {
              // Load global template
              const globalTemplate = await getDefaultPrompt();
              if (globalTemplate) {
                setCurrentPromptTemplate(globalTemplate);
              }
            }
          }
        }
      } catch (error) {
        console.error('Failed to load column configuration:', error);
        toast.error('Failed to load column configuration');
      } finally {
        setLoadingConfiguration(false);
      }
    };

    loadColumnConfiguration();
  }, [currentColumn, configuredColumns]);

  // Sync current column's model selection when switching columns
  useEffect(() => {
    if (currentColumn && configuredColumns[currentColumn]) {
      setCurrentColumnModel(configuredColumns[currentColumn].selectedModel || selectedLlmModel);
    } else {
      setCurrentColumnModel(selectedLlmModel);
    }
  }, [currentColumn, configuredColumns, selectedLlmModel]);

  // Load current column's saved template when column changes
  useEffect(() => {
    if (currentColumn && configuredColumns[currentColumn]) {
      const savedTemplate = configuredColumns[currentColumn].promptTemplate;
      if (savedTemplate !== currentPromptTemplate) {
        setCurrentPromptTemplate(savedTemplate);
      }
      setIsPromptRendered(false);
      setLlmResponse(null);
    }
  }, [currentColumn]);

  const getDefaultPrompt = async (): Promise<string> => {
    try {
      const response = await fetch('/api/settings/prompt/default');
      if (response.ok) {
        const responseText = await response.text();
        if (!responseText || responseText === 'undefined') {
          return getFallbackPrompt();
        }
        const result = JSON.parse(responseText);
        if (result.success) {
          return result.default_prompt || getFallbackPrompt();
        }
      }
    } catch (error) {
      console.error('Failed to fetch default prompt:', error);
    }
    return getFallbackPrompt();
  };

  const getFallbackPrompt = () => {
    return 'Transformation instructions: @description\n\nValidation data for reference: @prompt_additional_data\nThe validation column is: @output_validation_field\n\nPlease use the information in the notes: @notes\nCurrent row data: @row\n\nPlease provide your response in the expected format. Be precise and follow any constraints provided.';
  };

  const saveCurrentTemplate = useCallback(async () => {
    if (!currentColumn || !currentPromptTemplate.trim()) {
      toast.error('Please enter a prompt template before saving');
      return;
    }

    setIsSavingTemplate(true);
    
    try {
      const response = await fetch('/api/unified-config/prompts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          column_name: currentColumn,
          prompt_template: currentPromptTemplate,
          selected_model: currentColumnModel,
          job_id: jobId
        }),
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const result = await response.json();
      if (!result.success) throw new Error(result.error);

      // Update state
      const newConfiguredColumns = {
        ...configuredColumns,
        [currentColumn]: {
          ...configuredColumns[currentColumn],
          promptTemplate: currentPromptTemplate,
          selectedModel: currentColumnModel,
          isConfigured: true,
          isSaved: true
        }
      };
      setConfiguredColumns(newConfiguredColumns);

      toast.success(`Template saved for column: ${currentColumn}`);
    } catch (error) {
      console.error('Failed to save template:', error);
      toast.error('Failed to save template');
    } finally {
      setIsSavingTemplate(false);
    }
  }, [currentColumn, currentPromptTemplate, currentColumnModel, configuredColumns, jobId]);

  const saveAllTemplates = useCallback(async () => {
    setIsSavingTemplate(true);
    let savedCount = 0;
    let errorCount = 0;

    try {
      const configResponse = await fetch('/api/unified-config');
      if (!configResponse.ok) throw new Error('Failed to load unified configuration');
      
      const configResult = await configResponse.json();
      if (!configResult.success) throw new Error(configResult.error);

      const globalTemplate = await getDefaultPrompt();
      if (!globalTemplate) throw new Error('Failed to load default global template');

      for (const column of aiTransformColumns) {
        const columnConfig = configuredColumns[column];
        const unifiedColumnConfig = configResult.data.columns.find((col: any) => col.column_name === column);
        
        let templateToSave: string;
        if (column === currentColumn) {
          templateToSave = currentPromptTemplate;
        } else if (columnConfig?.promptTemplate?.trim()) {
          templateToSave = columnConfig.promptTemplate;
        } else if (unifiedColumnConfig?.default_mapping_content?.trim()) {
          templateToSave = unifiedColumnConfig.default_mapping_content;
        } else {
          templateToSave = globalTemplate;
        }

        if (!templateToSave?.trim()) {
          errorCount++;
          continue;
        }

        try {
          const response = await fetch('/api/unified-config/prompts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              column_name: column,
              prompt_template: templateToSave,
              selected_model: column === currentColumn ? currentColumnModel : (configuredColumns[column]?.selectedModel || selectedLlmModel),
              job_id: jobId
            }),
          });

          if (!response.ok) throw new Error(`HTTP ${response.status}`);
          const result = await response.json();
          if (!result.success) throw new Error(result.error);

          savedCount++;
        } catch (error) {
          console.error(`Failed to save template for ${column}:`, error);
          errorCount++;
        }
      }

      // Update all configured columns state
      const updatedColumns = { ...configuredColumns };
      aiTransformColumns.forEach(column => {
        const unifiedColumnConfig = configResult.data.columns.find((col: any) => col.column_name === column);
        let finalTemplate: string;
        
        if (column === currentColumn) {
          finalTemplate = currentPromptTemplate;
        } else if (updatedColumns[column]?.promptTemplate?.trim()) {
          finalTemplate = updatedColumns[column].promptTemplate;
        } else if (unifiedColumnConfig?.default_mapping_content?.trim()) {
          finalTemplate = unifiedColumnConfig.default_mapping_content;
        } else {
          finalTemplate = globalTemplate;
        }
        
        updatedColumns[column] = {
          columnName: column,
          promptTemplate: finalTemplate || '',
          selectedModel: updatedColumns[column]?.selectedModel || selectedLlmModel || null,
          isConfigured: true,
          isSaved: true
        };
      });
      
      setConfiguredColumns(updatedColumns);

      if (errorCount > 0) {
        toast.warning(`Saved ${savedCount} templates, ${errorCount} failed`);
      } else {
        toast.success(`All ${savedCount} templates saved successfully`);
      }
    } catch (error) {
      console.error('Failed to save all templates:', error);
      toast.error('Failed to save all templates');
    } finally {
      setIsSavingTemplate(false);
    }
  }, [aiTransformColumns, configuredColumns, currentColumn, currentPromptTemplate, currentColumnModel, selectedLlmModel, jobId]);

  const clearAllMappings = useCallback(async () => {
    try {
      const clearedColumns: Record<string, ConfiguredColumn> = {};
      aiTransformColumns.forEach(col => {
        clearedColumns[col] = {
          columnName: col,
          promptTemplate: '',
          selectedModel: null,
          isConfigured: false,
          isSaved: false
        };
      });
      
      setConfiguredColumns(clearedColumns);
      
      if (currentColumn) {
        setCurrentPromptTemplate('');
        setIsPromptRendered(false);
        setLlmResponse(null);
      }
      
      if (jobId) {
        await fetch('/api/import/ai-transform-state', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            job_id: jobId,
            column_states: clearedColumns
          }),
        });
      }
      
      toast.success('All column mappings cleared');
    } catch (error) {
      console.error('Failed to clear mappings:', error);
      toast.error('Failed to clear mappings');
    }
  }, [aiTransformColumns, currentColumn, jobId]);

  const renderPromptWithPlaceholders = useCallback(async (
    currentRow: any, 
    rowIndex: number, 
    worksheetData: any[] = [], 
    jobNotes: string = ''
  ) => {
    if (!currentPromptTemplate) {
      toast.error('No prompt template available');
      return;
    }

    try {
      let renderedText = currentPromptTemplate;
      
      const escapeHtml = (text: string) => {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
      };
      
      const wrapPlaceholderValue = (value: string) => {
        const escapedValue = escapeHtml(value);
        return `<span style="color: #6b7280; font-style: italic;">${escapedValue}</span>`;
      };
      
      // Replace placeholders
      const validationData = formatAdditionalDataAsMarkdown(worksheetData);
      renderedText = renderedText.replace(/@prompt_additional_data/g, wrapPlaceholderValue(validationData));
      
      const outputValidationFieldValue = columnConfiguration?.output_validation_column || 'No validation field specified';
      renderedText = renderedText.replace(/@output_validation_field/g, wrapPlaceholderValue(outputValidationFieldValue));
      renderedText = renderedText.replace(/@prompt_additional_data_column/g, wrapPlaceholderValue(outputValidationFieldValue));
      
      const notesValue = jobNotes || 'No job notes specified';
      renderedText = renderedText.replace(/@notes/g, wrapPlaceholderValue(notesValue));
      
      const rowValue = JSON.stringify(currentRow, null, 2);
      renderedText = renderedText.replace(/@row/g, wrapPlaceholderValue(rowValue));
      
      const descriptionValue = columnConfiguration?.description || columnConfiguration?.prompt || 'No description specified';
      renderedText = renderedText.replace(/@description/g, wrapPlaceholderValue(descriptionValue));
      renderedText = renderedText.replace(/@prompt/g, wrapPlaceholderValue(descriptionValue));
      
      const taskDescriptionValue = `Transform the source data to match the requirements for the "${currentColumn}" column`;
      renderedText = renderedText.replace(/@taskDescription/g, wrapPlaceholderValue(taskDescriptionValue));
      
      // Replace dynamic column placeholders
      Object.entries(currentRow || {}).forEach(([column, value]) => {
        const placeholder = `@${column}`;
        const valueStr = String(value || '');
        if (renderedText.includes(placeholder)) {
          renderedText = renderedText.replace(new RegExp(placeholder, 'g'), wrapPlaceholderValue(valueStr));
        }
      });

      setRenderedPrompt(renderedText);
      setIsPromptRendered(true);
      toast.success('Prompt rendered with current row data');
      
    } catch (error) {
      console.error('Failed to render prompt:', error);
      toast.error('Failed to render prompt with placeholders');
    }
  }, [currentPromptTemplate, columnConfiguration, currentColumn]);

  const formatAdditionalDataAsMarkdown = (data: any[]): string => {
    if (!data || data.length === 0) {
      return 'No additional data available';
    }
    
    const columns = Object.keys(data[0]);
    if (columns.length === 0) {
      return 'No additional data available';
    }
    
    let markdown = '| ' + columns.join(' | ') + ' |\n';
    markdown += '|' + columns.map(() => '---').join('|') + '|\n';
    
    for (const row of data) {
      const values = columns.map(col => {
        const value = row[col];
        return value ? String(value).replace(/\|/g, '\\|') : '';
      });
      markdown += '| ' + values.join(' | ') + ' |\n';
    }
    
    return markdown;
  };

  const testPromptWithLLM = useCallback(async (currentRow: any, rowIndex: number) => {
    if (!currentPromptTemplate) {
      toast.error('No prompt template available');
      return;
    }

    if (isTestingLLM) return;
    setIsTestingLLM(true);
    setLlmResponse(null);

    try {
      console.log('▶️ Sending prompt to LLM for column:', currentColumn);
      console.log('💿 With model:', currentColumnModel || 'default');

      const requestBody = {
        job_id: jobId,
        column_name: currentColumn,
        row_index: rowIndex,
        model_id: currentColumnModel || selectedLlmModel,
        bypass_cache: true,
        worksheet_data: !jobId ? [currentRow] : [],
        column_configuration: columnConfiguration
      };

      const response = await fetch('/api/import/ai-transform', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const result = await response.json();
      if (!result.success) throw new Error(result.error);

      setLlmResponse({
        raw: JSON.stringify(result, null, 2),
        extracted: result.result || result.transformed_value || 'No transformed value returned',
        fromCache: result.fromCache || false,
        cacheBypassed: result.cache_bypassed || false,
        cacheStats: result.cache_stats || null,
        structured_output: result.structured_output || false,
        reasoning: result.reasoning || '',
        answer: result.answer || result.result || result.transformed_value || '',
        provider: result.provider || 'openrouter'
      });

      const structuredMsg = result.structured_output ? ' with structured output' : '';
      const providerMsg = result.provider ? ` (${result.provider})` : '';
      
      if (result.cache_bypassed) {
        toast.success(`LLM transformation completed (cache bypassed)${structuredMsg}${providerMsg}`);
      } else if (result.fromCache) {
        toast.success(`LLM transformation completed (from cache)${structuredMsg}${providerMsg}`);
      } else {
        toast.success(`LLM transformation completed (fresh API call)${structuredMsg}${providerMsg}`);
      }
      
    } catch (error) {
      console.error('Failed to send prompt to LLM:', error);
      toast.error(`LLM test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTestingLLM(false);
    }
  }, [currentPromptTemplate, currentColumn, currentColumnModel, selectedLlmModel, jobId, columnConfiguration, isTestingLLM]);

  const goToNextColumn = useCallback(() => {
    if (currentColumnIndex < aiTransformColumns.length - 1) {
      setCurrentColumnIndex(currentColumnIndex + 1);
    }
  }, [currentColumnIndex, aiTransformColumns.length]);

  const goToPreviousColumn = useCallback(() => {
    if (currentColumnIndex > 0) {
      setCurrentColumnIndex(currentColumnIndex - 1);
    }
  }, [currentColumnIndex]);

  return {
    // Column state
    configuredColumns,
    currentColumn,
    currentColumnIndex,
    totalColumns,
    progressPercentage,
    allColumnsConfigured,
    
    // Current column details
    columnConfiguration,
    currentPromptTemplate,
    currentColumnModel,
    
    // Testing state
    isTestingLLM,
    llmResponse,
    renderedPrompt,
    isPromptRendered,
    
    // Loading states
    loadingConfiguration,
    isSavingTemplate,
    
    // Actions
    setCurrentColumnIndex,
    setCurrentPromptTemplate,
    setCurrentColumnModel,
    saveCurrentTemplate,
    saveAllTemplates,
    clearAllMappings,
    testPromptWithLLM,
    renderPromptWithPlaceholders,
    goToNextColumn,
    goToPreviousColumn,
  };
} 