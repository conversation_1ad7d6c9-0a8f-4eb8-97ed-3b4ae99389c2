import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useImportWizardStore } from '@/stores/import-wizard-store';

export interface SourceDataState {
  sourceRows: any[];
  jobNotes: string;
  worksheetData: any[];
  availableWorksheets: string[];
}

export interface SourceDataLoadingState {
  loadingSourceData: boolean;
  loadingJobNotes: boolean;
  loadingWorksheet: boolean;
  loadingWorksheets: boolean;
}

export interface SourceDataErrorState {
  sourceDataError: string;
  jobNotesError: string;
  worksheetError: string;
}

export interface RowNavigationState {
  currentRowIndex: number;
  totalRows: number;
}

export interface UseSourceDataOptions {
  jobId: string | null;
  pageSize?: number;
  autoLoadJobNotes?: boolean;
  autoLoadWorksheets?: boolean;
}

export interface UseSourceDataReturn {
  // Data state
  data: SourceDataState;
  loading: SourceDataLoadingState;
  errors: SourceDataErrorState;
  navigation: RowNavigationState;
  
  // Current row helpers
  currentRow: any | null;
  hasNextRow: boolean;
  hasPreviousRow: boolean;
  
  // Actions
  loadSourceData: (page?: number, pageSize?: number) => Promise<void>;
  refreshSourceData: () => Promise<void>;
  loadJobNotes: () => Promise<void>;
  saveJobNotes: (notes: string) => Promise<void>;
  loadWorksheetData: (columnName?: string) => Promise<void>;
  loadAvailableWorksheets: () => Promise<void>;
  
  // Navigation
  setCurrentRowIndex: (index: number) => void;
  goToNextRow: () => void;
  goToPreviousRow: () => void;
  goToFirstRow: () => void;
  goToLastRow: () => void;
  goToSpecificRow: (rowNumber: number) => void;
  
  // Utilities
  clearErrors: () => void;
  formatWorksheetDataAsMarkdown: (data?: any[]) => string;
}

export function useSourceData(options: UseSourceDataOptions): UseSourceDataReturn {
  const { jobId, pageSize = 10, autoLoadJobNotes = true, autoLoadWorksheets = true } = options;
  
  // Data state
  const [data, setData] = useState<SourceDataState>({
    sourceRows: [],
    jobNotes: '',
    worksheetData: [],
    availableWorksheets: []
  });
  
  // Loading state
  const [loading, setLoading] = useState<SourceDataLoadingState>({
    loadingSourceData: false,
    loadingJobNotes: false,
    loadingWorksheet: false,
    loadingWorksheets: false
  });
  
  // Error state
  const [errors, setErrors] = useState<SourceDataErrorState>({
    sourceDataError: '',
    jobNotesError: '',
    worksheetError: ''
  });
  
  // Navigation state
  const [navigation, setNavigation] = useState<RowNavigationState>({
    currentRowIndex: 0,
    totalRows: 0
  });
  
  // Computed values
  const currentRow = data.sourceRows[navigation.currentRowIndex] || null;
  const hasNextRow = navigation.currentRowIndex < data.sourceRows.length - 1;
  const hasPreviousRow = navigation.currentRowIndex > 0;
  
  // Load source data with enhanced error handling
  const loadSourceData = useCallback(async (page: number = 1, customPageSize?: number) => {
    if (!jobId) {
      setErrors(prev => ({ ...prev, sourceDataError: 'No job ID available' }));
      return;
    }
    
    const effectivePageSize = customPageSize || pageSize;
    setLoading(prev => ({ ...prev, loadingSourceData: true }));
    setErrors(prev => ({ ...prev, sourceDataError: '' }));
    
    try {
      const url = `/api/import/grid-data?job_id=${jobId}&page=${page}&page_size=${effectivePageSize}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        // Handle specific error cases with user-friendly messages
        if (result.error?.includes('No source data available')) {
          setErrors(prev => ({ 
            ...prev, 
            sourceDataError: `No source data found for this job. ${result.details || ''}` 
          }));
          toast.error('No Source Data Available', {
            description: 'Please return to step 1 and re-upload your file.',
          });
        } else if (result.error?.includes('No column information available')) {
          setErrors(prev => ({ 
            ...prev, 
            sourceDataError: `Column information missing. ${result.details || ''}` 
          }));
          toast.error('Column Information Missing', {
            description: 'Please return to step 1 and re-upload your file.',
          });
        } else {
          setErrors(prev => ({ 
            ...prev, 
            sourceDataError: result.error || 'API returned success: false' 
          }));
          toast.error('Data Loading Error', {
            description: result.error || 'Unknown error occurred'
          });
        }
        return;
      }
      
      if (!result.data?.rows || result.data.rows.length === 0) {
        setErrors(prev => ({ 
          ...prev, 
          sourceDataError: 'No data rows available in the response' 
        }));
        toast.error('Empty Data Response', {
          description: 'The data source appears to be empty. Please re-upload your file.'
        });
        return;
      }
      
      // Update data state
      setData(prev => ({
        ...prev,
        sourceRows: result.data.rows
      }));
      
      setNavigation(prev => ({
        ...prev,
        totalRows: result.data.total_rows || result.data.rows.length
      }));
      
      // Also store in the wizard store for consistency
      useImportWizardStore.getState().setSourceData(result.data.rows);
      
      toast.success('Source data loaded successfully', {
        description: `${result.data.rows.length} rows loaded`
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setErrors(prev => ({ 
        ...prev, 
        sourceDataError: `Failed to load source data: ${errorMessage}` 
      }));
      toast.error('Failed to load source data', {
        description: 'Please check your connection and try again, or return to step 1 to re-upload.'
      });
    } finally {
      setLoading(prev => ({ ...prev, loadingSourceData: false }));
    }
  }, [jobId, pageSize]);
  
  // Refresh source data (reload current page)
  const refreshSourceData = useCallback(async () => {
    await loadSourceData(1, pageSize);
  }, [loadSourceData, pageSize]);
  
  // Load job notes
  const loadJobNotes = useCallback(async () => {
    if (!jobId) return;
    
    setLoading(prev => ({ ...prev, loadingJobNotes: true }));
    setErrors(prev => ({ ...prev, jobNotesError: '' }));
    
    try {
      const response = await fetch(`/api/jobs/${jobId}/notes`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setData(prev => ({ ...prev, jobNotes: result.notes || '' }));
        }
      }
    } catch (error) {
      console.warn('Could not load job notes:', error);
      setErrors(prev => ({ 
        ...prev, 
        jobNotesError: error instanceof Error ? error.message : 'Failed to load job notes' 
      }));
    } finally {
      setLoading(prev => ({ ...prev, loadingJobNotes: false }));
    }
  }, [jobId]);
  
  // Save job notes
  const saveJobNotes = useCallback(async (notes: string) => {
    if (!jobId) return;
    
    setLoading(prev => ({ ...prev, loadingJobNotes: true }));
    
    try {
      const response = await fetch(`/api/jobs/${jobId}/notes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notes }),
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setData(prev => ({ ...prev, jobNotes: notes }));
          toast.success('Job notes saved successfully');
        } else {
          throw new Error(result.message || 'Failed to save notes');
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setErrors(prev => ({ ...prev, jobNotesError: errorMessage }));
      toast.error('Failed to save job notes', { description: errorMessage });
    } finally {
      setLoading(prev => ({ ...prev, loadingJobNotes: false }));
    }
  }, [jobId]);
  
  // Load available worksheets
  const loadAvailableWorksheets = useCallback(async () => {
    setLoading(prev => ({ ...prev, loadingWorksheets: true }));
    
    try {
      const response = await fetch('/api/google-sheets/worksheets');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setData(prev => ({ ...prev, availableWorksheets: result.data || [] }));
        }
      }
    } catch (error) {
      console.warn('Could not load available worksheets:', error);
    } finally {
      setLoading(prev => ({ ...prev, loadingWorksheets: false }));
    }
  }, []);
  
  // Load worksheet data for a specific column
  const loadWorksheetData = useCallback(async (columnName?: string) => {
    if (!columnName) return;
    
    setLoading(prev => ({ ...prev, loadingWorksheet: true }));
    setErrors(prev => ({ ...prev, worksheetError: '' }));
    
    try {
      // First try to load validation data from mapping definitions
      const mappingResponse = await fetch(`/api/import/mapping-definitions?job_id=${jobId}`);
      if (mappingResponse.ok) {
        const mappingResult = await mappingResponse.json();
        if (mappingResult.success && mappingResult.data?.length > 0) {
          const columnData = mappingResult.data.find((col: any) => col.column_name === columnName);
          if (columnData?.validation_data) {
            setData(prev => ({ ...prev, worksheetData: columnData.validation_data }));
            return;
          }
        }
      }
      
      // If no validation data, try to load from Google Sheets worksheet
      if (data.availableWorksheets.includes(columnName)) {
        const response = await fetch(`/api/google-sheets/worksheets?name=${encodeURIComponent(columnName)}&use_cache=false`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success && result.data) {
          setData(prev => ({ ...prev, worksheetData: result.data }));
          toast.success(`Loaded additional data for ${columnName}`, {
            description: `Found ${result.data.length} rows in the ${columnName} worksheet`
          });
        } else {
          throw new Error(result.message || 'No data returned');
        }
      } else {
        // No additional worksheet found for this column
        setData(prev => ({ ...prev, worksheetData: [] }));
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setErrors(prev => ({ ...prev, worksheetError: errorMessage }));
      setData(prev => ({ ...prev, worksheetData: [] }));
      toast.error('Failed to load additional data', { description: errorMessage });
    } finally {
      setLoading(prev => ({ ...prev, loadingWorksheet: false }));
    }
  }, [data.availableWorksheets, jobId]);
  
  // Navigation functions
  const setCurrentRowIndex = useCallback((index: number) => {
    if (index >= 0 && index < data.sourceRows.length) {
      setNavigation(prev => ({ ...prev, currentRowIndex: index }));
    }
  }, [data.sourceRows.length]);
  
  const goToNextRow = useCallback(() => {
    if (hasNextRow) {
      setNavigation(prev => ({ ...prev, currentRowIndex: prev.currentRowIndex + 1 }));
    }
  }, [hasNextRow]);
  
  const goToPreviousRow = useCallback(() => {
    if (hasPreviousRow) {
      setNavigation(prev => ({ ...prev, currentRowIndex: prev.currentRowIndex - 1 }));
    }
  }, [hasPreviousRow]);
  
  const goToFirstRow = useCallback(() => {
    setNavigation(prev => ({ ...prev, currentRowIndex: 0 }));
  }, []);
  
  const goToLastRow = useCallback(() => {
    setNavigation(prev => ({ ...prev, currentRowIndex: Math.max(0, data.sourceRows.length - 1) }));
  }, [data.sourceRows.length]);
  
  const goToSpecificRow = useCallback((rowNumber: number) => {
    const index = Math.max(0, Math.min(rowNumber - 1, data.sourceRows.length - 1));
    setNavigation(prev => ({ ...prev, currentRowIndex: index }));
  }, [data.sourceRows.length]);
  
  // Utility functions
  const clearErrors = useCallback(() => {
    setErrors({
      sourceDataError: '',
      jobNotesError: '',
      worksheetError: ''
    });
  }, []);
  
  const formatWorksheetDataAsMarkdown = useCallback((worksheetData?: any[]): string => {
    const dataToFormat = worksheetData || data.worksheetData;
    
    if (!dataToFormat || dataToFormat.length === 0) {
      return 'No additional data available';
    }
    
    const columns = Object.keys(dataToFormat[0]);
    if (columns.length === 0) {
      return 'No additional data available';
    }
    
    // Create markdown table
    let markdown = '| ' + columns.join(' | ') + ' |\n';
    markdown += '|' + columns.map(() => '---').join('|') + '|\n';
    
    // Add all data rows
    for (const row of dataToFormat) {
      const values = columns.map(col => {
        const value = row[col];
        return value ? String(value).replace(/\|/g, '\\|') : ''; // Escape pipes in data
      });
      markdown += '| ' + values.join(' | ') + ' |\n';
    }
    
    return markdown;
  }, [data.worksheetData]);
  
  // Auto-load data when jobId changes
  useEffect(() => {
    if (jobId) {
      loadSourceData(1, pageSize);
      
      if (autoLoadJobNotes) {
        loadJobNotes();
      }
      
      if (autoLoadWorksheets) {
        loadAvailableWorksheets();
      }
    }
  }, [jobId, loadSourceData, loadJobNotes, loadAvailableWorksheets, autoLoadJobNotes, autoLoadWorksheets, pageSize]);
  
  return {
    // Data state
    data,
    loading,
    errors,
    navigation,
    
    // Current row helpers
    currentRow,
    hasNextRow,
    hasPreviousRow,
    
    // Actions
    loadSourceData,
    refreshSourceData,
    loadJobNotes,
    saveJobNotes,
    loadWorksheetData,
    loadAvailableWorksheets,
    
    // Navigation
    setCurrentRowIndex,
    goToNextRow,
    goToPreviousRow,
    goToFirstRow,
    goToLastRow,
    goToSpecificRow,
    
    // Utilities
    clearErrors,
    formatWorksheetDataAsMarkdown
  };
} 