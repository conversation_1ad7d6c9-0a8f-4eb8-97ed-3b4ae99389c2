import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  UnifiedColumn, 
  UnifiedColumnStatistics, 
  ColumnTypeFilter 
} from '@/types';
import { useConfigurationStore } from '@/stores/configuration-store';

interface UnifiedConfigData {
  columns: UnifiedColumn[];
  statistics: UnifiedColumnStatistics;
  connection_status: {
    connected: boolean;
    configured: boolean;
    attributes_count?: number;
    families_count?: number;
    categories_count?: number;
    last_sync?: string;
  };
  filter_applied: ColumnTypeFilter;
  timestamp: string;
}

interface UseUnifiedConfigReturn {
  // Data
  columns: UnifiedColumn[];
  statistics: UnifiedColumnStatistics;
  connectionStatus: {
    connected: boolean;
    configured: boolean;
    attributes_count?: number;
    families_count?: number;
    categories_count?: number;
    last_sync?: string;
    last_test?: string;
  };
  
  // State
  isLoading: boolean;
  error: string | null;
  filter: ColumnTypeFilter;
  authError: boolean;
  retryCount: number;
  maxRetries: number;
  
  // Actions
  setFilter: (filter: ColumnTypeFilter) => void;
  refreshConfig: () => Promise<void>;
  
  // Computed values
  erpColumns: UnifiedColumn[];
  pimColumns: UnifiedColumn[];
  configuredPimColumns: UnifiedColumn[];
  unconfiguredPimColumns: UnifiedColumn[];
  
  // Utilities
  getColumnsByType: (type: 'Navisionvorlage' | 'Akeneo') => UnifiedColumn[];
  getRequiredColumns: () => UnifiedColumn[];
  searchColumns: (query: string) => UnifiedColumn[];
}

export function useUnifiedConfig(): UseUnifiedConfigReturn {
  // Global configuration store
  const configStore = useConfigurationStore();
  
  // Local state
  const [data, setData] = useState<UnifiedConfigData | null>(null);
  const [filter, setFilter] = useState<ColumnTypeFilter>({
    showERP: true,
    showPIM: true,
    showBoth: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [authError, setAuthError] = useState<boolean>(false);

  // Use store state for loading and error
  const isLoading = configStore.isLoading;
  const error = configStore.error;
  
  const MAX_RETRIES = 3;

  // Load unified configuration data
  const loadConfig = useCallback(async (currentFilter: ColumnTypeFilter = filter, forceLoad: boolean = false) => {
    // Check retry limit
    if (!forceLoad && retryCount >= MAX_RETRIES) {
      console.warn(`Max retries (${MAX_RETRIES}) reached for loading unified configuration`);
      setAuthError(true);
      return;
    }

    try {
      configStore.setLoading(true);
      configStore.setError(null);
      setAuthError(false);

      // Increment retry count only for automatic retries, not forced loads
      if (!forceLoad) {
        setRetryCount(prev => prev + 1);
      }

      // First check Google Sheets auth status
      const authResponse = await fetch('/api/google-sheets/auth/status');
      const authData = await authResponse.json();
      
      if (!authData.authenticated && authData.configurationMissing) {
        setAuthError(true);
        throw new Error('Google Sheets authentication required');
      }

      const params = new URLSearchParams();
      if (currentFilter.showERP && !currentFilter.showBoth) {
        params.append('showERP', 'true');
      }
      if (currentFilter.showPIM && !currentFilter.showBoth) {
        params.append('showPIM', 'true');
      }
      if (currentFilter.showBoth) {
        params.append('showBoth', 'true');
      }

      const response = await fetch(`/api/unified-config?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to load unified configuration');
      }

      setData(result.data);
      
      // Reset retry count on successful load
      setRetryCount(0);
      setAuthError(false);
      
      // Update global store with data and connection status
      if (result.data) {
        configStore.setColumns(result.data.columns);
        configStore.setStatistics(result.data.statistics);
        configStore.setLastRefresh(new Date().toISOString());
        
        // Update connection status from API response
        if (result.data.connection_status) {
          configStore.setConnectionStatus(result.data.connection_status);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      
      // Check if this is an auth error
      if (errorMessage.includes('authentication') || errorMessage.includes('Google Sheets')) {
        setAuthError(true);
      }
      
      configStore.setError(errorMessage);
      console.error(`Failed to load unified configuration (attempt ${retryCount + 1}/${MAX_RETRIES}):`, err);
    } finally {
      configStore.setLoading(false);
    }
  }, [configStore, filter, retryCount, MAX_RETRIES]);

  // Refresh configuration (clear caches)
  const refreshConfig = useCallback(async () => {
    try {
      configStore.setLoading(true);
      configStore.setError(null);
      
      // Reset retry count and auth error for manual refresh
      setRetryCount(0);
      setAuthError(false);

      // First refresh the backend caches
      const refreshResponse = await fetch('/api/unified-config', {
        method: 'POST'
      });

      if (!refreshResponse.ok) {
        const errorData = await refreshResponse.json();
        throw new Error(errorData.message || 'Failed to refresh configuration');
      }

      const refreshResult = await refreshResponse.json();
      
      // Update connection status from refresh response
      if (refreshResult.connection_status) {
        configStore.setConnectionStatus(refreshResult.connection_status);
      }

      // Then reload the data (force load to bypass retry limit)
      await loadConfig(filter, true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh configuration';
      configStore.setError(errorMessage);
      console.error('Failed to refresh unified configuration:', err);
    }
  }, [loadConfig, filter, configStore]);

  // Update filter and reload data
  const updateFilter = useCallback((newFilter: ColumnTypeFilter) => {
    setFilter(newFilter);
    loadConfig(newFilter);
  }, [loadConfig]);

  // Load data on mount only
  useEffect(() => {
    // Only load if we don't have cached data
    if ((!configStore.columns || configStore.columns.length === 0) && !configStore.isLoading) {
      loadConfig(filter);
    }
  }, []); // Empty dependency array - load only on mount

  // Computed values - prefer store data if available, fallback to local data
  const columns = (configStore.columns && configStore.columns.length > 0) ? configStore.columns : (data?.columns || []);
  const statistics = (configStore.statistics && configStore.statistics.totalERPColumns > 0) ? configStore.statistics : (data?.statistics || {
    totalERPColumns: 0,
    totalPIMApiColumns: 0,
    totalPIMConfigured: 0,
    totalPIMUnconfigured: 0,
    configurationCoverage: 0
  });
  
  // Use global store connection status
  const connectionStatus = configStore.connectionStatus;

  // Derived data - memoize to prevent unnecessary recalculations
  const erpColumns = useMemo(() => columns.filter(col => col.type === 'Navisionvorlage'), [columns]);
  const pimColumns = useMemo(() => columns.filter(col => col.type === 'Akeneo'), [columns]);
  const configuredPimColumns = useMemo(() => pimColumns.filter(col => col.has_configuration), [pimColumns]);
  const unconfiguredPimColumns = useMemo(() => pimColumns.filter(col => !col.has_configuration), [pimColumns]);

  // Utility functions - memoize to prevent recreating on every render
  const getColumnsByType = useCallback((type: 'Navisionvorlage' | 'Akeneo') => {
    return columns.filter(col => col.type === type);
  }, [columns]);

  const getRequiredColumns = useCallback(() => {
    return columns.filter(col => col.required);
  }, [columns]);

  const searchColumns = useCallback((query: string) => {
    if (!query.trim()) return columns;
    
    const lowerQuery = query.toLowerCase();
    return columns.filter(col => 
      col.column_name.toLowerCase().includes(lowerQuery) ||
      (col.prompt && col.prompt.toLowerCase().includes(lowerQuery)) ||
      (col.default_mapping_content && col.default_mapping_content.toLowerCase().includes(lowerQuery))
    );
  }, [columns]);

  return {
    // Data
    columns,
    statistics,
    connectionStatus,
    
    // State
    isLoading,
    error,
    filter,
    authError,
    retryCount,
    maxRetries: MAX_RETRIES,
    
    // Actions
    setFilter: updateFilter,
    refreshConfig,
    
    // Computed values
    erpColumns,
    pimColumns,
    configuredPimColumns,
    unconfiguredPimColumns,
    
    // Utilities
    getColumnsByType,
    getRequiredColumns,
    searchColumns
  };
}

// Additional hook for Akeneo connection management - now uses global store
export function useAkeneoConnection() {
  const configStore = useConfigurationStore();

  return {
    testConnection: configStore.testConnection,
    refreshConnection: configStore.refreshConnection,
    isTestingConnection: configStore.isTestingConnection,
    connectionResult: {
      success: configStore.connectionStatus.connected,
      message: configStore.connectionStatus.connected ? 'Connection successful' : 'Connection failed',
      status: configStore.connectionStatus,
      configured: configStore.connectionStatus.configured,
      connected: configStore.connectionStatus.connected
    }
  };
}