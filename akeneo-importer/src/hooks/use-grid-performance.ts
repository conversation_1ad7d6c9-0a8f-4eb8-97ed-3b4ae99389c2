// @ts-nocheck
"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { GridApi } from "ag-grid-community";

interface PerformanceMetrics {
  // Memory metrics
  memoryUsage: number;
  memoryLimit: number;
  memoryPercentage: number;
  
  // Row metrics
  totalRows: number;
  loadedRows: number;
  visibleRows: number;
  
  // Performance metrics
  renderTime: number;
  scrollPerformance: number;
  filterTime: number;
  sortTime: number;
  
  // Cache metrics
  cacheHitRate: number;
  cacheSize: number;
  cacheMisses: number;
  
  // Network metrics
  apiCallCount: number;
  averageResponseTime: number;
  failedRequests: number;
  
  // Grid state
  columnsVisible: number;
  columnsTotal: number;
  filtersActive: number;
  sortsActive: number;
}

interface PerformanceEvent {
  type: 'render' | 'scroll' | 'filter' | 'sort' | 'api-call' | 'memory-warning';
  timestamp: number;
  duration?: number;
  details?: any;
}

interface GridPerformanceOptions {
  memoryLimit?: number; // MB
  enableMemoryMonitoring?: boolean;
  enableNetworkMonitoring?: boolean;
  enableEventTracking?: boolean;
  performanceCheckInterval?: number; // ms
  memoryWarningThreshold?: number; // percentage
}

const DEFAULT_OPTIONS: GridPerformanceOptions = {
  memoryLimit: 200, // 200MB
  enableMemoryMonitoring: true,
  enableNetworkMonitoring: true,
  enableEventTracking: true,
  performanceCheckInterval: 5000, // 5 seconds
  memoryWarningThreshold: 80 // 80%
};

export function useGridPerformance(
  gridApi: GridApi | null,
  options: GridPerformanceOptions = {}
) {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    memoryUsage: 0,
    memoryLimit: opts.memoryLimit!,
    memoryPercentage: 0,
    totalRows: 0,
    loadedRows: 0,
    visibleRows: 0,
    renderTime: 0,
    scrollPerformance: 0,
    filterTime: 0,
    sortTime: 0,
    cacheHitRate: 0,
    cacheSize: 0,
    cacheMisses: 0,
    apiCallCount: 0,
    averageResponseTime: 0,
    failedRequests: 0,
    columnsVisible: 0,
    columnsTotal: 0,
    filtersActive: 0,
    sortsActive: 0
  });
  
  const [events, setEvents] = useState<PerformanceEvent[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [warnings, setWarnings] = useState<string[]>([]);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const apiCallStartTimes = useRef<Map<string, number>>(new Map());
  const renderStartTime = useRef<number>(0);
  const lastScrollTime = useRef<number>(0);

  // Add performance event
  const addEvent = useCallback((event: Omit<PerformanceEvent, 'timestamp'>) => {
    if (!opts.enableEventTracking) return;
    
    const fullEvent: PerformanceEvent = {
      ...event,
      timestamp: Date.now()
    };
    
    setEvents(prev => {
      const newEvents = [...prev, fullEvent];
      // Keep only last 100 events
      return newEvents.slice(-100);
    });
  }, [opts.enableEventTracking]);

  // Get memory usage
  const getMemoryUsage = useCallback((): number => {
    if (!opts.enableMemoryMonitoring) return 0;
    
    // @ts-ignore - performance.memory is not in types but exists in Chrome
    const memory = (performance as any).memory;
    if (memory) {
      return memory.usedJSHeapSize / (1024 * 1024); // Convert to MB
    }
    return 0;
  }, [opts.enableMemoryMonitoring]);

  // Update grid metrics
  const updateGridMetrics = useCallback(() => {
    if (!gridApi) return;

    const startTime = performance.now();
    
    try {
      // Get grid state
      const displayedRowCount = gridApi.getDisplayedRowCount();
      const totalRowCount = gridApi.getInfiniteRowCount() || 0;
      const columns = gridApi.getColumns() || [];
      const visibleColumns = columns.filter(col => col.isVisible()).length;
      
      // Get filter and sort state
      const filterModel = gridApi.getFilterModel();
      const sortModel = gridApi.getSortModel();
      const activeFilters = Object.keys(filterModel).length;
      const activeSorts = sortModel.length;
      
      // Calculate visible rows (approximation)
      const gridContainer = document.querySelector('.ag-center-cols-viewport');
      const rowHeight = 28; // Default AG Grid row height
      const visibleRows = gridContainer 
        ? Math.ceil(gridContainer.clientHeight / rowHeight)
        : 0;

      // Memory usage
      const memoryUsage = getMemoryUsage();
      const memoryPercentage = (memoryUsage / opts.memoryLimit!) * 100;
      
      // Check for memory warning
      if (memoryPercentage > opts.memoryWarningThreshold!) {
        const warning = `Memory usage at ${memoryPercentage.toFixed(1)}% (${memoryUsage.toFixed(1)}MB)`;
        setWarnings(prev => {
          if (!prev.includes(warning)) {
            addEvent({ type: 'memory-warning', details: { memoryUsage, memoryPercentage } });
            return [...prev, warning];
          }
          return prev;
        });
      }

      const renderTime = performance.now() - startTime;

      setMetrics(prev => ({
        ...prev,
        memoryUsage,
        memoryPercentage,
        totalRows: totalRowCount,
        loadedRows: displayedRowCount,
        visibleRows,
        renderTime,
        columnsVisible: visibleColumns,
        columnsTotal: columns.length,
        filtersActive: activeFilters,
        sortsActive: activeSorts
      }));

    } catch (error) {
      console.error('Error updating grid metrics:', error);
    }
  }, [gridApi, getMemoryUsage, opts.memoryLimit, opts.memoryWarningThreshold, addEvent]);

  // Track API calls
  const trackApiCall = useCallback((url: string, method: string = 'GET') => {
    if (!opts.enableNetworkMonitoring) return;
    
    const callId = `${method}:${url}:${Date.now()}`;
    apiCallStartTimes.current.set(callId, Date.now());
    
    setMetrics(prev => ({
      ...prev,
      apiCallCount: prev.apiCallCount + 1
    }));

    return callId;
  }, [opts.enableNetworkMonitoring]);

  // Complete API call tracking
  const completeApiCall = useCallback((callId: string, success: boolean = true) => {
    if (!opts.enableNetworkMonitoring || !callId) return;
    
    const startTime = apiCallStartTimes.current.get(callId);
    if (!startTime) return;
    
    const duration = Date.now() - startTime;
    apiCallStartTimes.current.delete(callId);
    
    setMetrics(prev => {
      const totalCalls = prev.apiCallCount;
      const newAverageResponseTime = 
        ((prev.averageResponseTime * (totalCalls - 1)) + duration) / totalCalls;
      
      return {
        ...prev,
        averageResponseTime: newAverageResponseTime,
        failedRequests: success ? prev.failedRequests : prev.failedRequests + 1
      };
    });

    addEvent({
      type: 'api-call',
      duration,
      details: { success, callId }
    });
  }, [opts.enableNetworkMonitoring, addEvent]);

  // Track render performance
  const startRenderTracking = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  const endRenderTracking = useCallback(() => {
    if (renderStartTime.current === 0) return;
    
    const duration = performance.now() - renderStartTime.current;
    renderStartTime.current = 0;
    
    addEvent({
      type: 'render',
      duration,
      details: { renderTime: duration }
    });
  }, [addEvent]);

  // Track scroll performance
  const trackScrollPerformance = useCallback(() => {
    const now = performance.now();
    if (lastScrollTime.current > 0) {
      const scrollDelta = now - lastScrollTime.current;
      
      setMetrics(prev => ({
        ...prev,
        scrollPerformance: scrollDelta
      }));
      
      addEvent({
        type: 'scroll',
        duration: scrollDelta,
        details: { scrollPerformance: scrollDelta }
      });
    }
    lastScrollTime.current = now;
  }, [addEvent]);

  // Track filter performance
  const trackFilterPerformance = useCallback((startTime: number) => {
    const duration = performance.now() - startTime;
    
    setMetrics(prev => ({
      ...prev,
      filterTime: duration
    }));
    
    addEvent({
      type: 'filter',
      duration,
      details: { filterTime: duration }
    });
  }, [addEvent]);

  // Track sort performance
  const trackSortPerformance = useCallback((startTime: number) => {
    const duration = performance.now() - startTime;
    
    setMetrics(prev => ({
      ...prev,
      sortTime: duration
    }));
    
    addEvent({
      type: 'sort',
      duration,
      details: { sortTime: duration }
    });
  }, [addEvent]);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;
    
    setIsMonitoring(true);
    
    intervalRef.current = setInterval(() => {
      updateGridMetrics();
    }, opts.performanceCheckInterval);
  }, [isMonitoring, updateGridMetrics, opts.performanceCheckInterval]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;
    
    setIsMonitoring(false);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [isMonitoring]);

  // Reset metrics
  const resetMetrics = useCallback(() => {
    setMetrics({
      memoryUsage: 0,
      memoryLimit: opts.memoryLimit!,
      memoryPercentage: 0,
      totalRows: 0,
      loadedRows: 0,
      visibleRows: 0,
      renderTime: 0,
      scrollPerformance: 0,
      filterTime: 0,
      sortTime: 0,
      cacheHitRate: 0,
      cacheSize: 0,
      cacheMisses: 0,
      apiCallCount: 0,
      averageResponseTime: 0,
      failedRequests: 0,
      columnsVisible: 0,
      columnsTotal: 0,
      filtersActive: 0,
      sortsActive: 0
    });
    setEvents([]);
    setWarnings([]);
  }, [opts.memoryLimit]);

  // Clear warnings
  const clearWarnings = useCallback(() => {
    setWarnings([]);
  }, []);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const isPerformanceGood = 
      metrics.memoryPercentage < 70 &&
      metrics.renderTime < 100 &&
      metrics.scrollPerformance < 50 &&
      metrics.averageResponseTime < 1000;

    const performanceScore = Math.round(
      (100 - metrics.memoryPercentage) * 0.3 +
      (metrics.renderTime < 100 ? 100 : Math.max(0, 100 - metrics.renderTime / 10)) * 0.3 +
      (metrics.scrollPerformance < 50 ? 100 : Math.max(0, 100 - metrics.scrollPerformance / 5)) * 0.2 +
      (metrics.averageResponseTime < 1000 ? 100 : Math.max(0, 100 - metrics.averageResponseTime / 100)) * 0.2
    );

    return {
      isPerformanceGood,
      performanceScore,
      recommendations: getRecommendations()
    };
  }, [metrics]);

  // Get performance recommendations
  const getRecommendations = useCallback((): string[] => {
    const recommendations: string[] = [];
    
    if (metrics.memoryPercentage > 80) {
      recommendations.push("Consider reducing the number of visible columns or implementing pagination");
    }
    
    if (metrics.renderTime > 100) {
      recommendations.push("Grid rendering is slow. Consider disabling animations or reducing data complexity");
    }
    
    if (metrics.scrollPerformance > 50) {
      recommendations.push("Scrolling performance is poor. Enable row virtualization or reduce row height");
    }
    
    if (metrics.averageResponseTime > 2000) {
      recommendations.push("API responses are slow. Consider implementing caching or optimizing server queries");
    }
    
    if (metrics.columnsVisible > 20) {
      recommendations.push("Too many visible columns may impact performance. Hide unused columns");
    }
    
    if (metrics.filtersActive > 5) {
      recommendations.push("Multiple active filters may slow down the grid. Consider combining filters");
    }
    
    return recommendations;
  }, [metrics]);

  // Auto-start monitoring when grid API is available
  useEffect(() => {
    if (gridApi && !isMonitoring) {
      startMonitoring();
    }
  }, [gridApi, isMonitoring, startMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    metrics,
    events,
    warnings,
    isMonitoring,
    
    // Control functions
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    clearWarnings,
    
    // Tracking functions
    trackApiCall,
    completeApiCall,
    startRenderTracking,
    endRenderTracking,
    trackScrollPerformance,
    trackFilterPerformance,
    trackSortPerformance,
    
    // Analysis functions
    getPerformanceSummary,
    getRecommendations
  };
}