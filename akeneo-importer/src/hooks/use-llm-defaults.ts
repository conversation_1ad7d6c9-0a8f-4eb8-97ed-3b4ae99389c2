import { useState, useEffect } from 'react';

export interface LLMDefaults {
  temperature: number;
  model: string;
  provider: 'groq' | 'openrouter';
  loading: boolean;
  error: string | null;
}

export function useLLMDefaults() {
  const [defaults, setDefaults] = useState<LLMDefaults>({
    temperature: 1, // Safe fallback
    model: '',
    provider: 'groq',
    loading: true,
    error: null
  });

  const loadDefaults = async () => {
    try {
      setDefaults(prev => ({ ...prev, loading: true, error: null }));

      // Load current provider first
      const providerResponse = await fetch('/api/models/provider');
      if (!providerResponse.ok) {
        throw new Error('Failed to load provider');
      }
      const providerData = await providerResponse.json();
      const currentProvider = providerData.success ? providerData.provider : 'groq';

      // Load defaults for the current provider
      const [temperatureResponse, modelResponse] = await Promise.all([
        fetch(`/api/models/temperature?provider=${currentProvider}`),
        fetch(`/api/models/default?provider=${currentProvider}`)
      ]);

      if (!temperatureResponse.ok || !modelResponse.ok) {
        throw new Error('Failed to load defaults');
      }

      const [temperatureData, modelData] = await Promise.all([
        temperatureResponse.json(),
        modelResponse.json()
      ]);

      setDefaults({
        temperature: temperatureData.success ? temperatureData.default_temperature : 1,
        model: modelData.success ? modelData.default_model : '',
        provider: currentProvider,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('Error loading LLM defaults:', error);
      setDefaults(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load defaults'
      }));
    }
  };

  const updateTemperature = async (temperature: number) => {
    try {
      const response = await fetch('/api/models/temperature', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          temperature, 
          provider: defaults.provider 
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update temperature');
      }

      setDefaults(prev => ({ ...prev, temperature }));
      return { success: true };
    } catch (error) {
      console.error('Error updating temperature:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update temperature' 
      };
    }
  };

  useEffect(() => {
    loadDefaults();
  }, []);

  return {
    defaults,
    loadDefaults,
    updateTemperature,
    refresh: loadDefaults
  };
} 