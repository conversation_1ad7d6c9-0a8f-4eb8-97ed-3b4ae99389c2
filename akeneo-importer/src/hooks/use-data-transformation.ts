import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';

export interface TransformationJob {
  id: string;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'cancelled' | 'error';
  progress: number;
  processedRows: number;
  totalRows: number;
  processedCells: number;
  totalCells: number;
  currentRowCells: number;
  currentRowTotalCells: number;
  elapsedTime: number;
  estimatedTimeRemaining: number;
  startTime?: number;
  pauseTime?: number;
  error?: string;
}

export interface TransformedData {
  [key: string]: any;
  _rowIndex: number;
  _transformationStatus: 'pending' | 'processing' | 'completed' | 'error';
  _transformationError?: string;
}

export interface RowTransformationResult {
  rowIndex: number;
  sourceData: Record<string, any>;
  transformedData: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
}

export interface UseDataTransformationOptions {
  jobId: string | null;
  selectedTargetColumns: string[];
  columnMappingTypes: Record<string, string>;
  oneToOneMappings: Record<string, string>;
  aiTransformMappings: Record<string, string>;
  selectedLlmModel: string | null;
}

export interface UseDataTransformationReturn {
  // Transformation state
  transformationResults: Record<number, RowTransformationResult>;
  transformationJobId: string | null;
  isTransforming: boolean;
  isCreatingTasks: boolean;
  isCancelling: boolean;
  isCancelled: boolean;
  
  // Progress tracking
  transformationProgress: {
    totalRows: number;
    totalCells: number;
    processedRows: number;
    processedCells: number;
    currentRow: number;
    currentRowCells: number;
    currentRowTotalCells: number;
    startTime: number;
    elapsedTime: number;
    estimatedTimeRemaining: number;
    overallProgress: number;
  };
  
  // Column model mappings
  columnModelMappings: Record<string, string | null>;
  
  // Actions
  transformSelectedRows: (rowIndices: number[], sourceRows: any[], bulkOperationId?: string) => Promise<void>;
  transformAllRows: (sourceRows: any[], numRows?: number) => Promise<void>;
  cancelTransformation: () => Promise<void>;
  loadColumnModelMappings: () => Promise<void>;
  clearTransformationCache: (type: 'current' | 'all') => Promise<void>;
  exportData: (format: 'excel' | 'csv') => Promise<void>;
  
  // Utilities
  initializeTransformationResults: (sourceRows: any[], startIndex?: number) => void;
  formatDuration: (ms: number) => string;
}

export function useDataTransformation(options: UseDataTransformationOptions): UseDataTransformationReturn {
  const {
    jobId,
    selectedTargetColumns,
    columnMappingTypes,
    oneToOneMappings,
    aiTransformMappings,
    selectedLlmModel
  } = options;

  // Transformation state
  const [transformationResults, setTransformationResults] = useState<Record<number, RowTransformationResult>>({});
  const [transformationJobId, setTransformationJobId] = useState<string | null>(null);
  const [isTransforming, setIsTransforming] = useState(false);
  const [isCreatingTasks, setIsCreatingTasks] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  
  // Progress tracking
  const [transformationProgress, setTransformationProgress] = useState({
    totalRows: 0,
    totalCells: 0,
    processedRows: 0,
    processedCells: 0,
    currentRow: 0,
    currentRowCells: 0,
    currentRowTotalCells: 0,
    startTime: 0,
    elapsedTime: 0,
    estimatedTimeRemaining: 0,
    overallProgress: 0
  });
  
  // Column model mappings
  const [columnModelMappings, setColumnModelMappings] = useState<Record<string, string | null>>({});
  
  // Timer and cancellation refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const cancellationRef = useRef<{ cancelled: boolean }>({ cancelled: false });

  // Load column model mappings from AI transform state
  const loadColumnModelMappings = useCallback(async () => {
    if (!jobId) return;
    
    try {
      const response = await fetch(`/api/import/ai-transform-state?job_id=${jobId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.columnStates) {
          const mappings: Record<string, string | null> = {};
          Object.values(result.data.columnStates).forEach((columnState: any) => {
            if (columnState.columnName && columnState.selectedModel) {
              mappings[columnState.columnName] = columnState.selectedModel;
            }
          });
          setColumnModelMappings(mappings);
          console.log('✅ Loaded column model mappings:', mappings);
        }
      }
    } catch (error) {
      console.error('Failed to load column model mappings:', error);
    }
  }, [jobId]);

  // Initialize transformation results for current page rows
  const initializeTransformationResults = useCallback((sourceRows: any[], startIndex: number = 0) => {
    const results: Record<number, RowTransformationResult> = {};
    
    sourceRows.forEach((row, index) => {
      const globalRowIndex = startIndex + index;
      results[globalRowIndex] = {
        rowIndex: globalRowIndex,
        sourceData: row,
        transformedData: {},
        status: 'pending'
      };
    });
    
    setTransformationResults(prev => ({
      ...prev,
      ...results
    }));
  }, []);

  // Perform AI transformation for a single cell
  const performAITransformation = useCallback(async (
    sourceRow: any, 
    targetColumn: string, 
    prompt: string,
    rowIndex: number,
    bulkOperationId?: string
  ): Promise<string> => {
    // Get the saved model for this column, fallback to global model
    const columnModel = columnModelMappings[targetColumn] || selectedLlmModel;
    
    const response = await fetch('/api/import/ai-transform', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        job_id: jobId,
        column_name: targetColumn,
        prompt_template: prompt,
        row_index: rowIndex,
        model_id: columnModel,
        bulk_operation_id: bulkOperationId
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.cancelled) {
      throw new Error('Operation cancelled');
    }
    
    if (!result.success) {
      throw new Error(result.error || 'AI transformation failed');
    }
    
    return result.answer || result.result || result.transformed_value || 'No result';
  }, [columnModelMappings, selectedLlmModel, jobId]);

  // Transform specific rows
  const transformSelectedRows = useCallback(async (rowIndices: number[], sourceRows: any[], bulkOperationId?: string) => {
    if (!jobId || rowIndices.length === 0) return;
    
    const startTime = Date.now();
    const totalRows = rowIndices.length;
    const totalCells = totalRows * selectedTargetColumns.length;
    
    setIsTransforming(true);
    setTransformationProgress({
      totalRows,
      processedRows: 0,
      currentRow: 0,
      totalCells,
      processedCells: 0,
      currentRowCells: 0,
      currentRowTotalCells: selectedTargetColumns.length,
      startTime,
      elapsedTime: 0,
      estimatedTimeRemaining: 0,
      overallProgress: 0
    });
    
    // Start progress timer
    const progressTimer = setInterval(() => {
      setTransformationProgress(prev => ({
        ...prev,
        elapsedTime: Date.now() - startTime,
        estimatedTimeRemaining: prev.processedCells > 0 ? 
          Math.round(((Date.now() - startTime) / prev.processedCells) * (totalCells - prev.processedCells)) : 0
      }));
    }, 100);
    
    try {
      let processedCells = 0;
      
      for (let i = 0; i < rowIndices.length; i++) {
        const globalRowIndex = rowIndices[i];
        const sourceRow = sourceRows[globalRowIndex] || sourceRows[globalRowIndex % sourceRows.length];
        
        if (!sourceRow) continue;
        
        // Update current row progress
        setTransformationProgress(prev => ({
          ...prev,
          currentRow: i + 1,
          currentRowCells: 0,
          processedRows: i
        }));
        
        // Update row status to processing
        setTransformationResults(prev => ({
          ...prev,
          [globalRowIndex]: {
            ...prev[globalRowIndex],
            status: 'processing'
          }
        }));
        
        // Transform each target column for this row
        const transformedData: Record<string, any> = {};
        
        for (let j = 0; j < selectedTargetColumns.length; j++) {
          const targetColumn = selectedTargetColumns[j];
          const mappingType = columnMappingTypes[targetColumn];
          
          if (mappingType === 'one-to-one') {
            // Simple one-to-one mapping
            const sourceColumn = oneToOneMappings[targetColumn];
            if (sourceColumn && sourceRow[sourceColumn] !== undefined) {
              transformedData[targetColumn] = sourceRow[sourceColumn];
            }
          } else if (mappingType === 'ai-transform') {
            // AI transformation
            const prompt = aiTransformMappings[targetColumn];
            if (prompt) {
              try {
                const transformedValue = await performAITransformation(sourceRow, targetColumn, prompt, globalRowIndex, bulkOperationId);
                transformedData[targetColumn] = transformedValue;
              } catch (error) {
                console.error(`AI transformation failed for column ${targetColumn}:`, error);
                // Check if this was a cancellation
                if (error instanceof Error && error.message === 'Operation cancelled') {
                  clearInterval(progressTimer);
                  setIsTransforming(false);
                  toast.info('Transformation was cancelled');
                  return;
                }
                transformedData[targetColumn] = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
              }
            }
          } else {
            // Default value
            transformedData[targetColumn] = '';
          }
          
          // Update progress after each cell is processed
          processedCells++;
          const overallProgress = Math.round((processedCells / totalCells) * 100);
          setTransformationProgress(prev => ({
            ...prev,
            currentRowCells: j + 1,
            processedCells,
            overallProgress
          }));
          
          // Small delay to show progress (remove in production for faster processing)
          await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        // Update row with completed transformation
        setTransformationResults(prev => ({
          ...prev,
          [globalRowIndex]: {
            ...prev[globalRowIndex],
            transformedData,
            status: 'completed'
          }
        }));
        
        // Update final progress for this row
        setTransformationProgress(prev => ({
          ...prev,
          processedRows: i + 1,
          currentRowCells: selectedTargetColumns.length
        }));
      }
      
      toast.success(`Transformed ${rowIndices.length} rows successfully`);
      
    } catch (error) {
      console.error('Transformation failed:', error);
      toast.error('Transformation failed');
    } finally {
      clearInterval(progressTimer);
      setIsTransforming(false);
      setTransformationProgress({
        totalRows: 0,
        processedRows: 0,
        currentRow: 0,
        totalCells: 0,
        processedCells: 0,
        currentRowCells: 0,
        currentRowTotalCells: 0,
        startTime: 0,
        elapsedTime: 0,
        estimatedTimeRemaining: 0,
        overallProgress: 0
      });
    }
  }, [jobId, selectedTargetColumns, columnMappingTypes, oneToOneMappings, aiTransformMappings, performAITransformation]);

  // Transform all rows (bulk operation)
  const transformAllRows = useCallback(async (sourceRows: any[], numRows?: number) => {
    const rowsToTransform = numRows ? Math.min(numRows, sourceRows.length) : sourceRows.length;
    const rowIndices = Array.from({ length: rowsToTransform }, (_, i) => i);
    await transformSelectedRows(rowIndices, sourceRows);
  }, [transformSelectedRows]);

  // Cancel transformation
  const cancelTransformation = useCallback(async () => {
    setIsCancelling(true);
    cancellationRef.current.cancelled = true;
    
    try {
      if (transformationJobId) {
        const response = await fetch(`/api/import/transformation-job/${transformationJobId}/cancel`, {
          method: 'POST',
        });
        
        if (response.ok) {
          toast.success('Transformation cancelled successfully');
        }
      }
      
      setIsCancelled(true);
      setIsTransforming(false);
    } catch (error) {
      console.error('Failed to cancel transformation:', error);
      toast.error('Failed to cancel transformation');
    } finally {
      setIsCancelling(false);
    }
  }, [transformationJobId]);

  // Clear transformation cache
  const clearTransformationCache = useCallback(async (type: 'current' | 'all') => {
    try {
      const response = await fetch('/api/import/clear-transformation-cache', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_id: type === 'current' ? jobId : undefined,
          clear_all: type === 'all'
        }),
      });
      
      if (response.ok) {
        const result = await response.json();
        toast.success(`Cache cleared: ${result.cleared_count || 0} entries removed`);
      }
    } catch (error) {
      console.error('Failed to clear cache:', error);
      toast.error('Failed to clear transformation cache');
    }
  }, [jobId]);

  // Export data
  const exportData = useCallback(async (format: 'excel' | 'csv') => {
    if (!jobId) {
      toast.error('No job ID available for export');
      return;
    }
    
    try {
      const response = await fetch(`/api/jobs/${jobId}/export-${format}`, {
        method: 'POST',
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `export-${jobId}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success(`${format.toUpperCase()} export completed`);
      } else {
        throw new Error(`Export failed: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Export failed:', error);
      toast.error(`Failed to export ${format.toUpperCase()}`);
    }
  }, [jobId]);

  // Format duration utility
  const formatDuration = useCallback((ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  }, []);

  // Load column model mappings on mount
  useEffect(() => {
    loadColumnModelMappings();
  }, [loadColumnModelMappings]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return {
    // Transformation state
    transformationResults,
    transformationJobId,
    isTransforming,
    isCreatingTasks,
    isCancelling,
    isCancelled,
    
    // Progress tracking
    transformationProgress,
    
    // Column model mappings
    columnModelMappings,
    
    // Actions
    transformSelectedRows,
    transformAllRows,
    cancelTransformation,
    loadColumnModelMappings,
    clearTransformationCache,
    exportData,
    
    // Utilities
    initializeTransformationResults,
    formatDuration,
  };
} 