import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { useImportWizardStore } from '@/stores/import-wizard-store';

export interface RowTransformationResult {
  rowIndex: number;
  sourceData: Record<string, any>;
  transformedData: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
}

export interface TransformationProgress {
  totalRows: number;
  totalCells: number;
  processedRows: number;
  processedCells: number;
  currentRow: number;
  currentRowCells: number;
  currentRowTotalCells: number;
  startTime: number;
  elapsedTime: number;
  estimatedTimeRemaining: number;
  overallProgress: number;
}

export interface TransformationJob {
  id: string;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'cancelled' | 'error';
  progress: number;
  processedRows: number;
  totalRows: number;
  processedCells: number;
  totalCells: number;
  currentRowCells: number;
  currentRowTotalCells: number;
  elapsedTime: number;
  estimatedTimeRemaining: number;
  startTime?: number;
  pauseTime?: number;
  error?: string;
}

export interface UseTransformationStateOptions {
  jobId: string | null;
  selectedTargetColumns: string[];
  columnMappingTypes: Record<string, string>;
  oneToOneMappings: Record<string, string>;
  aiTransformMappings: Record<string, string>;
  selectedLlmModel: string | null;
}

export interface UseTransformationStateReturn {
  // Column mappings
  columnModelMappings: Record<string, string | null>;
  
  // Transformation state
  transformationResults: Record<number, RowTransformationResult>;
  transformationProgress: TransformationProgress;
  transformationJobId: string | null;
  
  // Status flags
  isTransforming: boolean;
  isCreatingTasks: boolean;
  isCancelling: boolean;
  isCancelled: boolean;
  
  // Actions
  loadColumnModelMappings: () => Promise<void>;
  updateColumnMapping: (column: string, mapping: string) => void;
  clearTransformationCache: (type: 'current' | 'all') => Promise<void>;
  
  // Transformation operations
  initializeTransformationResults: (sourceRows: any[], startIndex?: number) => void;
  transformSelectedRows: (rowIndices: number[], sourceRows: any[], bulkOperationId?: string) => Promise<void>;
  transformAllRows: (sourceRows: any[], numRows?: number) => Promise<void>;
  cancelTransformation: () => Promise<void>;
  
  // Progress tracking
  startProgressTracking: (totalRows: number, totalCells: number) => void;
  updateProgress: (processedCells: number, currentRow: number, currentRowCells: number) => void;
  stopProgressTracking: () => void;
  
  // Export operations
  exportData: (format: 'excel' | 'csv') => Promise<void>;
  
  // Utilities
  formatDuration: (ms: number) => string;
}

export function useTransformationState(options: UseTransformationStateOptions): UseTransformationStateReturn {
  const {
    jobId,
    selectedTargetColumns,
    columnMappingTypes,
    oneToOneMappings,
    aiTransformMappings,
    selectedLlmModel
  } = options;

  // State
  const [columnModelMappings, setColumnModelMappings] = useState<Record<string, string | null>>({});
  const [transformationResults, setTransformationResults] = useState<Record<number, RowTransformationResult>>({});
  const [transformationJobId, setTransformationJobId] = useState<string | null>(null);
  const [isTransforming, setIsTransforming] = useState(false);
  const [isCreatingTasks, setIsCreatingTasks] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  
  const [transformationProgress, setTransformationProgress] = useState<TransformationProgress>({
    totalRows: 0,
    totalCells: 0,
    processedRows: 0,
    processedCells: 0,
    currentRow: 0,
    currentRowCells: 0,
    currentRowTotalCells: 0,
    startTime: 0,
    elapsedTime: 0,
    estimatedTimeRemaining: 0,
    overallProgress: 0
  });
  
  // Timer and cancellation refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const cancellationRef = useRef<{ cancelled: boolean }>({ cancelled: false });

  // Load column model mappings from AI transform state
  const loadColumnModelMappings = useCallback(async () => {
    if (!jobId) return;
    
    try {
      const response = await fetch(`/api/import/ai-transform-state?job_id=${jobId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.columnStates) {
          const mappings: Record<string, string | null> = {};
          Object.values(result.data.columnStates).forEach((columnState: any) => {
            if (columnState.columnName && columnState.selectedModel) {
              mappings[columnState.columnName] = columnState.selectedModel;
            }
          });
          setColumnModelMappings(mappings);
          console.log('✅ Loaded column model mappings:', mappings);
        }
      }
    } catch (error) {
      console.error('Failed to load column model mappings:', error);
    }
  }, [jobId]);

  // Update column mapping
  const updateColumnMapping = useCallback((column: string, mapping: string) => {
    setColumnModelMappings(prev => ({
      ...prev,
      [column]: mapping
    }));
  }, []);

  // Initialize transformation results for source rows
  const initializeTransformationResults = useCallback((sourceRows: any[], startIndex: number = 0) => {
    const results: Record<number, RowTransformationResult> = {};
    
    sourceRows.forEach((row, index) => {
      const globalRowIndex = startIndex + index;
      results[globalRowIndex] = {
        rowIndex: globalRowIndex,
        sourceData: row,
        transformedData: {},
        status: 'pending'
      };
    });
    
    setTransformationResults(prev => ({
      ...prev,
      ...results
    }));
  }, []);

  // Perform AI transformation for a single cell
  const performAITransformation = useCallback(async (
    sourceRow: any, 
    targetColumn: string, 
    prompt: string,
    rowIndex: number,
    bulkOperationId?: string
  ): Promise<string> => {
    // Get the saved model for this column, fallback to global model
    const columnModel = columnModelMappings[targetColumn] || selectedLlmModel;
    
    const response = await fetch('/api/import/ai-transform', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        job_id: jobId,
        column_name: targetColumn,
        prompt_template: prompt,
        row_index: rowIndex,
        model_id: columnModel,
        bulk_operation_id: bulkOperationId
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.cancelled) {
      throw new Error('Operation cancelled');
    }
    
    if (!result.success) {
      throw new Error(result.error || 'AI transformation failed');
    }
    
    return result.transformed_value || '';
  }, [columnModelMappings, selectedLlmModel, jobId]);

  // Transform specific rows
  const transformSelectedRows = useCallback(async (rowIndices: number[], sourceRows: any[], bulkOperationId?: string) => {
    if (!jobId || rowIndices.length === 0) return;
    
    const startTime = Date.now();
    const totalRows = rowIndices.length;
    const totalCells = totalRows * selectedTargetColumns.length;
    
    setIsTransforming(true);
    setIsCancelled(false);
    cancellationRef.current.cancelled = false;
    
    startProgressTracking(totalRows, totalCells);
    
    try {
      let processedCells = 0;
      
      for (let i = 0; i < rowIndices.length; i++) {
        if (cancellationRef.current.cancelled) {
          break;
        }
        
        const globalRowIndex = rowIndices[i];
        const sourceRow = sourceRows[globalRowIndex] || sourceRows[globalRowIndex % sourceRows.length];
        
        if (!sourceRow) continue;
        
        // Update current row progress
        updateProgress(processedCells, i + 1, 0);
        
        // Update row status to processing
        setTransformationResults(prev => ({
          ...prev,
          [globalRowIndex]: {
            ...prev[globalRowIndex],
            status: 'processing'
          }
        }));
        
        // Transform each target column for this row
        const transformedData: Record<string, any> = {};
        
        for (let j = 0; j < selectedTargetColumns.length; j++) {
          if (cancellationRef.current.cancelled) {
            break;
          }
          
          const targetColumn = selectedTargetColumns[j];
          const mappingType = columnMappingTypes[targetColumn];
          
          if (mappingType === 'one-to-one') {
            // Simple one-to-one mapping
            const sourceColumn = oneToOneMappings[targetColumn];
            if (sourceColumn && sourceRow[sourceColumn] !== undefined) {
              transformedData[targetColumn] = sourceRow[sourceColumn];
            }
          } else if (mappingType === 'ai-transform') {
            // AI transformation
            const prompt = aiTransformMappings[targetColumn];
            if (prompt) {
              try {
                const transformedValue = await performAITransformation(sourceRow, targetColumn, prompt, globalRowIndex, bulkOperationId);
                transformedData[targetColumn] = transformedValue;
              } catch (error) {
                console.error(`AI transformation failed for column ${targetColumn}:`, error);
                // Check if this was a cancellation
                if (error instanceof Error && error.message === 'Operation cancelled') {
                  cancellationRef.current.cancelled = true;
                  break;
                }
                transformedData[targetColumn] = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
              }
            }
          } else {
            // Default value
            transformedData[targetColumn] = '';
          }
          
          // Update progress after each cell is processed
          processedCells++;
          updateProgress(processedCells, i + 1, j + 1);
          
          // Small delay to allow UI updates
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        // Update row with completed transformation
        setTransformationResults(prev => ({
          ...prev,
          [globalRowIndex]: {
            ...prev[globalRowIndex],
            transformedData,
            status: cancellationRef.current.cancelled ? 'pending' : 'completed'
          }
        }));
      }
      
      if (!cancellationRef.current.cancelled) {
        toast.success('Transformation completed successfully');
      }
    } catch (error) {
      console.error('Transformation error:', error);
      toast.error('Transformation failed', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setIsTransforming(false);
      stopProgressTracking();
    }
  }, [
    jobId, 
    selectedTargetColumns, 
    columnMappingTypes, 
    oneToOneMappings, 
    aiTransformMappings, 
    performAITransformation
  ]);

  // Transform all rows
  const transformAllRows = useCallback(async (sourceRows: any[], numRows?: number) => {
    const rowsToTransform = numRows ? sourceRows.slice(0, numRows) : sourceRows;
    const rowIndices = rowsToTransform.map((_, index) => index);
    await transformSelectedRows(rowIndices, sourceRows);
  }, [transformSelectedRows]);

  // Cancel transformation
  const cancelTransformation = useCallback(async () => {
    setIsCancelling(true);
    cancellationRef.current.cancelled = true;
    
    if (transformationJobId) {
      try {
        const response = await fetch(`/api/import/transformation-job/${transformationJobId}/cancel`, {
          method: 'POST'
        });
        
        if (response.ok) {
          setIsCancelled(true);
          toast.success('Transformation cancelled successfully');
        }
      } catch (error) {
        console.error('Failed to cancel transformation:', error);
        toast.error('Failed to cancel transformation');
      }
    } else {
      setIsCancelled(true);
      toast.success('Transformation cancelled');
    }
    
    setIsCancelling(false);
    setIsTransforming(false);
    stopProgressTracking();
  }, [transformationJobId]);

  // Progress tracking functions
  const startProgressTracking = useCallback((totalRows: number, totalCells: number) => {
    const startTime = Date.now();
    setTransformationProgress({
      totalRows,
      totalCells,
      processedRows: 0,
      processedCells: 0,
      currentRow: 0,
      currentRowCells: 0,
      currentRowTotalCells: selectedTargetColumns.length,
      startTime,
      elapsedTime: 0,
      estimatedTimeRemaining: 0,
      overallProgress: 0
    });
    
    // Start progress timer
    timerRef.current = setInterval(() => {
      setTransformationProgress(prev => ({
        ...prev,
        elapsedTime: Date.now() - startTime,
        estimatedTimeRemaining: prev.processedCells > 0 ? 
          Math.round(((Date.now() - startTime) / prev.processedCells) * (totalCells - prev.processedCells)) : 0
      }));
    }, 100);
  }, [selectedTargetColumns.length]);

  const updateProgress = useCallback((processedCells: number, currentRow: number, currentRowCells: number) => {
    setTransformationProgress(prev => ({
      ...prev,
      processedCells,
      currentRow,
      currentRowCells,
      processedRows: Math.floor(processedCells / selectedTargetColumns.length),
      overallProgress: Math.round((processedCells / prev.totalCells) * 100)
    }));
  }, [selectedTargetColumns.length]);

  const stopProgressTracking = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Clear transformation cache
  const clearTransformationCache = useCallback(async (type: 'current' | 'all') => {
    if (!jobId) return;
    
    try {
      const endpoint = type === 'current' 
        ? `/api/import/clear-transformation-cache?job_id=${jobId}`
        : '/api/import/clear-transformation-cache';
        
      const response = await fetch(endpoint, { method: 'POST' });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          toast.success(`${type === 'current' ? 'Current job' : 'All'} transformation cache cleared`);
        }
      }
    } catch (error) {
      console.error('Failed to clear transformation cache:', error);
      toast.error('Failed to clear transformation cache');
    }
  }, [jobId]);

  // Export data
  const exportData = useCallback(async (format: 'excel' | 'csv') => {
    if (!jobId) return;
    
    try {
      const response = await fetch(`/api/jobs/${jobId}/export-${format}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `export.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success(`Data exported as ${format.toUpperCase()}`);
      }
    } catch (error) {
      console.error(`Failed to export as ${format}:`, error);
      toast.error(`Failed to export as ${format.toUpperCase()}`);
    }
  }, [jobId]);

  // Format duration utility
  const formatDuration = useCallback((ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    if (ms < 3600000) return `${Math.round(ms / 60000)}m`;
    return `${Math.round(ms / 3600000)}h`;
  }, []);

  // Auto-load column model mappings when jobId changes
  useEffect(() => {
    if (jobId) {
      loadColumnModelMappings();
    }
  }, [jobId, loadColumnModelMappings]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return {
    // Column mappings
    columnModelMappings,
    
    // Transformation state
    transformationResults,
    transformationProgress,
    transformationJobId,
    
    // Status flags
    isTransforming,
    isCreatingTasks,
    isCancelling,
    isCancelled,
    
    // Actions
    loadColumnModelMappings,
    updateColumnMapping,
    clearTransformationCache,
    
    // Transformation operations
    initializeTransformationResults,
    transformSelectedRows,
    transformAllRows,
    cancelTransformation,
    
    // Progress tracking
    startProgressTracking,
    updateProgress,
    stopProgressTracking,
    
    // Export operations
    exportData,
    
    // Utilities
    formatDuration
  };
} 