import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';

export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalRows: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface DataLoadingState {
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  lastLoadTime: number;
}

export interface DataFilters {
  searchTerm: string;
  columnFilters: Record<string, string>;
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
}

export interface UseDataPaginationOptions {
  jobId: string | null;
  initialPageSize?: number;
  enableVirtualization?: boolean;
  enableCaching?: boolean;
  cacheSize?: number;
}

export interface UseDataPaginationReturn {
  // Data state
  sourceData: any[];
  transformedData: any[];
  currentPageData: any[];
  
  // Pagination state
  pagination: PaginationState;
  
  // Loading state
  loading: DataLoadingState;
  
  // Filters
  filters: DataFilters;
  
  // Actions
  loadPage: (page: number) => Promise<void>;
  loadNextPage: () => Promise<void>;
  loadPreviousPage: () => Promise<void>;
  setPageSize: (size: number) => void;
  refreshData: () => Promise<void>;
  
  // Filtering
  setSearchTerm: (term: string) => void;
  setColumnFilter: (column: string, value: string) => void;
  clearFilters: () => void;
  setSorting: (column: string, direction: 'asc' | 'desc') => void;
  
  // Data access
  getRowByIndex: (index: number) => any | null;
  getVisibleRows: () => any[];
  getTotalRowCount: () => number;
  
  // Cache management
  clearCache: () => void;
  getCacheStats: () => { size: number; hitRate: number };
}

export function useDataPagination(options: UseDataPaginationOptions): UseDataPaginationReturn {
  const {
    jobId,
    initialPageSize = 50,
    enableVirtualization = true,
    enableCaching = true,
    cacheSize = 1000
  } = options;

  // Data state
  const [sourceData, setSourceData] = useState<any[]>([]);
  const [transformedData, setTransformedData] = useState<any[]>([]);
  const [currentPageData, setCurrentPageData] = useState<any[]>([]);
  
  // Pagination state
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    pageSize: initialPageSize,
    totalRows: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });
  
  // Loading state
  const [loading, setLoading] = useState<DataLoadingState>({
    isLoading: false,
    isLoadingMore: false,
    error: null,
    lastLoadTime: 0
  });
  
  // Filters
  const [filters, setFilters] = useState<DataFilters>({
    searchTerm: '',
    columnFilters: {},
    sortColumn: null,
    sortDirection: 'asc'
  });
  
  // Cache for loaded pages
  const pageCache = useRef<Map<string, { data: any[]; timestamp: number }>>(new Map());
  const cacheHits = useRef(0);
  const cacheMisses = useRef(0);
  
  // Generate cache key for current state
  const getCacheKey = useCallback((page: number, pageSize: number, filters: DataFilters): string => {
    return `${page}-${pageSize}-${JSON.stringify(filters)}`;
  }, []);
  
  // Check if cache entry is valid
  const isCacheValid = useCallback((timestamp: number): boolean => {
    const maxAge = 5 * 60 * 1000; // 5 minutes
    return Date.now() - timestamp < maxAge;
  }, []);
  
  // Get data from cache
  const getFromCache = useCallback((key: string): any[] | null => {
    if (!enableCaching) return null;
    
    const cached = pageCache.current.get(key);
    if (cached && isCacheValid(cached.timestamp)) {
      cacheHits.current++;
      return cached.data;
    }
    
    if (cached) {
      pageCache.current.delete(key); // Remove expired entry
    }
    
    cacheMisses.current++;
    return null;
  }, [enableCaching, isCacheValid]);
  
  // Store data in cache
  const storeInCache = useCallback((key: string, data: any[]): void => {
    if (!enableCaching) return;
    
    // Implement LRU eviction if cache is full
    if (pageCache.current.size >= cacheSize) {
      const oldestKey = pageCache.current.keys().next().value;
      if (oldestKey) {
        pageCache.current.delete(oldestKey);
      }
    }
    
    pageCache.current.set(key, {
      data: [...data], // Clone to prevent mutations
      timestamp: Date.now()
    });
  }, [enableCaching, cacheSize]);
  
  // Load data from API
  const loadDataFromAPI = useCallback(async (
    page: number,
    pageSize: number,
    searchTerm: string = '',
    columnFilters: Record<string, string> = {},
    sortColumn: string | null = null,
    sortDirection: 'asc' | 'desc' = 'asc'
  ): Promise<{ data: any[]; totalRows: number }> => {
    if (!jobId) {
      throw new Error('No job ID provided');
    }
    
    const params = new URLSearchParams({
      page: page.toString(),
      limit: pageSize.toString(),
      ...(searchTerm && { search: searchTerm }),
      ...(sortColumn && { sort_column: sortColumn }),
      ...(sortDirection && { sort_direction: sortDirection }),
      ...Object.entries(columnFilters).reduce((acc, [key, value]) => {
        if (value) acc[`filter_${key}`] = value;
        return acc;
      }, {} as Record<string, string>)
    });
    
    const response = await fetch(`/api/import/grid-data?job_id=${jobId}&${params}`);
    
    if (!response.ok) {
      throw new Error(`Failed to load data: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to load data');
    }
    
    return {
      data: result.data || [],
      totalRows: result.totalRows || result.total || 0
    };
  }, [jobId]);
  
  // Load specific page
  const loadPage = useCallback(async (page: number) => {
    const cacheKey = getCacheKey(page, pagination.pageSize, filters);
    
    // Check cache first
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      setCurrentPageData(cachedData);
      setPagination(prev => ({
        ...prev,
        currentPage: page,
        hasNextPage: page < prev.totalPages,
        hasPreviousPage: page > 1
      }));
      return;
    }
    
    setLoading(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const { data, totalRows } = await loadDataFromAPI(
        page,
        pagination.pageSize,
        filters.searchTerm,
        filters.columnFilters,
        filters.sortColumn,
        filters.sortDirection
      );
      
      const totalPages = Math.ceil(totalRows / pagination.pageSize);
      
      setCurrentPageData(data);
      setPagination({
        currentPage: page,
        pageSize: pagination.pageSize,
        totalRows,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      });
      
      // Store in cache
      storeInCache(cacheKey, data);
      
      setLoading(prev => ({
        ...prev,
        isLoading: false,
        lastLoadTime: Date.now()
      }));
      
    } catch (error) {
      console.error('Failed to load page:', error);
      setLoading(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
      toast.error('Failed to load data');
    }
  }, [pagination.pageSize, filters, getCacheKey, getFromCache, loadDataFromAPI, storeInCache]);
  
  // Load next page
  const loadNextPage = useCallback(async () => {
    if (pagination.hasNextPage) {
      await loadPage(pagination.currentPage + 1);
    }
  }, [pagination.hasNextPage, pagination.currentPage, loadPage]);
  
  // Load previous page
  const loadPreviousPage = useCallback(async () => {
    if (pagination.hasPreviousPage) {
      await loadPage(pagination.currentPage - 1);
    }
  }, [pagination.hasPreviousPage, pagination.currentPage, loadPage]);
  
  // Set page size
  const setPageSize = useCallback((size: number) => {
    setPagination(prev => ({
      ...prev,
      pageSize: size,
      totalPages: Math.ceil(prev.totalRows / size),
      currentPage: 1 // Reset to first page when changing page size
    }));
    
    // Clear cache when page size changes
    pageCache.current.clear();
    
    // Reload current page with new size
    loadPage(1);
  }, [loadPage]);
  
  // Refresh data
  const refreshData = useCallback(async () => {
    // Clear cache to force fresh data
    pageCache.current.clear();
    await loadPage(pagination.currentPage);
  }, [pagination.currentPage, loadPage]);
  
  // Set search term
  const setSearchTerm = useCallback((term: string) => {
    setFilters(prev => ({ ...prev, searchTerm: term }));
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      pageCache.current.clear(); // Clear cache when filters change
      loadPage(1); // Reset to first page when searching
    }, 300);
    
    return () => clearTimeout(timeoutId);
  }, [loadPage]);
  
  // Set column filter
  const setColumnFilter = useCallback((column: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      columnFilters: {
        ...prev.columnFilters,
        [column]: value
      }
    }));
    
    pageCache.current.clear(); // Clear cache when filters change
    loadPage(1); // Reset to first page when filtering
  }, [loadPage]);
  
  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({
      searchTerm: '',
      columnFilters: {},
      sortColumn: null,
      sortDirection: 'asc'
    });
    
    pageCache.current.clear();
    loadPage(1);
  }, [loadPage]);
  
  // Set sorting
  const setSorting = useCallback((column: string, direction: 'asc' | 'desc') => {
    setFilters(prev => ({
      ...prev,
      sortColumn: column,
      sortDirection: direction
    }));
    
    pageCache.current.clear();
    loadPage(1);
  }, [loadPage]);
  
  // Get row by index (supports virtual scrolling)
  const getRowByIndex = useCallback((index: number): any | null => {
    if (enableVirtualization) {
      // For virtualization, we only have current page data
      const localIndex = index % pagination.pageSize;
      return currentPageData[localIndex] || null;
    } else {
      // For non-virtualized, we might have all data loaded
      return sourceData[index] || null;
    }
  }, [enableVirtualization, pagination.pageSize, currentPageData, sourceData]);
  
  // Get visible rows
  const getVisibleRows = useCallback((): any[] => {
    return currentPageData;
  }, [currentPageData]);
  
  // Get total row count
  const getTotalRowCount = useCallback((): number => {
    return pagination.totalRows;
  }, [pagination.totalRows]);
  
  // Clear cache
  const clearCache = useCallback(() => {
    pageCache.current.clear();
    cacheHits.current = 0;
    cacheMisses.current = 0;
    toast.success('Cache cleared');
  }, []);
  
  // Get cache statistics
  const getCacheStats = useCallback(() => {
    const totalRequests = cacheHits.current + cacheMisses.current;
    const hitRate = totalRequests > 0 ? (cacheHits.current / totalRequests) * 100 : 0;
    
    return {
      size: pageCache.current.size,
      hitRate: Math.round(hitRate)
    };
  }, []);
  
  // Load initial data when jobId changes
  useEffect(() => {
    if (jobId) {
      loadPage(1);
    }
  }, [jobId]); // Only depend on jobId to avoid infinite loops
  
  // Cleanup cache on unmount
  useEffect(() => {
    return () => {
      pageCache.current.clear();
    };
  }, []);

  return {
    // Data state
    sourceData,
    transformedData,
    currentPageData,
    
    // Pagination state
    pagination,
    
    // Loading state
    loading,
    
    // Filters
    filters,
    
    // Actions
    loadPage,
    loadNextPage,
    loadPreviousPage,
    setPageSize,
    refreshData,
    
    // Filtering
    setSearchTerm,
    setColumnFilter,
    clearFilters,
    setSorting,
    
    // Data access
    getRowByIndex,
    getVisibleRows,
    getTotalRowCount,
    
    // Cache management
    clearCache,
    getCacheStats,
  };
} 