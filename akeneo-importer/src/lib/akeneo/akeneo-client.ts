import { AkeneoAttribute, <PERSON><PERSON><PERSON>F<PERSON><PERSON>, AkeneoCategory, AkeneoProduct } from '@/types';

export class AkeneoClient {
  private baseUrl: string;
  private accessToken?: string;
  private tokenExpiry?: Date;
  private isAuthenticating = false;
  
  constructor(
    private clientId: string,
    private clientSecret: string,
    private username: string,
    private password: string,
    baseUrl: string
  ) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
  }
  
  /**
   * Authenticate with Akeneo API using password grant
   * Based on the Python implementation which uses form-encoded data
   */
  private async authenticate(): Promise<void> {
    // Prevent multiple concurrent authentication attempts
    if (this.isAuthenticating) {
      await new Promise(resolve => setTimeout(resolve, 100));
      return this.authenticate();
    }

    this.isAuthenticating = true;

    try {
      // Use form-encoded data like the Python implementation
      const formData = new URLSearchParams();
      formData.append('grant_type', 'password');
      formData.append('client_id', this.clientId);
      formData.append('client_secret', this.clientSecret);
      formData.append('username', this.username);
      formData.append('password', this.password);

      console.log(`Requesting token from: ${this.baseUrl}/api/oauth/v1/token`);
      
      const response = await fetch(`${this.baseUrl}/api/oauth/v1/token`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        body: formData.toString()
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Akeneo authentication failed (${response.status}):`, errorText);
        throw new Error(`Akeneo authentication failed (${response.status}): ${errorText}`);
      }
      
      const data = await response.json();
      
      if (!data.access_token) {
        console.error('No access token in response:', data);
        throw new Error('No access token received from Akeneo API');
      }

      this.accessToken = data.access_token;
      this.tokenExpiry = new Date(Date.now() + ((data.expires_in || 3600) * 1000));
      
      console.log('Akeneo authentication successful');
      // console.log(`Token expires in: ${data.expires_in || 3600} seconds`);
    } catch (error) {
      console.error('Akeneo authentication error:', error);
      throw new Error(`Akeneo authentication failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      this.isAuthenticating = false;
    }
  }
  
  /**
   * Make authenticated request to Akeneo API
   * Includes automatic token refresh on 401 errors
   */
  private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    // Check if token needs refresh (with 60 second buffer)
    if (!this.accessToken || (this.tokenExpiry && this.tokenExpiry <= new Date(Date.now() + 60000))) {
      console.log('Token missing or expiring soon, refreshing...');
      await this.authenticate();
    }
    
    const url = `${this.baseUrl}/api/rest/v1${endpoint}`;
    
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...options.headers,
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      // Handle 401 errors by refreshing token and retrying once
      if (response.status === 401) {
        console.log('Received 401, refreshing token and retrying...');
        await this.authenticate();
        
        // Retry the request with new token
        const retryResponse = await fetch(url, {
          ...options,
          headers: {
            ...options.headers,
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        if (!retryResponse.ok) {
          const errorText = await retryResponse.text();
          throw new Error(`Akeneo API error after retry (${retryResponse.status}): ${errorText}`);
        }

        return retryResponse.json();
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Akeneo API error (${response.status}): ${errorText}`);
      }
      
      return response.json();
    } catch (error) {
      console.error(`Akeneo API request failed for ${endpoint}:`, error);
      throw new Error(`Akeneo API request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Test connection to Akeneo API
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('Testing Akeneo connection...');
      await this.getAllAttributes({ limit: 1 });
      console.log('Akeneo connection test successful');
      return true;
    } catch (error) {
      console.error('Akeneo connection test failed:', error);
      return false;
    }
  }
  
  // ============ ATTRIBUTES API ============
  
  /**
   * Get all attributes from Akeneo
   * This is critical for the unified configuration system
   */
  async getAttributes(params: { 
    limit?: number; 
    page?: number;
    search?: string;
    with_count?: boolean;
  } = {}): Promise<{ 
    _embedded: { items: AkeneoAttribute[] }; 
    _links: any;
    items_count?: number;
  }> {
    const searchParams = new URLSearchParams();
    
    // Set default limit to get all attributes for unified config
    searchParams.append('limit', (params.limit || 100).toString());
    
    if (params.page) {
      searchParams.append('page', params.page.toString());
    }
    
    if (params.search) {
      searchParams.append('search', params.search);
    }
    
    if (params.with_count !== false) {
      searchParams.append('with_count', 'true');
    }
    
    const endpoint = `/attributes?${searchParams.toString()}`;
    return this.request(endpoint);
  }

  /**
   * Get attribute options for a specific attribute (like HERSTELLER)
   */
  async getAttributeOptions(attributeCode: string, limit: number = 100): Promise<any[]> {
    const endpoint = `/attributes/${attributeCode}/options?limit=${limit}`;
    const response = await this.request(endpoint);
    return response._embedded?.items || [];
  }
  
  /**
   * Get all attributes in a simpler format for unified configuration
   */
  async getAllAttributes(params: { 
    limit?: number; 
    page?: number;
    search?: string;
    with_count?: boolean;
  } = {}): Promise<AkeneoAttribute[]> {
    const allAttributes: AkeneoAttribute[] = [];
    let page = 1;
    let hasMorePages = true;
    
    console.log('Loading all attributes from Akeneo...');
    
    while (hasMorePages) {
      try {
        const response = await this.getAttributes({ 
          limit: params.limit || 100, 
          page,
          with_count: true,
          search: params.search
        });
        
        const attributes = response._embedded?.items || [];
        allAttributes.push(...attributes);
        
        console.log(`Loaded page ${page}: ${attributes.length} attributes (total: ${allAttributes.length})`);
        
        // Check if there are more pages
        hasMorePages = response._links?.next?.href ? true : false;
        page++;
        
        if (params.limit && allAttributes.length >= params.limit) {
          return allAttributes.slice(0, params.limit);
        }

        // Safety check to prevent infinite loops
        if (page > 100) {
          console.warn('Akeneo attributes pagination limit reached (100 pages)');
          break;
        }
      } catch (error) {
        console.error(`Failed to fetch attributes page ${page}:`, error);
        break;
      }
    }
    
    console.log(`Loaded ${allAttributes.length} total attributes from Akeneo`);
    return allAttributes;
  }
  
  // ============ FAMILIES API ============
  
  async getFamilies(params: { 
    limit?: number; 
    page?: number;
    search?: string;
    with_count?: boolean;
  } = {}): Promise<AkeneoFamily[]> {
    const allFamilies: AkeneoFamily[] = [];
    let page = 1;
    let hasMorePages = true;

    while (hasMorePages) {
      const searchParams = new URLSearchParams();
      searchParams.append('limit', (params.limit || 100).toString());
      searchParams.append('page', page.toString());
      if (params.search) {
        searchParams.append('search', params.search);
      }
      if (params.with_count !== false) {
        searchParams.append('with_count', 'true');
      }

      const endpoint = `/families?${searchParams.toString()}`;
      const response = await this.request(endpoint);

      const families = response._embedded?.items || [];
      allFamilies.push(...families);

      hasMorePages = response._links?.next?.href ? true : false;
      page++;

      if (page > 100) { // Safety break
        console.warn('Akeneo families pagination limit reached (100 pages)');
        break;
      }
    }
    return allFamilies;
  }
  
  async getFamily(code: string): Promise<AkeneoFamily> {
    return this.request(`/families/${code}`);
  }
  
  // ============ CATEGORIES API ============
  
  async getCategories(): Promise<{ _embedded: { items: AkeneoCategory[] } }> {
    return this.request('/categories');
  }
  
  async getCategory(code: string): Promise<AkeneoCategory> {
    return this.request(`/categories/${code}`);
  }

  // ============ CHANNELS API ============
  
  async getChannels(): Promise<{ _embedded: { items: any[] } }> {
    return this.request('/channels');
  }

  // ============ LOCALES API ============
  
  async getLocales(): Promise<{ _embedded: { items: any[] } }> {
    return this.request('/locales');
  }
  
  // ============ PRODUCTS API ============
  
  async getProducts(params: Record<string, any> = {}): Promise<{ _embedded: { items: AkeneoProduct[] } }> {
    const searchParams = new URLSearchParams(params);
    const endpoint = `/products${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request(endpoint);
  }
  
  async getProduct(uuid: string): Promise<AkeneoProduct> {
    return this.request(`/products/${uuid}`);
  }
  
  async createProduct(productData: Partial<AkeneoProduct>): Promise<AkeneoProduct> {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData)
    });
  }
  
  async updateProduct(uuid: string, productData: Partial<AkeneoProduct>): Promise<AkeneoProduct> {
    return this.request(`/products/${uuid}`, {
      method: 'PATCH',
      body: JSON.stringify(productData)
    });
  }
  
  // ============ UTILITY METHODS ============
  
  /**
   * Get current client status
   */
  async getStatus(): Promise<{
    connected: boolean;
    baseUrl: string;
    authenticated: boolean;
    tokenExpiry?: string;
  }> {
    const authenticated = !!this.accessToken && (!this.tokenExpiry || this.tokenExpiry > new Date());
    
    let connected = false;
    if (authenticated) {
      connected = await this.testConnection();
    }
    
    return {
      connected,
      baseUrl: this.baseUrl,
      authenticated,
      tokenExpiry: this.tokenExpiry?.toISOString()
    };
  }
  
  /**
   * Clear authentication state
   */
  clearAuthentication(): void {
    this.accessToken = undefined;
    this.tokenExpiry = undefined;
    console.log('Akeneo authentication cleared');
  }
}

// Singleton instance
let akeneoClientInstance: AkeneoClient | null = null;

/**
 * Get or create singleton Akeneo client instance
 */
export function getAkeneoClient(): AkeneoClient {
  if (!akeneoClientInstance) {
    const clientId = process.env.AKENEO_CLIENT_ID;
    const clientSecret = process.env.AKENEO_CLIENT_SECRET;
    const username = process.env.AKENEO_USERNAME;
    const password = process.env.AKENEO_PASSWORD;
    const baseUrl = process.env.AKENEO_BASE_URL || process.env.AKENEO_ENDPOINT;
    
    console.log('Akeneo Environment Variables Check:');
    console.log('AKENEO_CLIENT_ID:', clientId ? 'Set' : 'Missing');
    console.log('AKENEO_CLIENT_SECRET:', clientSecret ? 'Set' : 'Missing');
    console.log('AKENEO_USERNAME:', username ? 'Set' : 'Missing');
    console.log('AKENEO_PASSWORD:', password ? 'Set' : 'Missing');
    console.log('AKENEO_BASE_URL:', process.env.AKENEO_BASE_URL ? 'Set' : 'Missing');
    console.log('AKENEO_ENDPOINT:', process.env.AKENEO_ENDPOINT ? 'Set' : 'Missing');
    console.log('Using baseUrl:', baseUrl);
    
    if (!clientId || !clientSecret || !username || !password || !baseUrl) {
      throw new Error(
        'Missing Akeneo configuration. Please set AKENEO_CLIENT_ID, AKENEO_CLIENT_SECRET, ' +
        'AKENEO_USERNAME, AKENEO_PASSWORD, and AKENEO_BASE_URL (or AKENEO_ENDPOINT) environment variables.'
      );
    }
    
    akeneoClientInstance = new AkeneoClient(
      clientId,
      clientSecret,
      username,
      password,
      baseUrl
    );
  }
  
  return akeneoClientInstance;
}

/**
 * Create a new Akeneo client instance (for testing or specific configurations)
 */
export function createAkeneoClient(config: {
  clientId: string;
  clientSecret: string;
  username: string;
  password: string;
  baseUrl: string;
}): AkeneoClient {
  return new AkeneoClient(
    config.clientId,
    config.clientSecret,
    config.username,
    config.password,
    config.baseUrl
  );
}

/**
 * Reset the singleton instance (useful for testing)
 */
export function resetAkeneoClient(): void {
  akeneoClientInstance = null;
} 