import { getAkeneoClient } from './akeneo-client';
import { 
  AkeneoAttribute, 
  AkeneoFamily, 
  AkeneoCategory, 
  AkeneoProduct,
  UnifiedColumn,
  AkeneoColumnMetadata
} from '@/types';

export class AkeneoIntegrationService {
  private client = getAkeneoClient();
  private attributesCache: AkeneoAttribute[] | null = null;
  private cacheTimestamp: Date | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get all Akeneo attributes with caching
   * Critical for unified configuration system
   */
  async getAttributes(forceRefresh = false): Promise<AkeneoAttribute[]> {
    // Check cache validity
    if (!forceRefresh && this.attributesCache && this.cacheTimestamp) {
      const cacheAge = Date.now() - this.cacheTimestamp.getTime();
      if (cacheAge < this.CACHE_DURATION) {
        console.log('Returning cached Akeneo attributes');
        return this.attributesCache;
      }
    }

    try {
      console.log('Fetching fresh Akeneo attributes...');
      this.attributesCache = await this.client.getAllAttributes();
      this.cacheTimestamp = new Date();
      
      console.log(`Loaded ${this.attributesCache.length} Akeneo attributes`);
      return this.attributesCache;
    } catch (error) {
      console.error('Failed to load Akeneo attributes:', error);
      
      // Return cached data if available, even if stale
      if (this.attributesCache) {
        console.warn('Returning stale cached attributes due to API error');
        return this.attributesCache;
      }
      
      throw new Error(`Failed to load Akeneo attributes: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Convert Akeneo attributes to unified columns for configuration system
   */
  async getUnifiedColumnsFromAkeneo(): Promise<UnifiedColumn[]> {
    const attributes = await this.getAttributes();
    
    return attributes.map(attr => this.convertAkeneoAttributeToUnifiedColumn(attr));
  }

  /**
   * Convert a single Akeneo attribute to unified column format
   */
  private convertAkeneoAttributeToUnifiedColumn(attribute: AkeneoAttribute): UnifiedColumn {
    const metadata: AkeneoColumnMetadata = {
      attribute_type: attribute.type,
      localizable: attribute.localizable || false,
      scopable: attribute.scopable || false,
      group: attribute.group
    };

    return {
      column_name: attribute.code,
      type: 'Akeneo', // PIM type
      source: 'api-only',
      required: false, // This will be determined by configuration
      default_mapping: null, // To be set by configuration
      default_mapping_content: null,
      prompt: null,
      output_validation_column: null,
      api_metadata: metadata,
      has_configuration: false
    };
  }

  /**
   * Get human-readable label for attribute
   */
  private getAttributeLabel(attribute: AkeneoAttribute): string {
    // Try to get English label first, then fallback to code
    if (attribute.labels) {
      return attribute.labels.en || attribute.labels[Object.keys(attribute.labels)[0]] || attribute.code;
    }
    return attribute.code;
  }

  /**
   * Get Akeneo families
   */
  async getFamilies(): Promise<AkeneoFamily[]> {
    try {
      const response = await this.client.getFamilies();
      return response;
    } catch (error) {
      console.error('Failed to load Akeneo families:', error);
      throw new Error(`Failed to load families: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get Akeneo categories
   */
  async getCategories(): Promise<AkeneoCategory[]> {
    try {
      const response = await this.client.getCategories();
      return response._embedded?.items || [];
    } catch (error) {
      console.error('Failed to load Akeneo categories:', error);
      throw new Error(`Failed to load categories: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Test connection to Akeneo
   */
  async testConnection(): Promise<boolean> {
    try {
      return await this.client.testConnection();
    } catch (error) {
      console.error('Akeneo connection test failed:', error);
      return false;
    }
  }

  /**
   * Get connection status and statistics
   */
  async getConnectionStatus(): Promise<{
    connected: boolean;
    configured: boolean;
    attributes_count?: number;
    families_count?: number;
    categories_count?: number;
    last_sync?: string;
  }> {
    try {
      const status = await this.client.getStatus();
      
      let attributesCount, familiesCount, categoriesCount;
      
      if (status.connected) {
        try {
          const [attributes, families, categories] = await Promise.allSettled([
            this.getAttributes(),
            this.getFamilies(),
            this.getCategories()
          ]);
          
          attributesCount = attributes.status === 'fulfilled' ? attributes.value.length : undefined;
          familiesCount = families.status === 'fulfilled' ? families.value.length : undefined;
          categoriesCount = categories.status === 'fulfilled' ? categories.value.length : undefined;
        } catch (error) {
          console.warn('Failed to get Akeneo statistics:', error);
        }
      }

      return {
        connected: status.connected,
        configured: status.connected, // If we can connect, it's configured
        attributes_count: attributesCount,
        families_count: familiesCount,
        categories_count: categoriesCount,
        last_sync: this.cacheTimestamp?.toISOString()
      };
    } catch (error) {
      console.error('Failed to get Akeneo status:', error);
      return {
        connected: false,
        configured: false
      };
    }
  }

  /**
   * Clear cache and force refresh
   */
  clearCache(): void {
    this.attributesCache = null;
    this.cacheTimestamp = null;
    console.log('Akeneo cache cleared');
  }

  /**
   * Get attributes by type/filter
   */
  async getAttributesByType(types: string[]): Promise<AkeneoAttribute[]> {
    const allAttributes = await this.getAttributes();
    return allAttributes.filter(attr => types.includes(attr.type));
  }

  /**
   * Search attributes by name/code
   */
  async searchAttributes(query: string): Promise<AkeneoAttribute[]> {
    const allAttributes = await this.getAttributes();
    const lowerQuery = query.toLowerCase();
    
    return allAttributes.filter(attr => 
      attr.code.toLowerCase().includes(lowerQuery) ||
      this.getAttributeLabel(attr).toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Get attribute by code
   */
  async getAttribute(code: string): Promise<AkeneoAttribute | null> {
    const allAttributes = await this.getAttributes();
    return allAttributes.find(attr => attr.code === code) || null;
  }
}

// Singleton instance
let akeneoIntegrationServiceInstance: AkeneoIntegrationService | null = null;

/**
 * Get singleton instance of Akeneo integration service
 */
export function getAkeneoIntegrationService(): AkeneoIntegrationService {
  if (!akeneoIntegrationServiceInstance) {
    akeneoIntegrationServiceInstance = new AkeneoIntegrationService();
  }
  return akeneoIntegrationServiceInstance;
} 