/**
 * Static LLM configuration constants
 * These are compile-time constants that don't change based on user settings
 */

export const LLM_CONSTANTS = {
  // Temperature constraints
  TEMPERATURE: {
    MIN: 0,
    MAX: 2,
    STEP: 0.1,
    FALLBACK: 1, // Used when service is unavailable
  },

  // Default concurrency limits
  CONCURRENCY: {
    MIN: 1,
    MAX: 10,
    DEFAULT: 3,
  },

  // Batch processing
  BATCH: {
    DEFAULT_SIZE: 100,
    MIN_SIZE: 1,
    MAX_SIZE: 1000,
  },

  // Cache settings
  CACHE: {
    DEFAULT_TTL: 3600, // 1 hour in seconds
    MAX_KEY_LENGTH: 250,
  },

  // API timeouts and retries
  API: {
    TIMEOUT_MS: 30000, // 30 seconds
    MAX_RETRIES: 3,
    RETRY_DELAY_MS: 1000,
  },

  // UI constants
  UI: {
    DEBOUNCE_MS: 300,
    PAGINATION_SIZE: 10,
    MAX_PROMPT_LENGTH: 10000,
  },

  // Model providers
  PROVIDERS: {
    GROQ: 'groq' as const,
    OPENROUTER: 'openrouter' as const,
  },

  // Response format types
  RESPONSE_FORMATS: {
    JSON_OBJECT: 'json_object' as const,
    TEXT: 'text' as const,
  },

  // LLM call logging
  LOGGING: {
    MAX_LOG_ENTRIES: 1000,
    LOG_ROTATION_DAYS: 7,
  },
} as const;

// Type exports for better type safety
export type LLMProvider = typeof LLM_CONSTANTS.PROVIDERS.GROQ | typeof LLM_CONSTANTS.PROVIDERS.OPENROUTER;
export type ResponseFormat = typeof LLM_CONSTANTS.RESPONSE_FORMATS.JSON_OBJECT | typeof LLM_CONSTANTS.RESPONSE_FORMATS.TEXT; 