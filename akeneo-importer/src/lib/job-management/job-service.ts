export interface JobMetadata {
  id: string;
  name: string;
  status: 'draft' | 'data-uploaded' | 'processing' | 'completed' | 'error';
  original_filename: string;
  source_type: 'file_upload' | 'google_sheets' | 'direct_url';
  created_at: string;
  updated_at: string;
  last_accessed: string;
  row_count: number;
  column_count: number;
  processed_rows: number;
  configuration_snapshot?: any[];
  transformation_modes?: Record<string, string>;
  progress: {
    total_cells: number;
    processed_cells: number;
    failed_cells: number;
    estimated_completion: string | null;
  };
}

export interface JobFilter {
  status?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
}

export interface CreateJobRequest {
  name: string;
  source_type: 'file_upload' | 'google_sheets' | 'direct_url';
  original_filename?: string;
  google_sheet_url?: string;
}

export interface UpdateJobRequest {
  name?: string;
  status?: JobMetadata['status'];
}

export class JobService {
  private baseUrl = '/api/jobs';

  async createJob(data: CreateJobRequest): Promise<JobMetadata> {
    console.log('🔧 JobService: Creating job with data:', data);
    
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to create job: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('📦 JobService: Received response:', result);
    
    // Handle the API response format: { success: true, job_id: string, job: JobMetadata }
    if (result.success && result.job) {
      console.log('✅ JobService: Returning job data:', result.job);
      return result.job;
    } else {
      // Fallback for direct JobMetadata response
      console.log('⚠️ JobService: Using fallback response format');
      return result;
    }
  }

  async listJobs(filter?: JobFilter, page: number = 1, limit: number = 20): Promise<{
    jobs: JobMetadata[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filter?.status) {
      params.append('status', filter.status);
    }
    if (filter?.search) {
      params.append('search', filter.search);
    }
    if (filter?.dateRange) {
      params.append('dateStart', filter.dateRange.start);
      params.append('dateEnd', filter.dateRange.end);
    }

    const response = await fetch(`${this.baseUrl}?${params}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch jobs: ${response.statusText}`);
    }

    return response.json();
  }

  async getJob(jobId: string): Promise<JobMetadata> {
    const response = await fetch(`${this.baseUrl}/${jobId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch job: ${response.statusText}`);
    }

    return response.json();
  }

  async updateJob(jobId: string, data: UpdateJobRequest): Promise<JobMetadata> {
    const response = await fetch(`${this.baseUrl}/${jobId}/metadata`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to update job: ${response.statusText}`);
    }

    return response.json();
  }

  async deleteJob(jobId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${jobId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`Failed to delete job: ${response.statusText}`);
    }
  }

  async duplicateJob(jobId: string, newName?: string): Promise<JobMetadata> {
    const response = await fetch(`${this.baseUrl}/${jobId}/duplicate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: newName }),
    });

    if (!response.ok) {
      throw new Error(`Failed to duplicate job: ${response.statusText}`);
    }

    return response.json();
  }

  async renameJob(jobId: string, newName: string): Promise<JobMetadata> {
    const response = await fetch(`${this.baseUrl}/${jobId}/rename`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: newName }),
    });

    if (!response.ok) {
      throw new Error(`Failed to rename job: ${response.statusText}`);
    }

    return response.json();
  }

  async archiveJob(jobId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${jobId}/archive`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error(`Failed to archive job: ${response.statusText}`);
    }
  }

  async restoreJob(jobId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${jobId}/restore`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error(`Failed to restore job: ${response.statusText}`);
    }
  }

  async exportJobToExcel(jobId: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/${jobId}/export-excel`);
    
    if (!response.ok) {
      throw new Error(`Failed to export job to Excel: ${response.statusText}`);
    }

    return response.blob();
  }

  async exportJobToJson(jobId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/${jobId}/export-json`);
    
    if (!response.ok) {
      throw new Error(`Failed to export job to JSON: ${response.statusText}`);
    }

    return response.json();
  }

  async bulkDelete(jobIds: string[]): Promise<void> {
    const response = await fetch(`${this.baseUrl}/bulk-delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ job_ids: jobIds }),
    });

    if (!response.ok) {
      throw new Error(`Failed to bulk delete jobs: ${response.statusText}`);
    }
  }

  async bulkArchive(jobIds: string[]): Promise<void> {
    const response = await fetch(`${this.baseUrl}/bulk-archive`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ job_ids: jobIds }),
    });

    if (!response.ok) {
      throw new Error(`Failed to bulk archive jobs: ${response.statusText}`);
    }
  }

  async bulkExport(jobIds: string[]): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/bulk-export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ job_ids: jobIds }),
    });

    if (!response.ok) {
      throw new Error(`Failed to bulk export jobs: ${response.statusText}`);
    }

    return response.blob();
  }
}

// Export a singleton instance
export const jobService = new JobService(); 