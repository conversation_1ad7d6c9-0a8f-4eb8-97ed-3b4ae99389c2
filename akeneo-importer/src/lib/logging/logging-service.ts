import fs from 'fs';
import path from 'path';

export class LoggingService {
  private logFilePath: string;
  private writeStream: fs.WriteStream;
  private static logsDir = path.join(process.cwd(), 'logs');

  constructor(operationId: string) {
    // Ensure logs directory exists
    if (!fs.existsSync(LoggingService.logsDir)) {
      fs.mkdirSync(LoggingService.logsDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const logFileName = `transform-${timestamp}-${operationId}.jsonl`;
    this.logFilePath = path.join(LoggingService.logsDir, logFileName);

    this.writeStream = fs.createWriteStream(this.logFilePath, { flags: 'a' });

    this.log({
      event: 'log_session_started',
      operationId,
      timestamp: new Date().toISOString(),
      logFile: this.logFilePath,
    });
  }

  public log(data: Record<string, any>): void {
    const logEntry = {
      ...data,
      timestamp: new Date().toISOString(),
    };
    this.writeStream.write(JSON.stringify(logEntry) + '\n');
  }

  public end(): void {
    this.log({ event: 'log_session_ended' });
    this.writeStream.end();
  }
} 