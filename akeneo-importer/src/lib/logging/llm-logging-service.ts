import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

export interface LLMLogEntry {
  id: string;
  timestamp: string;
  model: string;
  provider: string;
  messages: any[];
  response?: string;
  reasoning?: string;
  answer?: string;
  fromCache: boolean;
  duration?: number;
  temperature?: number;
  cacheKey?: string;
  error?: any;
  metadata?: any;
}

export class LLMLoggingService {
  private static logsDir = path.join(process.cwd(), 'logs');
  private static instance: LLMLoggingService;
  
  public static getInstance(): LLMLoggingService {
    if (!LLMLoggingService.instance) {
      LLMLoggingService.instance = new LLMLoggingService();
    }
    return LLMLoggingService.instance;
  }

  constructor() {
    // Ensure logs directory exists
    if (!fs.existsSync(LLMLoggingService.logsDir)) {
      fs.mkdirSync(LLMLoggingService.logsDir, { recursive: true });
    }
  }

  /**
   * Generate a unique ID for an LLM call
   */
  public generateCallId(): string {
    return crypto.randomBytes(4).toString('hex').toUpperCase();
  }

  /**
   * Log an LLM call with minimal terminal output and full file logging
   */
  public logLLMCall(entry: LLMLogEntry): void {
    // Show minimal terminal output
    const status = entry.fromCache ? '💾' : '🤖';
    const provider = entry.provider.toUpperCase();
    const duration = entry.duration ? ` (${entry.duration}ms)` : '';
    const temp = entry.temperature ? ` T=${entry.temperature}` : '';
    
    if (entry.error) {
      console.log(`❌ [${entry.id}] LLM call to ${provider} failed${duration}${temp}`);
    } else {
      console.log(`${status} [${entry.id}] LLM call to ${provider}${duration}${temp}`);
    }

    // Log full details to file
    this.writeToFile(entry);
  }

  /**
   * Write log entry to file
   */
  private writeToFile(entry: any): void {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const logFileName = `llm-calls-${today}.jsonl`;
    const logFilePath = path.join(LLMLoggingService.logsDir, logFileName);

    // Append to today's log file
    fs.appendFileSync(logFilePath, JSON.stringify(entry) + '\n');
  }

  /**
   * Get logs for a specific call ID
   */
  public async getCallLogs(callId: string): Promise<any[]> {
    const logFiles = fs.readdirSync(LLMLoggingService.logsDir)
      .filter(file => file.startsWith('llm-calls-') && file.endsWith('.jsonl'))
      .sort()
      .reverse(); // Start with most recent

    const entries: any[] = [];

    for (const file of logFiles) {
      const filePath = path.join(LLMLoggingService.logsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          const entry = JSON.parse(line);
          if (entry.id === callId) {
            entries.push(entry);
          }
        } catch (e) {
          // Skip invalid JSON lines
        }
      }
    }

    return entries.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }

  /**
   * Get recent LLM calls (for debugging/monitoring)
   */
  public async getRecentCalls(limit: number = 50): Promise<any[]> {
    const today = new Date().toISOString().split('T')[0];
    const logFileName = `llm-calls-${today}.jsonl`;
    const logFilePath = path.join(LLMLoggingService.logsDir, logFileName);

    if (!fs.existsSync(logFilePath)) {
      return [];
    }

    const content = fs.readFileSync(logFilePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());
    const entries: any[] = [];

    // Get the last N lines
    const recentLines = lines.slice(-limit);

    for (const line of recentLines) {
      try {
        entries.push(JSON.parse(line));
      } catch (e) {
        // Skip invalid JSON lines
      }
    }

    return entries.reverse(); // Most recent first
  }

  /**
   * Get the last X rows of logs across all log files for download
   */
  public async getLastLogs(limit: number = 100): Promise<any[]> {
    const logFiles = fs.readdirSync(LLMLoggingService.logsDir)
      .filter(file => file.startsWith('llm-calls-') && file.endsWith('.jsonl'))
      .sort()
      .reverse(); // Start with most recent files

    const allEntries: any[] = [];

    for (const file of logFiles) {
      const filePath = path.join(LLMLoggingService.logsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          const entry = JSON.parse(line);
          allEntries.push(entry);
        } catch (e) {
          // Skip invalid JSON lines
        }
      }

      // Stop reading files if we have enough entries
      if (allEntries.length >= limit * 2) {
        break;
      }
    }

    // Sort all entries by timestamp (most recent first)
    allEntries.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Return the last X entries
    return allEntries.slice(0, limit);
  }
} 