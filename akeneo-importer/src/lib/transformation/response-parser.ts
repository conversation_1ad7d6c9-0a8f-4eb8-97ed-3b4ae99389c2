// Response parsing utilities
export class ResponseParser {
  static extractAnswerFromResponse(response: string): { reasoning: string; answer: string } {
    // BULLETPROOF PARSER: Look for FINAL_ANSWER first (enforced by system prompt)
    const finalAnswerMatch = response.match(/FINAL_ANSWER:\s*(.+?)(?:\n|$)/i);
    
    if (finalAnswerMatch && finalAnswerMatch[1]) {
      const answer = finalAnswerMatch[1].trim();
      // Split response at the FINAL_ANSWER marker to get reasoning
      const splitIndex = response.search(/FINAL_ANSWER:/i);
      const reasoning = splitIndex > 0 ? response.substring(0, splitIndex).trim() : '';
      
      return {
        reasoning: reasoning || 'No reasoning provided',
        answer: answer
      };
    }

    // FALLBACK: Try other common patterns (for backward compatibility)
    const fallbackPatterns = [
      /ANSWER:\s*([^\n\r]*)/i,
      /Final Answer:\s*([^\n\r]*)/i,
      /Answer:\s*([^\n\r]*)/i,
    ];

    for (const pattern of fallbackPatterns) {
      const match = response.match(pattern);
      if (match && match[1] && match[1].trim().length > 0) {
        const answer = match[1].trim();
        const splitIndex = response.search(pattern);
        const reasoning = splitIndex > 0 ? response.substring(0, splitIndex).trim() : '';
        
        return {
          reasoning: reasoning || 'No reasoning provided',
          answer: answer
        };
      }
    }

    // LAST RESORT: Extract the last meaningful line
    const lines = response.split('\n').filter(line => line.trim());
    if (lines.length > 0) {
      // Look for the last line that seems like an answer
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        // Skip obviously non-answer lines
        if (line && 
            line.length > 0 && 
            line.length < 200 && // Reasonable answer length
            !line.includes('Based on') && 
            !line.includes('Therefore') &&
            !line.includes('In conclusion') &&
            !line.includes('Let me analyze') &&
            !line.includes('Looking at') &&
            !line.startsWith('<think>') &&
            !line.endsWith('</think>')) {
          
          // Clean up the answer (remove common formatting)
          let cleanAnswer = line
            .replace(/^\*+|\*+$/g, '') // Remove asterisks
            .replace(/^"+|"+$/g, '')   // Remove quotes
            .replace(/^'+|'+$/g, '')   // Remove single quotes
            .trim();
          
          if (cleanAnswer.length > 0) {
            const reasoning = lines.slice(0, i).join('\n').trim();
            return {
              reasoning: reasoning || 'No reasoning provided',
              answer: cleanAnswer
            };
          }
        }
      }
    }

    // ABSOLUTE FALLBACK: Use the entire response
    return {
      reasoning: response,
      answer: response.split('\n').filter(line => line.trim()).pop()?.trim() || response
    };
  }

  static generateStructuredPrompt(originalPrompt: string, showReasoning: boolean = true): string {
    const instructionText = showReasoning 
      ? `Please provide your reasoning process and then give your final answer. Format your response exactly as follows:

[Your detailed step-by-step reasoning and analysis here]

FINAL_ANSWER: [Your exact answer here]

Important: 
- Provide your reasoning first
- End with "FINAL_ANSWER: " followed by your exact answer
- Do not include any text after the final answer
- Keep the final answer concise and precise

Example:
Based on the product information provided, I need to analyze the delivery method and unit of measure. The product shows "Lieferhinweis" indicating direct delivery by truck, which suggests BESTELLWARE (dropshipping). The unit pattern is Stück/Stück/Stück based on the complete set nature of the product.

FINAL_ANSWER: AV000010`
      : `Please analyze the provided information and give your final answer. 

Think through the problem step by step, then end your response with:

FINAL_ANSWER: [Your exact answer here]

Important: Always end with "FINAL_ANSWER: " followed by your exact answer.`;

    return `${originalPrompt}

${instructionText}`;
  }
} 