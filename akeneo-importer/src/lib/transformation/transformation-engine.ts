import { RedisManager } from '@/lib/redis/redis-manager';
import { OpenRouterClient, GroqClient, ModelSelectionService } from './llm-client';
import { ProgressTracker, ProgressData } from './progress-tracker';
import { RedisDataStorage } from '@/lib/file-processing/redis-data-storage';
import { retryOnHttp500 } from '../utils';
import { REDIS_DB } from '@/types';
import type { ValidationConfig, RowData } from '@/types';
import { getPromptService } from './prompt-service';
import { UnifiedConfigurationService } from '../unified-configuration-service';
import { LoggingService } from '../logging/logging-service';

export type TransformationMode = '1to1' | 'ai_transform' | 'string_overwrite' | 'deactivated';

export interface TransformationConfig {
  mode: TransformationMode;
  sourceColumn?: string;        // For 1to1 mapping
  stringValue?: string;         // For string overwrite
  aiPrompt?: string;           // DEPRECATED: For AI transformation - will be replaced by column config
  validationConfig?: ValidationConfig;
  model?: string;              // Specific model for AI transformation
  temperature?: number;        // Temperature for AI transformations
  columnConfiguration?: any; // NEW: Pass full column configuration
}

export interface TransformationRequest {
  jobId: string;
  columnName: string;
  config: TransformationConfig;
  rowIndex?: number;           // For single cell transformation
  rowRange?: {                 // For bulk transformation
    start: number;
    end: number;
  };
  batchSize?: number;
  applyToExisting?: boolean;
  concurrency?: number;        // Number of concurrent workers (1-10)
  bulkOperationId?: string;    // For bulk operation cancellation tracking
  bypassCache?: boolean;       // NEW: To control caching behavior
  worksheetData?: any[];     // NEW: For job-less transformations
}

export interface TransformationResult {
  success: boolean;
  transformedValue?: any;
  transformedValues?: any[];   // For bulk operations
  fromCache: boolean;
  processingTime: number;
  taskId?: string;
  errors: string[];
  cacheKey?: string;
  renderedPrompt?: string;
  reasoning?: string; // To store the reasoning steps from the LLM
  redisStats: {
    cacheHits: number;
    cacheMisses: number;
  };
  workerStats?: {              // New for concurrent processing
    totalWorkers: number;
    activeWorkers: number;
    completedBatches: number;
    failedBatches: number;
  };
}

export interface CellContext {
  rowIndex: number;
  columnName: string;
  currentValue: any;
  rowData: RowData;
  sourceColumns: string[];
  validationData?: any[];
  jobId: string;
}

export interface WorkerBatch {
  id: string;
  startRow: number;
  endRow: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  workerId?: number;
  startTime?: number;
  completionTime?: number;
  error?: string;
}

export interface WorkerState {
  id: number;
  status: 'idle' | 'processing' | 'failed' | 'cancelled';
  currentBatch?: WorkerBatch;
  processedBatches: number;
  failedBatches: number;
  lastActivity: number;
}

export class TransformationEngine {
  private redis: any;
  private modelSelection: ModelSelectionService;
  private progressTracker: ProgressTracker;
  private dataStorage: RedisDataStorage;
  private promptService: any; // Add prompt service
  private configService: UnifiedConfigurationService; // Add config service

  constructor() {
    this.redis = RedisManager.getClient(REDIS_DB.JOBS);
    this.modelSelection = new ModelSelectionService();
    this.progressTracker = new ProgressTracker();
    this.dataStorage = new RedisDataStorage();
    this.promptService = getPromptService(); // Initialize
    this.configService = UnifiedConfigurationService.getInstance(); // Initialize
  }

  private async checkBulkOperationCancellation(bulkOperationId?: string): Promise<boolean> {
    if (!bulkOperationId) {
      console.log(`🔍 [CANCEL_CHECK] No bulkOperationId provided, skipping cancellation check`);
      return false;
    }
    
    try {
      const operationKey = `bulk_operation:${bulkOperationId}`;
      console.log(`🔍 [CANCEL_CHECK] Checking cancellation for bulk operation ${bulkOperationId} (key: ${operationKey})`);
      
      const operationData = await this.redis.get(operationKey);
      
      if (!operationData) {
        console.log(`⚠️ [CANCEL_CHECK] No operation data found for ${bulkOperationId}`);
        return false;
      }
      
      const operation = JSON.parse(operationData);
      const isCancelled = operation.status === 'cancelled';
      console.log(`🔍 [CANCEL_CHECK] Bulk operation ${bulkOperationId} status: ${operation.status}, cancelled: ${isCancelled}`);
      
      return isCancelled;
    } catch (error) {
      console.error(`❌ [CANCEL_CHECK] Error checking bulk operation cancellation for ${bulkOperationId}:`, error);
      return false;
    }
  }

  private async getLLMClient(): Promise<OpenRouterClient | GroqClient> {
    return await this.modelSelection.getLLMClient();
  }

  async transformSingleCell(request: TransformationRequest): Promise<TransformationResult> {
    const { jobId, columnName, rowIndex, config, bypassCache = false, worksheetData, bulkOperationId } = request;
    
    // NEW: Check for bulk operation cancellation before processing
    if (bulkOperationId && await this.checkBulkOperationCancellation(bulkOperationId)) {
      console.log(`🛑 [SINGLE_CELL] Bulk operation ${bulkOperationId} is cancelled, aborting single cell transform`);
      return {
        success: false,
        fromCache: false,
        processingTime: 0,
        errors: ['Operation cancelled'],
        redisStats: { cacheHits: 0, cacheMisses: 0 }
      };
    }

    if (rowIndex === undefined) {
      throw new Error('Row index is required for single cell transformation');
    }

    let sourceColumns: string[];
    let currentValue: any;
    let rowData: RowData;

    if (worksheetData && worksheetData.length > 0) {
      // Job-less mode: use provided worksheet data
      if (rowIndex >= worksheetData.length) {
        throw new Error(`Row index ${rowIndex} is out of range for provided worksheet data`);
      }
      
      rowData = worksheetData[rowIndex];
      sourceColumns = Object.keys(rowData);
      currentValue = rowData[columnName];
    } else {
      // Job-based mode: get data from Redis
      if (!jobId) {
        throw new Error('Job ID is required when no worksheet data is provided');
      }
      
      sourceColumns = await this.dataStorage.getJobColumns(jobId) || [];
      const fetchedRowData = await this.getRowData(jobId, rowIndex);
      
      if (!fetchedRowData) {
        throw new Error(`No data found for row ${rowIndex} in job ${jobId}`);
      }
      
      rowData = fetchedRowData;
      currentValue = rowData[columnName];
    }

    const validationData = config.validationConfig ? await this.getValidationData(config.validationConfig) : undefined;

    const context: CellContext = {
      rowIndex,
      columnName,
      currentValue,
      rowData,
      sourceColumns,
      validationData,
      jobId: jobId || 'job-less'
    };

    const result = await this.applyTransformation(context, config, bulkOperationId, bypassCache, worksheetData);

    // Store result if in job-based mode and transformation was successful
    if (result.success && jobId && result.transformedValue !== undefined) {
      await this.storeCellValue(jobId, rowIndex, columnName, result.transformedValue, result.cacheKey);
    }

    return result;
  }

  async transformBulk(request: TransformationRequest): Promise<TransformationResult> {
    const { jobId, columnName, config, rowRange, batchSize = 100, concurrency = 3, bulkOperationId, bypassCache = false } = request;
    
    console.log(`🚀 [BULK_START] Starting bulk transformation for job ${jobId}, column ${columnName}`);
    console.log(`🚀 [BULK_START] bulkOperationId: ${bulkOperationId}, bypassCache: ${bypassCache}`);
    console.log(`🚀 [BULK_START] rowRange: ${JSON.stringify(rowRange)}, batchSize: ${batchSize}, concurrency: ${concurrency}`);

    const maxConcurrency = Math.min(Math.max(concurrency, 1), 10);
    const totalRows = rowRange ? (rowRange.end - rowRange.start + 1) : await this.getJobRowCount(jobId);
    
    // Create task for progress tracking
    const taskId = await this.progressTracker.createTask(
      jobId,
      totalRows,
      columnName,
      config.mode
    );
    console.log(`📊 [BULK_PROGRESS] Created task ${taskId} for ${totalRows} rows`);

    try {
      // Create logger for this transformation session
      const logger = bulkOperationId ? new LoggingService(bulkOperationId) : undefined;

      // Check for immediate cancellation
      if (await this.progressTracker.isCancelled(taskId)) {
        console.log(`❌ [BULK_CANCELLED] Task ${taskId} was already cancelled before starting`);
        return { 
          success: false, 
          errors: ['Task was cancelled before starting'], 
          taskId,
          fromCache: false,
          processingTime: 0,
          redisStats: { cacheHits: 0, cacheMisses: 0 }
        };
      }

      if (bulkOperationId && await this.checkBulkOperationCancellation(bulkOperationId)) {
        console.log(`❌ [BULK_CANCELLED] Bulk operation ${bulkOperationId} was already cancelled before starting`);
        return { 
          success: false, 
          errors: ['Bulk operation was cancelled before starting'], 
          taskId,
          fromCache: false,
          processingTime: 0,
          redisStats: { cacheHits: 0, cacheMisses: 0 }
        };
      }

      await this.progressTracker.updateProgress(taskId, {
        status: 'processing',
        currentOperation: `Starting bulk ${config.mode} transformation for column ${columnName} with ${maxConcurrency} workers`
      });

      const shouldUseConcurrentProcessing = maxConcurrency > 1;
      
      console.log(`🔍 Processing decision: ${shouldUseConcurrentProcessing ? 'CONCURRENT' : 'SEQUENTIAL'} (${maxConcurrency} workers, ${totalRows} rows, mode: ${config.mode})`);
      
      const result = shouldUseConcurrentProcessing
        ? await this.processBulkTransformationConcurrent(jobId, columnName, config, taskId, rowRange, batchSize, maxConcurrency, logger, bulkOperationId, bypassCache)
        : await this.processBulkTransformation(jobId, columnName, config, taskId, rowRange, batchSize, logger, bulkOperationId, bypassCache);
      
      await this.progressTracker.completeTask(taskId, result.success);
      logger?.end();
      
      return {
        ...result,
        taskId
      };
    } catch (error) {
      console.error('Bulk transformation error:', error);
      await this.progressTracker.completeTask(taskId, false);
      
      return {
        success: false,
        fromCache: false,
        processingTime: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        redisStats: { cacheHits: 0, cacheMisses: 0 },
        taskId
      };
    }
  }

  private async processBulkTransformation(
    jobId: string,
    columnName: string,
    config: TransformationConfig,
    taskId: string,
    rowRange?: { start: number; end: number },
    batchSize: number = 100,
    logger?: LoggingService,
    bulkOperationId?: string,
    bypassCache: boolean = false
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    let processedRows = 0;
    let failedRows = 0;
    const errors: string[] = [];
    let totalCacheHits = 0;
    let totalCacheMisses = 0;

    const totalRows = await this.getJobRowCount(jobId);
    const start = rowRange?.start || 0;
    const end = rowRange?.end || totalRows - 1;

    for (let batchStart = start; batchStart <= end; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize - 1, end);

      if (await this.progressTracker.isCancelled(taskId)) {
        console.log(`[TASK:${taskId}] Cancellation detected in sequential processor. Halting.`);
        errors.push('Transformation cancelled by user.');
        break;
      }

      // Also check for bulk operation cancellation
      if (await this.checkBulkOperationCancellation(bulkOperationId)) {
        console.log(`[BULK_OP:${bulkOperationId}] Bulk operation cancellation detected in sequential processor. Halting.`);
        errors.push('Bulk operation cancelled by user.');
        break;
      }

      try {
        await this.progressTracker.updateProgress(taskId, {
          currentOperation: `Processing rows ${batchStart}-${batchEnd}`,
          currentBatch: Math.floor(batchStart / batchSize) + 1,
          processedCells: processedRows,
          failedCells: failedRows,
          cacheHits: totalCacheHits,
          cacheMisses: totalCacheMisses
        });

        const batchResult = await this.processBatch(jobId, columnName, config, batchStart, batchEnd, logger, bypassCache, bulkOperationId, taskId);
        
        processedRows += batchResult.processedCount;
        failedRows += batchResult.failedCount;
        errors.push(...batchResult.errors);
        totalCacheHits += batchResult.cacheHits;
        totalCacheMisses += batchResult.cacheMisses;

        await this.progressTracker.updateProgress(taskId, {
          processedCells: processedRows,
          failedCells: failedRows,
          cacheHits: totalCacheHits,
          cacheMisses: totalCacheMisses
        });

      } catch (batchError) {
        console.error(`Batch ${batchStart}-${batchEnd} failed:`, batchError);
        const batchErrorCount = batchEnd - batchStart + 1;
        failedRows += batchErrorCount;
        errors.push(`Batch failed: ${batchError instanceof Error ? batchError.message : 'Unknown error'}`);
      }
    }

    return {
      success: failedRows === 0,
      processingTime: Date.now() - startTime,
      fromCache: totalCacheHits > 0,
      errors: errors,
      redisStats: {
        cacheHits: totalCacheHits,
        cacheMisses: totalCacheMisses
      }
    };
  }

  private async processBatch(
    jobId: string,
    columnName: string,
    config: TransformationConfig,
    startRow: number,
    endRow: number,
    logger?: LoggingService,
    bypassCache: boolean = false,
    bulkOperationId?: string,
    taskId?: string
  ): Promise<{
    processedCount: number;
    failedCount: number;
    errors: string[];
    cacheHits: number;
    cacheMisses: number;
  }> {
    const batchData = await this.getBatchData(jobId, startRow, endRow);
    const sourceColumns = await this.dataStorage.getJobColumns(jobId) || [];
    const validationData = config.validationConfig ? await this.getValidationData(config.validationConfig) : undefined;
    
    console.log(`🔄 [BATCH_START] Processing batch rows ${startRow}-${endRow} (${batchData.length} rows)`);
    console.log(`🔄 [BATCH_START] bulkOperationId: ${bulkOperationId}, taskId: ${taskId}, bypassCache: ${bypassCache}`);
    
    let processedCount = 0;
    let failedCount = 0;
    let errors: string[] = [];
    let cacheHits = 0;
    let cacheMisses = 0;

    // Process each row individually with cancellation checks
    for (let index = 0; index < batchData.length; index++) {
      const rowData = batchData[index];
      const actualRowIndex = startRow + index;

      // Check for cancellation before each row
      console.log(`🔍 [CANCEL_CHECK] Checking cancellation for row ${actualRowIndex}`);
      
      if (taskId && await this.progressTracker.isCancelled(taskId)) {
        console.log(`❌ [CANCELLED] Task ${taskId} cancelled during batch processing at row ${actualRowIndex}`);
        break;
      }

      if (bulkOperationId && await this.checkBulkOperationCancellation(bulkOperationId)) {
        console.log(`❌ [CANCELLED] Bulk operation ${bulkOperationId} cancelled during batch processing at row ${actualRowIndex}`);
        break;
      }

      console.log(`✅ [CANCEL_CHECK] No cancellation detected, processing row ${actualRowIndex}`);

      try {
        const context: CellContext = {
          rowIndex: actualRowIndex,
          columnName,
          currentValue: rowData[columnName],
          rowData,
          sourceColumns,
          validationData,
          jobId
        };

        console.log(`🔄 [TRANSFORM_ROW] Starting transformation for row ${actualRowIndex}, column ${columnName}`);
        const result = await this.applyTransformation(context, config, bulkOperationId, bypassCache, undefined, logger);
        console.log(`✅ [TRANSFORM_ROW] Completed transformation for row ${actualRowIndex}: success=${result.success}, fromCache=${result.fromCache}`);

        if (result.success) {
          processedCount++;
          await this.storeCellValue(jobId, actualRowIndex, columnName, result.transformedValue, result.cacheKey);
        } else {
          failedCount++;
          errors.push(...result.errors);
        }

        // Update cache stats
        cacheHits += result.redisStats.cacheHits;
        cacheMisses += result.redisStats.cacheMisses;

      } catch (error) {
        console.error(`❌ [TRANSFORM_ERROR] Error processing row ${actualRowIndex}:`, error);
        failedCount++;
        errors.push(`Row ${actualRowIndex}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    console.log(`🏁 [BATCH_COMPLETE] Batch ${startRow}-${endRow} complete: ${processedCount} processed, ${failedCount} failed`);
    return { processedCount, failedCount, errors, cacheHits, cacheMisses };
  }
  
  private async processBulkTransformationConcurrent(
    jobId: string,
    columnName: string,
    config: TransformationConfig,
    taskId: string,
    rowRange?: { start: number; end: number },
    batchSize: number = 100,
    maxConcurrency: number = 10,
    logger?: LoggingService,
    bulkOperationId?: string,
    bypassCache: boolean = false
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    const totalRows = await this.getJobRowCount(jobId);
    const start = rowRange?.start || 0;
    const end = rowRange?.end || totalRows - 1;

    const batches: WorkerBatch[] = [];
    for (let i = start; i <= end; i += batchSize) {
      batches.push({
        id: `${taskId}-${i}`,
        startRow: i,
        endRow: Math.min(i + batchSize - 1, end),
        status: 'pending'
      });
    }

    const workers: WorkerState[] = Array.from({ length: maxConcurrency }, (_, i) => ({
      id: i,
      status: 'idle',
      processedBatches: 0,
      failedBatches: 0,
      lastActivity: Date.now()
    }));

    const batchStateKey = `task:${taskId}:batches`;
    await this.redis.set(batchStateKey, JSON.stringify(batches));

    let totalProcessed = 0;
    let totalFailed = 0;
    let totalCacheHits = 0;
    let totalCacheMisses = 0;
    const allErrors: string[] = [];

    const workerPromises = workers.map(worker => 
      this.processWorkerBatches(
        jobId,
        columnName,
        config,
        taskId,
        worker,
        batches,
        batchStateKey,
        logger,
        bulkOperationId,
        bypassCache
      )
    );
    
    const results = await Promise.all(workerPromises);
    
    for (const result of results) {
      totalProcessed += result.processedCount;
      totalFailed += result.failedCount;
      allErrors.push(...result.errors);
      totalCacheHits += result.cacheHits;
      totalCacheMisses += result.cacheMisses;
    }
    
    await this.redis.del(batchStateKey);

    return {
      success: totalFailed === 0,
      processingTime: Date.now() - startTime,
      fromCache: totalCacheHits > 0,
      errors: allErrors,
      redisStats: {
        cacheHits: totalCacheHits,
        cacheMisses: totalCacheMisses
      }
    };
  }

  private async processWorkerBatches(
    jobId: string,
    columnName: string,
    config: TransformationConfig,
    taskId: string,
    worker: WorkerState,
    batches: WorkerBatch[],
    batchStateKey: string,
    logger?: LoggingService,
    bulkOperationId?: string,
    bypassCache: boolean = false
  ): Promise<{
    processedCount: number;
    failedCount: number;
    errors: string[];
    cacheHits: number;
    cacheMisses: number;
  }> {
    let processedCount = 0;
    let failedCount = 0;
    let cacheHits = 0;
    let cacheMisses = 0;
    const errors: string[] = [];
    
    while (true) {
      // Check for cancellation before getting a new batch
      if (await this.progressTracker.isCancelled(taskId)) {
        console.log(`[TASK:${taskId}] Cancellation detected in worker ${worker.id}. Halting.`);
        worker.status = 'cancelled';
        break;
      }

      // Also check for bulk operation cancellation
      if (bulkOperationId && await this.checkBulkOperationCancellation(bulkOperationId)) {
        console.log(`[BULK_OP:${bulkOperationId}] Bulk operation cancellation detected in worker ${worker.id}. Halting.`);
        worker.status = 'cancelled';
        break;
      }

      let batchToProcess: WorkerBatch | null = null;
      try {
        const currentBatchesRaw = await this.redis.get(batchStateKey);
        if (!currentBatchesRaw) break;

        const currentBatches: WorkerBatch[] = JSON.parse(currentBatchesRaw);
        const pendingBatchIndex = currentBatches.findIndex(b => b.status === 'pending');

        if (pendingBatchIndex === -1) break;

        currentBatches[pendingBatchIndex].status = 'processing';
        currentBatches[pendingBatchIndex].workerId = worker.id;
        await this.redis.set(batchStateKey, JSON.stringify(currentBatches));
        
        batchToProcess = currentBatches[pendingBatchIndex];
      } catch (error) {
        console.error(`Worker ${worker.id} failed to get batch:`, error);
        continue;
      }
      
      if (batchToProcess) {
        try {
          const batchResult = await this.processBatch(
            jobId,
            columnName,
            config,
            batchToProcess.startRow,
            batchToProcess.endRow,
            logger,
            bypassCache,
            bulkOperationId,
            taskId
          );
          
          processedCount += batchResult.processedCount;
          failedCount += batchResult.failedCount;
          cacheHits += batchResult.cacheHits;
          cacheMisses += batchResult.cacheMisses;
          errors.push(...batchResult.errors);

        } catch (error) {
          const batchErrorCount = batchToProcess.endRow - batchToProcess.startRow + 1;
          failedCount += batchErrorCount;
          errors.push(`Batch ${batchToProcess.id} failed`);
        }
      }
    }
    
    return { processedCount, failedCount, errors, cacheHits, cacheMisses };
  }

  private async applyTransformation(
    context: CellContext,
    config: TransformationConfig,
    bulkOperationId?: string,
    bypassCache: boolean = false, // NEW: Control cache
    worksheetData?: any[], // NEW: Pass worksheet data for context
    logger?: LoggingService
  ): Promise<TransformationResult> {
    // Check for cancellation before starting
    if (await this.checkBulkOperationCancellation(bulkOperationId)) {
      return {
        success: false,
        fromCache: false,
        processingTime: 0,
        errors: ['Operation cancelled'],
        redisStats: { cacheHits: 0, cacheMisses: 0 }
      };
    }

    switch (config.mode) {
      case '1to1':
        return this.apply1to1Mapping(context, config);
      case 'ai_transform':
        return this.applyAITransformation(context, config, bulkOperationId, bypassCache, worksheetData, logger);
      case 'string_overwrite':
        return this.applyStringOverwrite(context, config);
      case 'deactivated':
        return {
          success: true,
          transformedValue: context.currentValue,
          fromCache: false,
          processingTime: 0,
          errors: [],
          redisStats: { cacheHits: 0, cacheMisses: 0 }
        };
      default:
        return {
          success: false,
          fromCache: false,
          processingTime: 0,
          errors: [`Unknown transformation mode: ${config.mode}`],
          redisStats: { cacheHits: 0, cacheMisses: 0 }
        };
    }
  }

  private async apply1to1Mapping(
    context: CellContext,
    config: TransformationConfig
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    
    if (!config.sourceColumn) {
      throw new Error('Source column is required for 1-to-1 mapping');
    }

    const transformedValue = context.rowData[config.sourceColumn];
    
    // Apply validation if configured
    if (config.validationConfig && context.validationData) {
      const isValid = this.validateValue(transformedValue, context.validationData, config.validationConfig);
      if (!isValid) {
        throw new Error(`Mapped value '${transformedValue}' is not valid`);
      }
    }

    return {
      success: true,
      transformedValue,
      fromCache: false,
      processingTime: Date.now() - startTime,
      errors: [],
      redisStats: { cacheHits: 0, cacheMisses: 0 }
    };
  }

  private async applyAITransformation(
    context: CellContext,
    config: TransformationConfig,
    bulkOperationId?: string,
    bypassCache: boolean = false, // NEW: Control cache
    worksheetData?: any[], // NEW: Pass worksheet data for context
    logger?: LoggingService
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    const redisStats = { cacheHits: 0, cacheMisses: 0 };
    
    console.log(`🤖 [AI_TRANSFORM] Starting AI transformation for job=${context.jobId}, column=${context.columnName}, row=${context.rowIndex}, bulkOp=${bulkOperationId}`);
    
    // NEW: Check for bulk operation cancellation before LLM call
    if (bulkOperationId && await this.checkBulkOperationCancellation(bulkOperationId)) {
      console.log(`🛑 [AI_TRANSFORM] Bulk operation ${bulkOperationId} is cancelled, aborting AI transformation`);
      return {
        success: false,
        fromCache: false,
        processingTime: Date.now() - startTime,
        errors: ['Operation cancelled'],
        redisStats
      };
    }
    
    const llmClient = await this.getLLMClient();
    
    // Initialize variables that need to be accessible in catch block
    let messages: any[] = [];
    let llmLogger: any;
    let callId: string = '';

    try {
      // NEW: Use the advanced prompt rendering flow from ai-transform endpoint
      const template = await this.promptService.getDefaultPrompt();
      
      const promptContext = await this.promptService.buildContextFromJobData(
        context.jobId,
        context.columnName,
        context.rowIndex,
        context.rowData,
        {
          columnConfiguration: config.columnConfiguration,
          worksheetData: worksheetData
        }
      );
      
      const renderResult = await this.promptService.renderPromptWithContext(template, promptContext);
      const finalPrompt = renderResult.renderedPrompt;

      if (renderResult.unreplacedPlaceholders.length > 0) {
        console.warn('⚠️ Unreplaced placeholders remain:', renderResult.unreplacedPlaceholders);
      }

      const systemPrompt = await this.promptService.getSystemPrompt();

      messages = [
        { role: "system" as const, content: systemPrompt },
        { role: "user" as const, content: finalPrompt }
      ];

      // Use the new LLM logging service for detailed logging
      const { LLMLoggingService } = await import('../logging/llm-logging-service');
      llmLogger = LLMLoggingService.getInstance();
      callId = llmLogger.generateCallId();

      console.log(`🔄 [AI_TRANSFORM] Making direct LLM call (NO HTTP) for ${context.columnName} row ${context.rowIndex}`);

      if ('chatStructured' in llmClient) {
        const modelService = new ModelSelectionService();
        const temperature = await modelService.getDefaultTemperature('groq');
        console.log(`🔄 [${callId}] Starting LLM call to GROQ (${config.model || 'default'}) with temperature ${temperature}`);
        
        const structuredResult = await (llmClient as GroqClient).chatStructured(
          messages,
          config.model,
          bypassCache,
          true, // show_reasoning
          temperature,
          logger
        );

        console.log(`✅ [AI_TRANSFORM] Direct LLM call completed for ${context.columnName} row ${context.rowIndex} - no HTTP involved`);

        // Log the completed LLM call with all details in one entry
        llmLogger.logLLMCall({
          id: callId,
          timestamp: new Date().toISOString(),
          model: config.model || 'default',
          provider: 'groq',
          messages,
          response: structuredResult.answer,
          reasoning: structuredResult.reasoning,
          fromCache: structuredResult.metadata?.from_cache || false,
          duration: Date.now() - startTime,
          temperature,
          metadata: {
            ...structuredResult.metadata,
            jobId: context.jobId,
            columnName: context.columnName,
            rowIndex: context.rowIndex,
            bulkOperationId,
            systemPrompt: messages.find(m => m.role === 'system')?.content,
            userPrompt: messages.find(m => m.role === 'user')?.content
          }
        });

        redisStats.cacheHits += structuredResult.metadata?.from_cache ? 1 : 0;
        redisStats.cacheMisses += structuredResult.metadata?.from_cache ? 0 : 1;
        
        return {
          success: true,
          transformedValue: structuredResult.answer,
          fromCache: structuredResult.metadata?.from_cache || false,
          processingTime: Date.now() - startTime,
          errors: [],
          redisStats: redisStats,
          renderedPrompt: finalPrompt,
          reasoning: structuredResult.reasoning
        };
      } else {
        const modelService = new ModelSelectionService();
        const temperature = await modelService.getDefaultTemperature('openrouter');
        console.log(`🔄 [${callId}] Starting LLM call to OPENROUTER (${config.model || 'default'}) with temperature ${temperature}`);
        
        // Fallback for other clients if needed
        const response = await llmClient.chat(messages, config.model, true, bypassCache, temperature);
        
        console.log(`✅ [AI_TRANSFORM] Direct LLM call completed for ${context.columnName} row ${context.rowIndex} - no HTTP involved`);
        
        // Log the completed LLM call with all details in one entry
        llmLogger.logLLMCall({
          id: callId,
          timestamp: new Date().toISOString(),
          model: config.model || 'default',
          provider: 'openrouter',
          messages,
          response: response,
          fromCache: false, // Cannot determine from this client
          duration: Date.now() - startTime,
          temperature,
          metadata: {
            jobId: context.jobId,
            columnName: context.columnName,
            rowIndex: context.rowIndex,
            bulkOperationId,
            systemPrompt: messages.find(m => m.role === 'system')?.content,
            userPrompt: messages.find(m => m.role === 'user')?.content
          }
        });
        
        // Basic parsing, assuming raw response is the answer
        return {
          success: true,
          transformedValue: response,
          fromCache: false, // Cannot determine from this client
          processingTime: Date.now() - startTime,
          errors: [],
          redisStats: redisStats,
          renderedPrompt: finalPrompt,
          reasoning: response
        };
      }
    } catch (error) {
      console.error(`❌ [AI_TRANSFORM] Error in direct LLM call for ${context.columnName} row ${context.rowIndex}:`, error);
      
      // Log the error using the LLM logging service
      const { LLMLoggingService } = await import('../logging/llm-logging-service');
      const llmLogger = LLMLoggingService.getInstance();
      const errorCallId = llmLogger.generateCallId();
      
      llmLogger.logLLMCall({
        id: errorCallId,
        timestamp: new Date().toISOString(),
        model: config.model || 'default',
        provider: 'unknown',
        messages,
        fromCache: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? { message: error.message, stack: error.stack } : String(error)
      });
      
      return {
        success: false,
        fromCache: false,
        processingTime: Date.now() - startTime,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        redisStats
      };
    }
  }

  private async applyStringOverwrite(
    context: CellContext,
    config: TransformationConfig
  ): Promise<TransformationResult> {
    const startTime = Date.now();

    if (config.stringValue === undefined) {
      throw new Error('String value is required for string overwrite');
    }

    if (config.validationConfig && context.validationData) {
      const isValid = this.validateValue(config.stringValue, context.validationData, config.validationConfig);
      if (!isValid) {
        throw new Error(`String value '${config.stringValue}' is not valid`);
      }
    }

    return {
      success: true,
      transformedValue: config.stringValue,
      fromCache: false,
      processingTime: Date.now() - startTime,
      errors: [],
      redisStats: { cacheHits: 0, cacheMisses: 0 }
    };
  }

  private buildContextualPrompt(prompt: string, context: CellContext): string {
    // THIS METHOD IS NOW DEPRECATED AND WILL BE REMOVED
    // The logic has been replaced by the more advanced prompt service flow
    return prompt
      .replace(/@value/g, String(context.currentValue ?? ''))
      .replace(/@column/g, context.columnName)
      .replace(/@rowData/g, JSON.stringify(context.rowData));
  }

  private validateValue(value: any, validationData: any[], config: ValidationConfig): boolean {
    if (!validationData || validationData.length === 0) {
      return true; // No data to validate against
    }

    switch (config.type) {
      case 'dropdown':
        return validationData.some(item => 
          item.Code === value || item.Beschreibung === value
        );
      case 'text':
        const str = String(value);
        if (config.min_length && str.length < config.min_length) return false;
        if (config.max_length && str.length > config.max_length) return false;
        if (config.pattern && !new RegExp(config.pattern).test(str)) return false;
        return true;
      case 'number':
        const num = Number(value);
        return !isNaN(num);
      case 'boolean':
        return typeof value === 'boolean' || value === 'true' || value === 'false';
      default:
        return true;
    }
  }

  private async getRowData(jobId: string, rowIndex: number): Promise<RowData | null> {
    try {
      const rows = await this.dataStorage.getRowRange(jobId, rowIndex, rowIndex + 1);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error(`Failed to get row data for job ${jobId}, row ${rowIndex}:`, error);
      return null;
    }
  }

  private async getBatchData(jobId: string, startRow: number, endRow: number): Promise<RowData[]> {
    try {
      return await this.dataStorage.getRowRange(jobId, startRow, endRow);
    } catch (error) {
      console.error(`Failed to get batch data for job ${jobId}, rows ${startRow}-${endRow}:`, error);
      return [];
    }
  }

  private async getJobRowCount(jobId: string): Promise<number> {
    try {
      const metadata = await this.dataStorage.getJobMetadata(jobId);
      return metadata?.row_count || 0;
    } catch (error) {
      console.error(`Failed to get row count for job ${jobId}:`, error);
      return 0;
    }
  }

  private async storeCellValue(jobId: string, rowIndex: number, columnName: string, value: any, cacheKey?: string): Promise<void> {
    try {
      const cellKey = `job:${jobId}:transform:${rowIndex}:${columnName}`;
      await this.redis.setex(cellKey, 1800, JSON.stringify({
        value,
        timestamp: Date.now(),
        rowIndex,
        columnName,
        jobId,
        cacheKey,
      }));

      const transformedDataKey = `job:${jobId}:transformed_data`;
      let transformedData: Record<string, Record<string, any>> = {};
      const existingData = await this.redis.get(transformedDataKey);
      if (existingData) {
        transformedData = JSON.parse(existingData);
      }
      
      if (!transformedData[rowIndex]) {
        transformedData[rowIndex] = {};
      }
      
      transformedData[rowIndex][columnName] = value;
      
      await this.redis.setex(transformedDataKey, 3600, JSON.stringify(transformedData));
    } catch (error) {
      console.error(`Failed to store cell value for job ${jobId}, row ${rowIndex}, column ${columnName}:`, error);
      throw error;
    }
  }

  private async getValidationData(config: ValidationConfig): Promise<any[]> {
    return [];
  }

  async getTransformationHistory(jobId: string): Promise<any[]> {
    try {
      const keys = await this.redis.keys(`job:${jobId}:transform:*`);
      const history = [];

      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          history.push(JSON.parse(data));
        }
      }

      return history.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error(`Failed to get transformation history for job ${jobId}:`, error);
      return [];
    }
  }

  async clearSingleRowCache(jobId: string, rowIndex: number): Promise<number> {
    try {
      const pattern = `job:${jobId}:transform:${rowIndex}:*`;
      const keys = await this.redis.keys(pattern);
      if (keys.length === 0) return 0;

      const llmClient = await this.getLLMClient();
      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          const parsedData = JSON.parse(data);
          if (parsedData.cacheKey) {
            await llmClient.clearCache(parsedData.cacheKey);
          }
        }
      }

      const clearedCount = await this.redis.del(keys);
      
      const transformedDataKey = `job:${jobId}:transformed_data`;
      const existingData = await this.redis.get(transformedDataKey);
      if (existingData) {
        const transformedData = JSON.parse(existingData);
        if (transformedData[rowIndex]) {
          delete transformedData[rowIndex];
          await this.redis.setex(transformedDataKey, 3600, JSON.stringify(transformedData));
        }
      }
      
      return clearedCount;
    } catch (error) {
      console.error(`Error clearing cache for row ${rowIndex}:`, error);
      throw error;
    }
  }

  async clearAllRowsCache(jobId: string): Promise<number> {
    try {
      const pattern = `job:${jobId}:transform:*`;
      const keys = await this.redis.keys(pattern);
      if (keys.length === 0) return 0;
      
      const llmClient = await this.getLLMClient();
      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          const parsedData = JSON.parse(data);
          if (parsedData.cacheKey) {
            await llmClient.clearCache(parsedData.cacheKey);
          }
        }
      }

      const clearedCount = await this.redis.del(keys);

      const transformedDataKey = `job:${jobId}:transformed_data`;
      await this.redis.del(transformedDataKey);

      return clearedCount;
    } catch (error) {
        console.error(`Error clearing all rows cache for job ${jobId}:`, error);
        throw error;
    }
  }
}

export default TransformationEngine; 