import { RedisManager } from '@/lib/redis/redis-manager';
import { REDIS_DB } from '@/types';

export interface ProgressData {
  taskId: string;
  jobId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  totalCells: number;
  processedCells: number;
  failedCells: number;
  currentBatch: number;
  totalBatches: number;
  estimatedCompletion?: string;
  processingRate: number; // cells per second
  startTime: number;
  lastUpdate: number;
  errors: string[];
  redisStats: {
    cacheHits: number;
    cacheMisses: number;
    memoryUsage: number;
  };
  currentOperation?: string;
  columnName?: string;
  transformationType?: string;
  workerStats?: {
    totalWorkers: number;
    activeWorkers: number;
    completedBatches: number;
    failedBatches: number;
  };
}

export interface TaskProgress {
  total: number;
  completed: number;
  failed: number;
  percentage: number;
  estimatedTimeRemaining: number;
  rate: number;
}

export class ProgressTracker {
  private redis: any;
  private publishChannel: string;

  constructor() {
    this.redis = RedisManager.getClient(REDIS_DB.TASKS);
    this.publishChannel = 'transformation:progress';
  }

  async createTask(
    jobId: string,
    totalCells: number,
    columnName?: string,
    transformationType?: string
  ): Promise<string> {
    const taskId = `task:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    
    const progressData: ProgressData = {
      taskId,
      jobId,
      status: 'queued',
      totalCells,
      processedCells: 0,
      failedCells: 0,
      currentBatch: 0,
      totalBatches: Math.ceil(totalCells / 100), // Assuming batch size of 100
      processingRate: 0,
      startTime: Date.now(),
      lastUpdate: Date.now(),
      errors: [],
      redisStats: {
        cacheHits: 0,
        cacheMisses: 0,
        memoryUsage: 0
      },
      columnName,
      transformationType
    };

    await this.redis.setex(
      `progress:${taskId}`,
      3600, // 1 hour TTL
      JSON.stringify(progressData)
    );

    // Add to job's task list
    await this.redis.lpush(`job:${jobId}:tasks`, taskId);
    await this.redis.expire(`job:${jobId}:tasks`, 3600);

    return taskId;
  }

  async updateProgress(
    taskId: string,
    updates: Partial<{
      processedCells: number;
      failedCells: number;
      currentBatch: number;
      status: ProgressData['status'];
      currentOperation: string;
      errors: string[];
      cacheHits: number;
      cacheMisses: number;
    }>
  ): Promise<void> {
    const progressKey = `progress:${taskId}`;
    const existingData = await this.redis.get(progressKey);
    
    if (!existingData) {
      throw new Error(`Task ${taskId} not found`);
    }

    const progressData: ProgressData = JSON.parse(existingData);
    const now = Date.now();

    // Update fields
    if (updates.processedCells !== undefined) {
      progressData.processedCells = updates.processedCells;
    }
    if (updates.failedCells !== undefined) {
      progressData.failedCells = updates.failedCells;
    }
    if (updates.currentBatch !== undefined) {
      progressData.currentBatch = updates.currentBatch;
    }
    if (updates.status !== undefined) {
      progressData.status = updates.status;
    }
    if (updates.currentOperation !== undefined) {
      progressData.currentOperation = updates.currentOperation;
    }
    if (updates.errors !== undefined) {
      progressData.errors = [...progressData.errors, ...updates.errors];
    }
    if (updates.cacheHits !== undefined) {
      progressData.redisStats.cacheHits += updates.cacheHits;
    }
    if (updates.cacheMisses !== undefined) {
      progressData.redisStats.cacheMisses += updates.cacheMisses;
    }

    // Calculate processing rate and ETA
    const elapsedTime = now - progressData.startTime;
    if (elapsedTime > 0 && progressData.processedCells > 0) {
      progressData.processingRate = (progressData.processedCells / elapsedTime) * 1000; // cells per second
      
      const remainingCells = progressData.totalCells - progressData.processedCells;
      if (progressData.processingRate > 0) {
        const estimatedSecondsRemaining = remainingCells / progressData.processingRate;
        progressData.estimatedCompletion = new Date(now + estimatedSecondsRemaining * 1000).toISOString();
      }
    }

    progressData.lastUpdate = now;

    // Save updated progress
    await this.redis.setex(progressKey, 3600, JSON.stringify(progressData));

    // Publish progress update
    await this.publishProgressUpdate(progressData);
  }

  async getProgress(taskId: string): Promise<ProgressData | null> {
    try {
      const data = await this.redis.get(`progress:${taskId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`Failed to get progress for task ${taskId}:`, error);
      return null;
    }
  }

  async getJobTasks(jobId: string): Promise<ProgressData[]> {
    try {
      const taskIds = await this.redis.lrange(`job:${jobId}:tasks`, 0, -1);
      const tasks: ProgressData[] = [];

      for (const taskId of taskIds) {
        const progress = await this.getProgress(taskId);
        if (progress) {
          tasks.push(progress);
        }
      }

      return tasks.sort((a, b) => b.startTime - a.startTime); // Most recent first
    } catch (error) {
      console.error(`Failed to get tasks for job ${jobId}:`, error);
      return [];
    }
  }

  async completeTask(taskId: string, success: boolean = true): Promise<void> {
    await this.updateProgress(taskId, {
      status: success ? 'completed' : 'failed'
    });
  }

  async cancelTask(taskId: string): Promise<void> {
    await this.updateProgress(taskId, {
      status: 'cancelled'
    });

    // Also cancel any concurrent batch processing for this task
    const batchStateKey = `batch_state:${taskId}`;
    const batchStateStr = await this.redis.get(batchStateKey);
    
    if (batchStateStr) {
      const batchState = JSON.parse(batchStateStr);
      batchState.status = 'cancelled';
      await this.redis.setex(batchStateKey, 3600, JSON.stringify(batchState));
      console.log(`🛑 Cancelled concurrent batch processing for task ${taskId}`);
    }
  }

  async isCancelled(taskId: string): Promise<boolean> {
    try {
      const data = await this.redis.get(`progress:${taskId}`);
      if (!data) return false; // If task doesn't exist, it can't be cancelled.
      const progress: ProgressData = JSON.parse(data);
      return progress.status === 'cancelled';
    } catch (error) {
      console.error(`Failed to check cancellation status for task ${taskId}:`, error);
      return false; // Fail safe
    }
  }

  getCancelEndpoint(taskId: string): string {
    return `/api/transform/progress/${taskId}`;
  }

  async cleanupOldTasks(maxAge: number = 24 * 60 * 60 * 1000): Promise<number> {
    const cutoff = Date.now() - maxAge;
    const keys = await this.redis.keys('progress:*');
    let cleaned = 0;

    for (const key of keys) {
      try {
        const data = await this.redis.get(key);
        if (data) {
          const progress: ProgressData = JSON.parse(data);
          if (progress.lastUpdate < cutoff) {
            await this.redis.del(key);
            cleaned++;
          }
        }
      } catch (error) {
        // If we can't parse the data, delete it
        await this.redis.del(key);
        cleaned++;
      }
    }

    return cleaned;
  }

  private async publishProgressUpdate(progressData: ProgressData): Promise<void> {
    try {
      const message = {
        type: 'progress_update',
        taskId: progressData.taskId,
        jobId: progressData.jobId,
        progress: {
          percentage: (progressData.processedCells / progressData.totalCells) * 100,
          completed: progressData.processedCells,
          total: progressData.totalCells,
          failed: progressData.failedCells,
          status: progressData.status,
          estimatedCompletion: progressData.estimatedCompletion,
          processingRate: progressData.processingRate,
          currentOperation: progressData.currentOperation
        },
        timestamp: progressData.lastUpdate
      };

      await this.redis.publish(this.publishChannel, JSON.stringify(message));
      await this.redis.publish(`${this.publishChannel}:${progressData.jobId}`, JSON.stringify(message));
    } catch (error) {
      console.error('Failed to publish progress update:', error);
    }
  }

  // Utility method to calculate ETA
  calculateETA(progressData: ProgressData): string | null {
    if (progressData.processingRate <= 0 || progressData.processedCells >= progressData.totalCells) {
      return null;
    }

    const remainingCells = progressData.totalCells - progressData.processedCells;
    const secondsRemaining = remainingCells / progressData.processingRate;
    const etaTime = new Date(Date.now() + secondsRemaining * 1000);
    
    return etaTime.toLocaleTimeString();
  }

  // Batch update method for bulk operations
  async updateBatchProgress(
    taskId: string,
    batchResults: {
      processed: number;
      failed: number;
      errors: string[];
      cacheHits: number;
      cacheMisses: number;
    }
  ): Promise<void> {
    await this.updateProgress(taskId, {
      processedCells: batchResults.processed,
      failedCells: batchResults.failed,
      errors: batchResults.errors,
      cacheHits: batchResults.cacheHits,
      cacheMisses: batchResults.cacheMisses,
      currentBatch: Math.floor(batchResults.processed / 100) + 1
    });
  }

  // Get aggregated statistics for all tasks
  async getTaskStatistics(): Promise<{
    totalTasks: number;
    activeTasks: number;
    completedTasks: number;
    failedTasks: number;
    totalCellsProcessed: number;
    averageProcessingRate: number;
    cacheHitRate: number;
  }> {
    const keys = await this.redis.keys('progress:*');
    let totalTasks = 0;
    let activeTasks = 0;
    let completedTasks = 0;
    let failedTasks = 0;
    let totalCellsProcessed = 0;
    let totalProcessingRate = 0;
    let totalCacheHits = 0;
    let totalCacheAttempts = 0;

    for (const key of keys) {
      try {
        const data = await this.redis.get(key);
        if (data) {
          const progress: ProgressData = JSON.parse(data);
          totalTasks++;
          
          switch (progress.status) {
            case 'processing':
            case 'queued':
              activeTasks++;
              break;
            case 'completed':
              completedTasks++;
              break;
            case 'failed':
              failedTasks++;
              break;
          }

          totalCellsProcessed += progress.processedCells;
          totalProcessingRate += progress.processingRate;
          totalCacheHits += progress.redisStats.cacheHits;
          totalCacheAttempts += progress.redisStats.cacheHits + progress.redisStats.cacheMisses;
        }
      } catch (error) {
        console.error('Error parsing task data:', error);
      }
    }

    return {
      totalTasks,
      activeTasks,
      completedTasks,
      failedTasks,
      totalCellsProcessed,
      averageProcessingRate: totalTasks > 0 ? totalProcessingRate / totalTasks : 0,
      cacheHitRate: totalCacheAttempts > 0 ? (totalCacheHits / totalCacheAttempts) * 100 : 0
    };
  }
}

export default ProgressTracker; 