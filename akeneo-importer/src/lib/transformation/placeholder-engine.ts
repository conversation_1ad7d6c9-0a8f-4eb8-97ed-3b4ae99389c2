export interface PlaceholderContext {
  jobId: string;
  columnName: string;
  currentRow: any;
  rowIndex: number;
  columnConfiguration?: {
    column_name: string;
    type: string;
    prompt?: string | null;
    description?: string | null;
    output_validation_column?: string | null;
    validation_data?: string[] | null;
  };
  jobNotes?: string;
  worksheetData?: any[];
  sourceColumns?: string[];
}

export interface PlaceholderRenderResult {
  renderedPrompt: string;
  placeholdersFound: string[];
  placeholdersReplaced: Record<string, string>;
  unreplacedPlaceholders: string[];
}

export class PlaceholderEngine {
  /**
   * Main method to render a prompt template with context
   */
  static renderTemplate(template: string, context: PlaceholderContext): PlaceholderRenderResult {
    const placeholdersFound = this.extractPlaceholders(template);
    const placeholdersReplaced: Record<string, string> = {};
    let renderedPrompt = template;

    // Replace placeholders in order of specificity (longest first to avoid partial matches)
    
    // 1. Replace specific compound placeholders first
    if (context.worksheetData && context.worksheetData.length > 0) {
      const validationData = this.formatAdditionalDataAsMarkdown(context.worksheetData);
      const placeholder = '@prompt_additional_data';
      if (renderedPrompt.includes(placeholder)) {
        renderedPrompt = renderedPrompt.replace(new RegExp(placeholder, 'g'), validationData);
        placeholdersReplaced[placeholder] = validationData.substring(0, 100) + '...';
      }
    }

    // Output validation field
    const outputValidationFieldValue = context.columnConfiguration?.output_validation_column || 'No validation field specified';
    const outputValidationPlaceholder = '@output_validation_field';
    if (renderedPrompt.includes(outputValidationPlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(outputValidationPlaceholder, 'g'), outputValidationFieldValue);
      placeholdersReplaced[outputValidationPlaceholder] = outputValidationFieldValue;
    }

    // Keep old placeholder for backward compatibility
    const oldValidationPlaceholder = '@prompt_additional_data_column';
    if (renderedPrompt.includes(oldValidationPlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(oldValidationPlaceholder, 'g'), outputValidationFieldValue);
      placeholdersReplaced[oldValidationPlaceholder] = outputValidationFieldValue;
    }

    // 2. Replace other standard placeholders
    const notesValue = context.jobNotes || 'No job notes specified';
    const notesPlaceholder = '@notes';
    if (renderedPrompt.includes(notesPlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(notesPlaceholder, 'g'), notesValue);
      placeholdersReplaced[notesPlaceholder] = notesValue;
    }

    // Row data placeholder
    const rowValue = JSON.stringify(context.currentRow, null, 2);
    const rowPlaceholder = '@row';
    if (renderedPrompt.includes(rowPlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(rowPlaceholder, 'g'), rowValue);
      placeholdersReplaced[rowPlaceholder] = rowValue.substring(0, 100) + '...';
    }

    // Column description
    const descriptionValue = context.columnConfiguration?.description || 
                           context.columnConfiguration?.prompt || 
                           'No description specified';
    const descriptionPlaceholder = '@description';
    if (renderedPrompt.includes(descriptionPlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(descriptionPlaceholder, 'g'), descriptionValue);
      placeholdersReplaced[descriptionPlaceholder] = descriptionValue;
    }

    // Keep @prompt for backward compatibility
    const promptPlaceholder = '@prompt';
    if (renderedPrompt.includes(promptPlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(promptPlaceholder, 'g'), descriptionValue);
      placeholdersReplaced[promptPlaceholder] = descriptionValue;
    }

    // Column name placeholder
    const columnNamePlaceholder = '@column_name';
    if (renderedPrompt.includes(columnNamePlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(columnNamePlaceholder, 'g'), context.columnName);
      placeholdersReplaced[columnNamePlaceholder] = context.columnName;
    }

    // Task description placeholder
    const taskDescriptionValue = `Transform the source data to match the requirements for the "${context.columnName}" column`;
    const taskDescriptionPlaceholder = '@taskDescription';
    if (renderedPrompt.includes(taskDescriptionPlaceholder)) {
      renderedPrompt = renderedPrompt.replace(new RegExp(taskDescriptionPlaceholder, 'g'), taskDescriptionValue);
      placeholdersReplaced[taskDescriptionPlaceholder] = taskDescriptionValue;
    }

    // 3. Replace dynamic column placeholders from current row
    if (context.currentRow) {
      Object.entries(context.currentRow).forEach(([column, value]) => {
        const placeholder = `@${column}`;
        const valueStr = String(value || '');
        if (renderedPrompt.includes(placeholder)) {
          renderedPrompt = renderedPrompt.replace(new RegExp(`@${this.escapeRegex(column)}`, 'g'), valueStr);
          placeholdersReplaced[placeholder] = valueStr.substring(0, 50) + (valueStr.length > 50 ? '...' : '');
        }
      });
    }

    // Find any unreplaced placeholders
    const unreplacedPlaceholders = this.extractPlaceholders(renderedPrompt);

    // Simple summary log instead of verbose details
    console.log('📝 Prompt rendered with placeholders:', {
      placeholders: Object.keys(placeholdersReplaced).length,
      unreplaced: unreplacedPlaceholders.length
    });

    return {
      renderedPrompt,
      placeholdersFound,
      placeholdersReplaced,
      unreplacedPlaceholders
    };
  }

  /**
   * Extract all @placeholder patterns from a template
   */
  static extractPlaceholders(template: string): string[] {
    const placeholderRegex = /@(\w+)/g;
    const placeholders: string[] = [];
    let match;

    while ((match = placeholderRegex.exec(template)) !== null) {
      const placeholder = `@${match[1]}`;
      if (!placeholders.includes(placeholder)) {
        placeholders.push(placeholder);
      }
    }

    return placeholders.sort();
  }

  /**
   * Validate that a template has valid placeholder syntax
   */
  static validateTemplate(template: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check for malformed placeholders (@ followed by non-word characters)
    const malformedPlaceholders = template.match(/@[^a-zA-Z0-9_\s]/g);
    if (malformedPlaceholders) {
      errors.push(`Malformed placeholders found: ${malformedPlaceholders.join(', ')}`);
    }

    // Check for empty placeholders (@)
    if (template.includes('@') && template.match(/@(?!\w)/g)) {
      errors.push('Empty placeholder (@) found - placeholders must be followed by alphanumeric characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Format worksheet data as markdown for validation
   */
  private static formatAdditionalDataAsMarkdown(worksheetData: any[]): string {
    if (!worksheetData || worksheetData.length === 0) {
      return 'No validation data available';
    }

    try {
      // Get the first few rows as examples
      const maxRows = Math.min(worksheetData.length, 10);
      const sampleData = worksheetData.slice(0, maxRows);
      
      // Create a markdown table
      if (sampleData.length > 0 && typeof sampleData[0] === 'object') {
        const headers = Object.keys(sampleData[0]);
        let markdown = '| ' + headers.join(' | ') + ' |\n';
        markdown += '| ' + headers.map(() => '---').join(' | ') + ' |\n';
        
        sampleData.forEach(row => {
          const values = headers.map(header => String(row[header] || '').substring(0, 50));
          markdown += '| ' + values.join(' | ') + ' |\n';
        });
        
        if (worksheetData.length > maxRows) {
          markdown += `\n... and ${worksheetData.length - maxRows} more rows`;
        }
        
        return markdown;
      } else {
        // Simple list format
        return sampleData.map((item, index) => `${index + 1}. ${String(item)}`).join('\n');
      }
    } catch (error) {
      console.error('Error formatting worksheet data:', error);
      return 'Error formatting validation data';
    }
  }

  /**
   * Escape special regex characters in a string
   */
  private static escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Get all supported placeholder names
   */
  static getSupportedPlaceholders(): string[] {
    return [
      '@prompt_additional_data',
      '@output_validation_field',
      '@prompt_additional_data_column', // backward compatibility
      '@notes',
      '@row',
      '@description',
      '@prompt', // backward compatibility
      '@column_name',
      '@taskDescription',
      // Dynamic placeholders from row data are not listed here as they vary per row
    ];
  }
} 