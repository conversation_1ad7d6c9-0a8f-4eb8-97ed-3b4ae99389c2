import OpenAI from 'openai';
import crypto from 'crypto';
import type { LLMRequest, LLMResponse } from '@/types';
import pricingConfig from '../../../config/llm-pricing.json';
import { LoggingService } from '../logging/logging-service';

export interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  pricing?: {
    prompt: string;
    completion: string;
  };
  context_length?: number;
  architecture?: string;
}

export interface GroqModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  active: boolean;
  context_window?: number;
}

export interface TransformationResult {
  success: boolean;
  transformedData: any;
  model: string;
  fromCache: boolean;
  processingTime: number;
  cacheKey?: string;
}

export interface StructuredLLMResponse {
  reasoning: string;
  answer: string;
  metadata?: {
    model: string;
    reasoning_format: string;
    response_format: string;
    from_cache: boolean;
  };
}

export interface GroqChatOptions {
  reasoning_format?: 'hidden' | 'raw' | 'parsed';
  response_format?: {
    type: 'json_schema' | 'json_object';
    json_schema?: {
      name: string;
      strict?: boolean;
      schema: any;
    };
  };
}

// Model capability detection
export interface ModelCapabilities {
  supportsJsonSchema: boolean;
  supportsJsonObject: boolean;
  supportsReasoningFormat: boolean;
  supportsReasoningEffort: boolean;
}

// Response parsing utilities
export class ResponseParser {
  static extractAnswerFromResponse(response: string): { reasoning: string; answer: string } {
    // BULLETPROOF PARSER: Look for FINAL_ANSWER first (enforced by system prompt)
    const finalAnswerMatch = response.match(/FINAL_ANSWER:\s*(.+?)(?:\n|$)/i);
    
    if (finalAnswerMatch && finalAnswerMatch[1]) {
      const answer = finalAnswerMatch[1].trim();
      // Split response at the FINAL_ANSWER marker to get reasoning
      const splitIndex = response.search(/FINAL_ANSWER:/i);
      const reasoning = splitIndex > 0 ? response.substring(0, splitIndex).trim() : '';
      
      return {
        reasoning: reasoning || 'No reasoning provided',
        answer: answer
      };
    }

    // FALLBACK: Try other common patterns (for backward compatibility)
    const fallbackPatterns = [
      /ANSWER:\s*([^\n\r]*)/i,
      /Final Answer:\s*([^\n\r]*)/i,
      /Answer:\s*([^\n\r]*)/i,
    ];

    for (const pattern of fallbackPatterns) {
      const match = response.match(pattern);
      if (match && match[1] && match[1].trim().length > 0) {
        const answer = match[1].trim();
        const splitIndex = response.search(pattern);
        const reasoning = splitIndex > 0 ? response.substring(0, splitIndex).trim() : '';
        
        return {
          reasoning: reasoning || 'No reasoning provided',
          answer: answer
        };
      }
    }

    // LAST RESORT: Extract the last meaningful line
    const lines = response.split('\n').filter(line => line.trim());
    if (lines.length > 0) {
      // Look for the last line that seems like an answer
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        // Skip obviously non-answer lines
        if (line && 
            line.length > 0 && 
            line.length < 200 && // Reasonable answer length
            !line.includes('Based on') && 
            !line.includes('Therefore') &&
            !line.includes('In conclusion') &&
            !line.includes('Let me analyze') &&
            !line.includes('Looking at') &&
            !line.startsWith('<think>') &&
            !line.endsWith('</think>')) {
          
          // Clean up the answer (remove common formatting)
          let cleanAnswer = line
            .replace(/^\*+|\*+$/g, '') // Remove asterisks
            .replace(/^"+|"+$/g, '')   // Remove quotes
            .replace(/^'+|'+$/g, '')   // Remove single quotes
            .trim();
          
          if (cleanAnswer.length > 0) {
            const reasoning = lines.slice(0, i).join('\n').trim();
            return {
              reasoning: reasoning || 'No reasoning provided',
              answer: cleanAnswer
            };
          }
        }
      }
    }

    // ABSOLUTE FALLBACK: Use the entire response
    return {
      reasoning: response,
      answer: response.split('\n').filter(line => line.trim()).pop()?.trim() || response
    };
  }

  static generateStructuredPrompt(originalPrompt: string, showReasoning: boolean = true): string {
    const instructionText = showReasoning 
      ? `Please provide your reasoning process and then give your final answer. Format your response exactly as follows:

[Your detailed step-by-step reasoning and analysis here]

FINAL_ANSWER: [Your exact answer here]

Important: 
- Provide your reasoning first
- End with "FINAL_ANSWER: " followed by your exact answer
- Do not include any text after the final answer
- Keep the final answer concise and precise

Example:
Based on the product information provided, I need to analyze the delivery method and unit of measure. The product shows "Lieferhinweis" indicating direct delivery by truck, which suggests BESTELLWARE (dropshipping). The unit pattern is Stück/Stück/Stück based on the complete set nature of the product.

FINAL_ANSWER: AV000010`
      : `Please analyze the provided information and give your final answer. 

Think through the problem step by step, then end your response with:

FINAL_ANSWER: [Your exact answer here]

Important: Always end with "FINAL_ANSWER: " followed by your exact answer.`;

    return `${originalPrompt}

${instructionText}`;
  }
}

export type LLMProvider = 'openrouter' | 'groq';

export interface LLMProviderConfig {
  provider: LLMProvider;
  models: OpenRouterModel[] | GroqModel[];
  defaultModel: string;
  enabledModels: string[];
}

export class OpenRouterClient {
  private client: OpenAI;
  private cacheServiceUrl: string;
  private authToken: string;

  constructor() {
    // Initialize OpenAI client for direct calls
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    });

    // Initialize cache service connection (optional)
    this.cacheServiceUrl = process.env.LLM_CACHE_SERVICE_URL || '';
    this.authToken = process.env.LLM_CACHE_AUTH_TOKEN || 'llm-cache-default-token-2024';
  }

  private generateCacheKey(messages: any[], model: string, temperature: number = 0.1, responseFormat: string = 'json_object'): string {
    // Create a more comprehensive cache key that includes message content hash
    const content = JSON.stringify({ 
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })), 
      model, 
      temperature, 
      responseFormat 
    });
    
    const hash = crypto.createHash('sha256').update(content).digest('hex');
    
    // Log first 8 characters for debugging
    console.log(`🔑 Generated cache key ${hash.substring(0, 8)}... for model ${model}`);
    console.log(`🔍 Cache key content preview:`, {
      model,
      temperature,
      responseFormat,
      messagesCount: messages.length,
      firstMessageRole: messages[0]?.role,
      firstMessageLength: messages[0]?.content?.length,
      firstMessagePreview: messages[0]?.content?.substring(0, 100) + '...',
      userMessageLength: messages[1]?.content?.length,
      userMessagePreview: messages[1]?.content?.substring(0, 100) + '...'
    });
    
    return hash;
  }

  private async getCachedResult(cacheKey: string): Promise<any | null> {
    if (!this.cacheServiceUrl) return null;
    
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/get/${cacheKey}`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`🔍 Cache response for key ${cacheKey.substring(0, 8)}...:`, {
          success: result.success,
          hasData: !!result.data,
          hasContent: !!(result.data && result.data.content),
          contentLength: result.data && result.data.content ? result.data.content.length : 0
        });
        
        // Only return cached data if the cache service indicates success AND has actual data
        if (result.success && result.data && result.data.content) {
          return result.data;
        }
      } else {
        console.log(`🔍 Cache miss for key ${cacheKey.substring(0, 8)}...: HTTP ${response.status}`);
      }
      
      // Return null for any other case (404, no success, no data, etc.)
      // This will trigger a fresh LLM call
      return null;
    } catch (error) {
             console.warn('Cache service unavailable:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  private async setCachedResult(cacheKey: string, data: any): Promise<void> {
    if (!this.cacheServiceUrl) return;
    
    try {
      await fetch(`${this.cacheServiceUrl}/api/cache/set`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({ key: cacheKey, data }),
      });
    } catch (error) {
             console.warn('Failed to cache result:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async chat(
    messages: any[],
    model?: string,
    useJsonFormat: boolean = true,
    bypassCache: boolean = false,
    temperature?: number
  ): Promise<string> {
    const startTime = Date.now();
    
    // Enforce mandatory cache usage
    if (bypassCache) {
      console.warn('CACHE BYPASS ATTEMPTED in OpenRouterClient: Forcing request through cache as per new policy.');
    }

    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('openrouter'));
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('openrouter'));
    const responseFormat = useJsonFormat ? 'json_object' : 'text';
    const cacheKey = this.generateCacheKey(messages, selectedModel, finalTemperature, responseFormat);

    // Always check cache first
    const cached = await this.getCachedResult(cacheKey);
    if (cached && cached.content && cached.content.trim()) {
        console.log(`✅ Cache hit for model ${selectedModel}`);
        return cached.content;
    } else if (cached) {
        console.log(`⚠️ Cache hit but empty content for model ${selectedModel}, making fresh LLM call`);
    }

    console.log(`🚀 Making new LLM call to OpenRouter for model ${selectedModel}...`);
    
    const response = await this.client.chat.completions.create({
      model: selectedModel,
      messages: messages,
      temperature: finalTemperature,
      response_format: { type: responseFormat },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('LLM response was empty');
    }

    // Store in cache
    await this.setCachedResult(cacheKey, { content, model: selectedModel });
    
    const duration = Date.now() - startTime;
    console.log(`✅ LLM call completed in ${duration}ms`);

    return content;
  }

  async transformData(
    data: any,
    prompt: string,
    model?: string,
    bypassCache: boolean = false
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    const selectedModel = model || process.env.DEFAULT_MODEL || 'qwen/qwen3-32b';
    
    // Construct messages for transformation
    // The 'prompt' is already fully contextualized by the TransformationEngine
    const messages = [
      {
        role: "system" as const,
        content: "You are a data transformation assistant. You will receive a detailed prompt with all necessary context and data. Your response must be only the single, transformed value. Do not add any extra text, explanation, or JSON formatting."
      },
      {
        role: "user" as const,
        content: prompt
      }
    ];

    try {
      // For this direct transformation, we expect a raw value, not JSON
      const response = await this.chat(messages, selectedModel, false, bypassCache);

      // The response is the transformed data directly
      const transformedData = response;

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        transformedData,
        model: selectedModel,
        fromCache: false, // The chat method handles cache detection
        processingTime,
        cacheKey: this.generateCacheKey(messages, selectedModel, 0.1, 'text')
      };
    } catch (error) {
      console.error('Data transformation error:', error);
      return {
        success: false,
        transformedData: null,
        model: selectedModel,
        fromCache: false,
        processingTime: Date.now() - startTime
      };
    }
  }

  async getAvailableModels(forceRefresh: boolean = false): Promise<OpenRouterModel[]> {
    try {
      // Fetch models directly from OpenRouter API
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        // Fallback to basic models if API fails
        return [
          { id: 'qwen/qwen3-32b', name: 'Qwen 3 32B', description: 'Qwen reasoning model with structured output (default)' },
          { id: 'deepseek/deepseek-r1-0528', name: 'DeepSeek R1', description: 'DeepSeek reasoning model' },
          { id: 'gpt-4o-mini', name: 'GPT-4O Mini', description: 'Fast and efficient model' },
          { id: 'gpt-4o', name: 'GPT-4O', description: 'Advanced model' },
          { id: 'gpt-4.1-mini', name: 'GPT-4.1 Mini', description: 'Latest mini model' },
          { id: 'gpt-4.1', name: 'GPT-4.1', description: 'Latest full model' },
          { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: 'Anthropic Claude model' },
          { id: 'meta-llama/llama-3.2-90b-vision-instruct', name: 'Llama 3.2 90B Vision', description: 'Meta Llama vision model' }
        ];
      }

      const data = await response.json();
      
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid response format from OpenRouter API');
      }

      // Transform OpenRouter API response to our format
      const models: OpenRouterModel[] = data.data.map((model: any) => ({
        id: model.id,
        name: model.name || model.id,
        description: model.description || '',
        pricing: model.pricing ? {
          prompt: model.pricing.prompt || '0',
          completion: model.pricing.completion || '0'
        } : undefined,
        context_length: model.context_length || 4096,
        architecture: model.architecture?.instruct_type || model.architecture?.modality
      }));

      console.log(`✅ Fetched ${models.length} models from OpenRouter API`);
      return models;
    } catch (error) {
      console.error('Failed to fetch available models:', error);
      // Return fallback models with Qwen as default
      return [
        { id: 'qwen/qwen3-32b', name: 'Qwen 3 32B', description: 'Qwen reasoning model with structured output (default)' }
      ];
    }
  }

  async clearCache(pattern?: string): Promise<number> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/clear${pattern ? `?pattern=${pattern}` : ''}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to clear cache: ${response.statusText}`);
      }

      const result = await response.json();
      return result.clearedCount || 0;
    } catch (error) {
      console.error('Cache clear error:', error);
      throw new Error(`Failed to clear cache: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: number;
    hitRate?: number;
  }> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/stats`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get cache stats: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        totalKeys: result.stats?.totalKeys || 0,
        memoryUsage: result.stats?.memoryUsage || 0,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        totalKeys: 0,
        memoryUsage: 0,
      };
    }
  }
}

export class GroqClient {
  private client: OpenAI;
  private cacheServiceUrl: string;
  private authToken: string;

  constructor() {
    // Initialize OpenAI client for Groq calls
    if (!process.env.GROQ_API_KEY) {
      throw new Error('GROQ_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      baseURL: "https://api.groq.com/openai/v1/",
      apiKey: process.env.GROQ_API_KEY,
    });

    // Cache service configuration
    if (!process.env.LLM_CACHE_SERVICE_URL) {
      console.error('❌ FATAL: LLM_CACHE_SERVICE_URL is not defined. Caching is mandatory and all LLM calls will fail.');
    }
    this.cacheServiceUrl = process.env.LLM_CACHE_SERVICE_URL || '';
    this.authToken = process.env.LLM_CACHE_AUTH_TOKEN || '';
  }

  private getModelCapabilities(model: string): ModelCapabilities {
    // Load capabilities from pricing configuration
    const modelConfig = Object.values(pricingConfig.models).find(
      (config: any) => config.model_id === model || config.model_id.includes(model.replace('/', '/'))
    );

    if (modelConfig && modelConfig.capabilities) {
      return {
        supportsJsonSchema: modelConfig.capabilities.json_mode === true,
        supportsJsonObject: modelConfig.capabilities.json_mode === true, 
        supportsReasoningFormat: modelConfig.capabilities.reasoning_format === true,
        supportsReasoningEffort: modelConfig.capabilities.reasoning_effort === true
      };
    }

    // Fallback for models not in pricing config
    console.warn(`Model ${model} not found in pricing config, using fallback capabilities`);
    return {
      supportsJsonSchema: false,
      supportsJsonObject: true, // Most Groq models support json_object
      supportsReasoningFormat: false,
      supportsReasoningEffort: false
    };
  }

  private generateCacheKey(messages: any[], model: string, temperature: number = 0.1, responseFormat: string = 'json_object', reasoningFormat?: string): string {
    const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n');
    const hashInput = `groq:${prompt}:${model}:${temperature}:${responseFormat}:${reasoningFormat || 'none'}`;
    const hash = require('crypto')
      .createHash('sha256')
      .update(hashInput)
      .digest('hex')
      .substring(0, 16);
    
    return `groq:${hash}`;
  }

  private async getCachedResult(cacheKey: string): Promise<any | null> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/get/${cacheKey}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (response.status === 404) {
        return null; // Cache miss
      }

      if (!response.ok) {
        console.warn('Cache service not available, proceeding without cache');
        return null;
      }

      const data = await response.json();
      const cachedData = data.data;
      
      // Handle both old format (direct content) and new format (with metadata)
      if (typeof cachedData === 'object' && cachedData.content !== undefined) {
        return cachedData.content; // New format with metadata
      } else {
        return cachedData; // Old format (direct content)
      }
    } catch (error) {
      console.warn('Cache service error:', error);
      return null;
    }
  }

  private async setCachedResult(cacheKey: string, data: any, model?: string): Promise<void> {
    try {
      // Include model metadata in cached data
      const cacheData = {
        content: data,
        model: model || 'unknown',
        timestamp: new Date().toISOString(),
        provider: 'groq'
      };

      await fetch(`${this.cacheServiceUrl}/api/cache/set`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({
          key: cacheKey,
          data: cacheData,
          ttl: 7 * 24 * 60 * 60 // 7 days in seconds
        }),
      });
    } catch (error) {
      console.warn('Failed to cache result:', error);
    }
  }

  async chat(
    messages: any[],
    model?: string,
    useJsonFormat: boolean = true,
    bypassCache: boolean = false,
    options?: GroqChatOptions,
    temperature?: number
  ): Promise<string> {
    const effectiveBypassCache = false; // Forcing cache usage
    if (bypassCache) {
        console.warn('CACHE BYPASS ATTEMPTED in GroqClient: Forcing request through cache as per new policy.');
    }
    const modelService = new ModelSelectionService();
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('groq'));
    const { content } = await this.chatWithMetadata(messages, model, useJsonFormat, effectiveBypassCache, options, finalTemperature);
    return content;
  }

  async chatWithMetadata(
    messages: any[],
    model?: string,
    useJsonFormat: boolean = true,
    bypassCache: boolean = false,
    options?: GroqChatOptions,
    temperature?: number
  ): Promise<{ content: string; fromCache: boolean }> {
    const startTime = Date.now();

    // Enforce mandatory cache usage and configuration
    if (!this.cacheServiceUrl) {
      throw new Error('Cache service is not configured. LLM call aborted.');
    }

    // Enforce mandatory cache usage
    if (bypassCache) {
      console.warn('CACHE BYPASS ATTEMPTED in GroqClient: Forcing request through cache as per new policy.');
    }
    
    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('groq'));
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('groq'));
    const capabilities = this.getModelCapabilities(selectedModel);
    
    const responseFormat = useJsonFormat && (capabilities.supportsJsonSchema || capabilities.supportsJsonObject)
      ? 'json_object'
      : 'text';
      
    const cacheKey = this.generateCacheKey(messages, selectedModel, finalTemperature, responseFormat, options?.reasoning_format);
    
    // Always check cache first
    const cached = await this.getCachedResult(cacheKey);
    if (cached) {
        console.log(`✅ Cache hit for model ${selectedModel}`);
        return { ...cached, fromCache: true };
    }

    console.log(`🚀 Making new LLM call to Groq for model ${selectedModel}...`);

    const completionParams: any = {
      model: selectedModel,
      messages,
      temperature: finalTemperature,
    };

    if (useJsonFormat && capabilities.supportsJsonObject) {
        completionParams.response_format = { type: 'json_object' };
    }
    
    const response = await this.client.chat.completions.create(completionParams);
    const content = response.choices[0].message.content || '';
    
    await this.setCachedResult(cacheKey, { content, model: selectedModel });
    
    const duration = Date.now() - startTime;
    console.log(`✅ Groq LLM call completed in ${duration}ms`);

    return { content, fromCache: false };
  }

  /**
   * Chat with structured output that separates reasoning from answer
   */
  async chatStructured(
    messages: any[],
    model?: string,
    bypassCache: boolean = false,
    showReasoning: boolean = true,
    temperature?: number,
    logger?: LoggingService
  ): Promise<StructuredLLMResponse> {
    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('groq'));
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('groq'));
    const startTime = Date.now();
    let cacheKey: string | null = null;
    
    const logDetails: Record<string, any> = {
      event: 'ai_transform_structured',
      model: selectedModel,
      bypassCache,
      showReasoning,
      temperature: finalTemperature,
      promptMessages: messages,
    };

    // 1. Generate Cache Key and Check Cache
    if (!bypassCache) {
      cacheKey = this.generateCacheKey(messages, selectedModel, finalTemperature, 'structured_json', 'parsed');
      logDetails.cacheKey = cacheKey;
      const cachedResult = await this.getCachedResult(cacheKey);
      if (cachedResult) {
        const processingTime = Date.now() - startTime;
        logger?.log({ ...logDetails, fromCache: true, processingTime, response: cachedResult });
        return {
          ...cachedResult,
          metadata: { ...cachedResult.metadata, from_cache: true }
        };
      }
    }

    // 2. Prepare the request for the LLM
    // Modify the user message to include structured output instructions
    const modifiedMessages = [...messages];
    const lastMessage = modifiedMessages[modifiedMessages.length - 1];
    if (lastMessage.role === 'user') {
      lastMessage.content = ResponseParser.generateStructuredPrompt(lastMessage.content, showReasoning);
    }

    // 3. Make the API Call
    let rawResponse: string;
    let finalResponse: StructuredLLMResponse;

    try {
      const response = await this.client.chat.completions.create({
        model: selectedModel,
        messages: modifiedMessages,
        temperature: finalTemperature,
      });
      rawResponse = response.choices[0].message.content || '';

      // 4. Parse the response
      const parsed = ResponseParser.extractAnswerFromResponse(rawResponse);
      finalResponse = {
        reasoning: parsed.reasoning,
        answer: parsed.answer,
        metadata: {
          model: selectedModel,
          reasoning_format: 'parsed',
          response_format: 'prompt_engineering',
          from_cache: false
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger?.log({
        ...logDetails,
        event: 'ai_transform_structured_error',
        fromCache: false,
        processingTime,
        error: error instanceof Error ? { message: error.message, stack: error.stack } : String(error),
      });
      console.error('Error in Groq chatStructured call:', error);
      throw error;
    }

    // 5. Log and Cache the new result
    const processingTime = Date.now() - startTime;
    logger?.log({
      ...logDetails,
      fromCache: false,
      processingTime,
      response: finalResponse,
    });

    if (cacheKey) {
      await this.setCachedResult(cacheKey, finalResponse, selectedModel);
    }
    
    return finalResponse;
  }

  async transformData(
    data: any,
    prompt: string,
    model?: string,
    bypassCache: boolean = false
  ): Promise<TransformationResult> {
    const selectedModel = model || process.env.GROQ_DEFAULT_MODEL || 'qwen/qwen3-32b';
    const startTime = Date.now();

    // The 'prompt' is already fully contextualized by the TransformationEngine
    const messages = [
      {
        role: "system" as const,
        content: "You are a data transformation assistant. You will receive a detailed prompt with all necessary context and data. Your response must be only the single, transformed value. Do not add any extra text, explanation, or JSON formatting."
      },
      {
        role: "user" as const,
        content: prompt
      }
    ];

    try {
      // We expect a raw value, not JSON
      const response = await this.chat(messages, selectedModel, false, bypassCache);

      // The response is the transformed data directly
      const transformedData = response;

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        transformedData,
        model: selectedModel,
        fromCache: false, // The chat method handles cache detection
        processingTime,
        cacheKey: this.generateCacheKey(messages, selectedModel, 0.1, 'text')
      };
    } catch (error) {
      console.error('Groq data transformation error:', error);
      return {
        success: false,
        transformedData: null,
        model: selectedModel,
        fromCache: false,
        processingTime: Date.now() - startTime
      };
    }
  }

  async getAvailableModels(forceRefresh: boolean = false): Promise<GroqModel[]> {
    try {
      // Fetch models directly from Groq API
      const response = await fetch('https://api.groq.com/openai/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        console.error(`Groq API error: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Groq API returned ${response.status}: ${response.statusText}. ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid response format from Groq API - no models data found');
      }

      // Filter active models only
      const models: GroqModel[] = data.data
        .filter((model: any) => model.active !== false)
        .map((model: any) => ({
          id: model.id,
          object: model.object || 'model',
          created: model.created || Date.now(),
          owned_by: model.owned_by || 'groq',
          active: model.active !== false,
          context_window: model.context_window
        }));

      console.log(`✅ Fetched ${models.length} models from Groq API:`, models.map(m => m.id));
      return models;
    } catch (error) {
      console.error('Failed to fetch Groq models:', error);
      
      // Check if API key is configured
      if (!process.env.GROQ_API_KEY) {
        throw new Error('GROQ_API_KEY environment variable is not configured');
      }
      
      // If API key is present but API call failed, provide more specific error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to fetch models from Groq API: ${errorMessage}`);
    }
  }

  async clearCache(pattern?: string): Promise<number> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/clear${pattern ? `?pattern=${pattern}` : ''}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to clear cache: ${response.statusText}`);
      }

      const result = await response.json();
      return result.clearedCount || 0;
    } catch (error) {
      console.error('Cache clear error:', error);
      throw new Error(`Failed to clear cache: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: number;
    hitRate?: number;
  }> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/stats`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get cache stats: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        totalKeys: result.stats?.totalKeys || 0,
        memoryUsage: result.stats?.memoryUsage || 0,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        totalKeys: 0,
        memoryUsage: 0,
      };
    }
  }

  async getDefaultModel(): Promise<string> {
    // Implementation of getDefaultModel method
    throw new Error('Method not implemented');
  }
}

// Enhanced Model Selection Service with Provider Support
export class ModelSelectionService {
  private cacheServiceUrl: string;
  private authToken: string;
  private redis: any;

  constructor() {
    if (!process.env.LLM_CACHE_SERVICE_URL) {
      console.warn('⚠️ LLM_CACHE_SERVICE_URL is not defined in ModelSelectionService. Some features may not work.');
    }
    this.cacheServiceUrl = process.env.LLM_CACHE_SERVICE_URL || '';
    this.authToken = process.env.LLM_CACHE_AUTH_TOKEN || 'llm-cache-default-token-2024';
    
    // Initialize Redis connection for storing model settings
    try {
      // Import RedisManager for consistent connection handling
      const { RedisManager } = require('../redis/redis-manager');
      const { REDIS_DB } = require('../../types');
      this.redis = RedisManager.getClient(REDIS_DB.CACHE);
      console.log('✅ ModelSelectionService Redis connection initialized');
    } catch (error) {
      console.warn('Redis not available for model settings storage:', error);
      this.redis = null;
    }
  }

  async getCurrentProvider(): Promise<LLMProvider> {
    try {
      if (this.redis) {
        const stored = await this.redis.get('llm:current_provider');
        if (stored) {
          return stored as LLMProvider;
        }
      }
    } catch (error) {
      console.warn('Error getting current provider from Redis:', error);
    }
    
    // Fallback to environment variable or default
    return (process.env.LLM_PROVIDER as LLMProvider) || 'groq';
  }

  async setCurrentProvider(provider: LLMProvider): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.set('llm:current_provider', provider);
      }
      console.log('Provider set:', provider);
    } catch (error) {
      console.warn('Error setting current provider in Redis:', error);
    }
  }

  async getEnabledModels(provider?: LLMProvider): Promise<string[]> {
    const currentProvider = provider || await this.getCurrentProvider();
    
    try {
      if (this.redis) {
        const stored = await this.redis.get(`llm:enabled_models:${currentProvider}`);
        if (stored) {
          const models = JSON.parse(stored);
          console.log(`Retrieved enabled models for ${currentProvider}:`, models);
          return models;
        }
      }
    } catch (error) {
      console.warn('Error getting enabled models from Redis:', error);
    }
    
    // Return default enabled models if nothing is stored
    if (currentProvider === 'groq') {
      const defaultModels = [
        // Primary Qwen model - default and only enabled by default
        'qwen/qwen3-32b'
      ];
      
      // Try to get available models and filter defaults to only include available ones
      try {
        const groqClient = new GroqClient();
        const availableModels = await groqClient.getAvailableModels();
        const availableIds = new Set(availableModels.map(m => m.id));
        const validDefaults = defaultModels.filter(id => availableIds.has(id));
        
        if (validDefaults.length > 0) {
          return validDefaults;
        }
      } catch (error) {
        console.warn('Could not validate default models against available models:', error);
      }
      
      return defaultModels;
    } else {
      // OpenRouter models
      return [
        'qwen/qwen-2.5-72b-instruct',
        'meta-llama/llama-3.1-70b-instruct',
        'anthropic/claude-3.5-sonnet'
      ];
    }
  }

  async setEnabledModels(modelIds: string[], provider?: LLMProvider): Promise<void> {
    const currentProvider = provider || await this.getCurrentProvider();
    
    try {
      if (this.redis) {
        await this.redis.set(`llm:enabled_models:${currentProvider}`, JSON.stringify(modelIds));
        console.log(`Enabled models stored for ${currentProvider}:`, modelIds);
      } else {
        console.log(`Redis not available - enabled models set for ${currentProvider}:`, modelIds);
      }
    } catch (error) {
      console.error('Error setting enabled models in Redis:', error);
      throw error;
    }
  }

  async getDefaultModel(provider?: LLMProvider): Promise<string> {
    const currentProvider = provider || await this.getCurrentProvider();
    
    try {
      if (this.redis) {
        const stored = await this.redis.get(`llm:default_model:${currentProvider}`);
        if (stored) {
          return stored;
        }
      }
    } catch (error) {
      console.warn('Error getting default model from Redis:', error);
    }
    
    // Environment variable fallbacks
    if (currentProvider === 'groq') {
      return process.env.GROQ_DEFAULT_MODEL || 'qwen/qwen3-32b';
    } else {
      return process.env.DEFAULT_MODEL || 'qwen/qwen-2.5-72b-instruct';
    }
  }

  async setDefaultModel(modelId: string, provider?: LLMProvider): Promise<void> {
    const currentProvider = provider || await this.getCurrentProvider();
    const enabledModels = await this.getEnabledModels(currentProvider);
    
    if (!enabledModels.includes(modelId)) {
      throw new Error('Model must be enabled before setting as default');
    }
    
    try {
      if (this.redis) {
        await this.redis.set(`llm:default_model:${currentProvider}`, modelId);
      }
      console.log(`Default model set for ${currentProvider}:`, modelId);
    } catch (error) {
      console.error('Error setting default model in Redis:', error);
      throw error;
    }
  }

  async getDefaultTemperature(provider?: LLMProvider): Promise<number> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      if (!this.redis) {
        // Fallback to default temperature
        return 1;
      }
      
      const defaultTemperature = await this.redis.get(`llm:default_temperature:${currentProvider}`);
      if (defaultTemperature !== null) {
        const temp = parseFloat(defaultTemperature);
        // Validate temperature range
        if (temp >= 0 && temp <= 2) {
          return temp;
        }
      }
      
      // Default temperature if not set or invalid
      return 1;
    } catch (error) {
      console.error('Error getting default temperature:', error);
      return 1;
    }
  }

  async setDefaultTemperature(temperature: number, provider?: LLMProvider): Promise<void> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      // Validate temperature range
      if (temperature < 0 || temperature > 2) {
        throw new Error('Temperature must be between 0 and 2');
      }
      
      if (this.redis) {
        await this.redis.set(`llm:default_temperature:${currentProvider}`, temperature.toString());
        console.log(`✅ Set default temperature for ${currentProvider}: ${temperature}`);
      }
    } catch (error) {
      console.error('Error setting default temperature:', error);
      throw error;
    }
  }

  async getAvailableModels(provider?: LLMProvider, forceRefresh: boolean = false): Promise<OpenRouterModel[] | GroqModel[]> {
    const currentProvider = provider || await this.getCurrentProvider();
    
    if (currentProvider === 'groq') {
      const groqClient = new GroqClient();
      return groqClient.getAvailableModels(forceRefresh);
    } else {
      const openRouterClient = new OpenRouterClient();
      return openRouterClient.getAvailableModels(forceRefresh);
    }
  }

  async getLLMClient(provider?: LLMProvider): Promise<OpenRouterClient | GroqClient> {
    const currentProvider = provider || await this.getCurrentProvider();
    
    if (currentProvider === 'groq') {
      return new GroqClient();
    } else {
      return new OpenRouterClient();
    }
  }
}

export default OpenRouterClient; 