import { RedisManager } from '@/lib/redis/redis-manager';
import { REDIS_DB } from '@/types';
import crypto from 'crypto';
import { PlaceholderEngine, PlaceholderContext, PlaceholderRenderResult } from './placeholder-engine';

export interface PromptTemplate {
  id: string;
  name: string;
  description?: string;
  template: string;
  placeholders: string[];
  created_at: string;
  updated_at: string;
  usage_count: number;
}

export interface PlaceholderData {
  row?: any;
  prompt?: string; // Legacy - now mapped to description
  description?: string; // New preferred placeholder
  notes?: string;
  context?: string;
  taskDescription?: string;
  examples?: string;
  styleGuide?: string;
  constraints?: string;
  format?: string;
  audience?: string;
  prompt_additional_data?: string;
  prompt_additional_data_column?: string; // Legacy - now mapped to output_validation_field
  output_validation_field?: string; // New preferred placeholder
  [key: string]: any; // For dynamic column placeholders like @ColumnName
}

export class PromptService {
  private redis: any;
  private readonly defaultPromptKey = 'settings:prompt:default';
  private readonly systemPromptKey = 'settings:prompt:system';
  private readonly templatesPrefix = 'settings:prompt:templates:';

  constructor() {
    this.redis = RedisManager.getClient(REDIS_DB.SESSIONS);
  }

  /**
   * NEW: Render a prompt template with context using the PlaceholderEngine
   */
  async renderPromptWithContext(template: string, context: PlaceholderContext): Promise<PlaceholderRenderResult> {
    try {
      // Use the PlaceholderEngine to render the template
      const result = PlaceholderEngine.renderTemplate(template, context);
      
      return result;
    } catch (error) {
      console.error('Error rendering prompt template:', error);
      throw new Error(`Failed to render prompt template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * NEW: Build context for template rendering from job data
   */
  async buildContextFromJobData(
    jobId: string,
    columnName: string,
    rowIndex: number,
    currentRow: any,
    options?: {
      jobNotes?: string;
      worksheetData?: any[];
      columnConfiguration?: any;
    }
  ): Promise<PlaceholderContext> {
    try {
      const context: PlaceholderContext = {
        jobId,
        columnName,
        currentRow,
        rowIndex,
        columnConfiguration: options?.columnConfiguration,
        jobNotes: options?.jobNotes,
        worksheetData: options?.worksheetData,
        sourceColumns: currentRow ? Object.keys(currentRow) : []
      };

      return context;
    } catch (error) {
      console.error('Error building context from job data:', error);
      throw new Error(`Failed to build context: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Default prompt management
  async getDefaultPrompt(): Promise<string> {
    try {
      const prompt = await this.redis.get(this.defaultPromptKey);
      if (prompt) {
        return prompt;
      }
      
      // Return a sensible default if none is set
      return this.getBuiltInDefaultPrompt();
    } catch (error) {
      console.error('Error getting default prompt:', error);
      return this.getBuiltInDefaultPrompt();
    }
  }

  async setDefaultPrompt(prompt: string): Promise<void> {
    try {
      await this.redis.set(this.defaultPromptKey, prompt);
      console.log('Default prompt updated successfully');
    } catch (error) {
      console.error('Error setting default prompt:', error);
      throw new Error('Failed to save default prompt');
    }
  }

  private getBuiltInDefaultPrompt(): string {
    return `Transformation instructions: @description

Validation data for reference: @prompt_additional_data
Only return the data in this column: @output_validation_field

Please use the information in the notes: @notes
Current row data: @row

Please provide your response in the expected format. Be precise and follow any constraints provided.`;
  }

  // System prompt management
  async getSystemPrompt(): Promise<string> {
    try {
      const systemPrompt = await this.redis.get(this.systemPromptKey);
      if (systemPrompt) {
        return systemPrompt;
      }
      
      // Return user's requested default if none is set
      return this.getBuiltInSystemPrompt();
    } catch (error) {
      console.error('Error getting system prompt:', error);
      return this.getBuiltInSystemPrompt();
    }
  }

  async setSystemPrompt(prompt: string): Promise<void> {
    try {
      await this.redis.set(this.systemPromptKey, prompt);
      console.log('System prompt updated successfully');
    } catch (error) {
      console.error('Error setting system prompt:', error);
      throw new Error('Failed to save system prompt');
    }
  }

  private getBuiltInSystemPrompt(): string {
    return "You are a senior ecommerce product data manager.";
  }

  // Template management
  async getPromptTemplates(): Promise<PromptTemplate[]> {
    try {
      const keys = await this.redis.keys(`${this.templatesPrefix}*`);
      const templates: PromptTemplate[] = [];
      
      for (const key of keys) {
        const templateData = await this.redis.get(key);
        if (templateData) {
          try {
            const template = JSON.parse(templateData);
            templates.push(template);
          } catch (parseError) {
            console.warn(`Failed to parse template data for key ${key}:`, parseError);
          }
        }
      }
      
      // Sort by usage count (descending) then by updated_at (newest first)
      return templates.sort((a, b) => {
        if (a.usage_count !== b.usage_count) {
          return b.usage_count - a.usage_count;
        }
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      });
    } catch (error) {
      console.error('Error getting prompt templates:', error);
      return [];
    }
  }

  async savePromptTemplate(template: Omit<PromptTemplate, 'id' | 'created_at' | 'updated_at' | 'usage_count'>): Promise<PromptTemplate> {
    try {
      const now = new Date().toISOString();
      const id = crypto.randomUUID();
      
      const fullTemplate: PromptTemplate = {
        ...template,
        id,
        created_at: now,
        updated_at: now,
        usage_count: 0,
        placeholders: this.extractPlaceholders(template.template)
      };
      
      const templateKey = `${this.templatesPrefix}${id}`;
      await this.redis.set(templateKey, JSON.stringify(fullTemplate));
      
      console.log(`Prompt template saved with ID: ${id}`);
      return fullTemplate;
    } catch (error) {
      console.error('Error saving prompt template:', error);
      throw new Error('Failed to save prompt template');
    }
  }

  async updatePromptTemplate(id: string, updates: Partial<PromptTemplate>): Promise<PromptTemplate | null> {
    try {
      const templateKey = `${this.templatesPrefix}${id}`;
      const existingData = await this.redis.get(templateKey);
      
      if (!existingData) {
        return null;
      }
      
      const existingTemplate = JSON.parse(existingData);
      const updatedTemplate: PromptTemplate = {
        ...existingTemplate,
        ...updates,
        id, // Ensure ID doesn't change
        updated_at: new Date().toISOString(),
        placeholders: updates.template ? this.extractPlaceholders(updates.template) : existingTemplate.placeholders
      };
      
      await this.redis.set(templateKey, JSON.stringify(updatedTemplate));
      
      console.log(`Prompt template updated: ${id}`);
      return updatedTemplate;
    } catch (error) {
      console.error('Error updating prompt template:', error);
      throw new Error('Failed to update prompt template');
    }
  }

  async deletePromptTemplate(id: string): Promise<boolean> {
    try {
      const templateKey = `${this.templatesPrefix}${id}`;
      const result = await this.redis.del(templateKey);
      
      if (result === 1) {
        console.log(`Prompt template deleted: ${id}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error deleting prompt template:', error);
      throw new Error('Failed to delete prompt template');
    }
  }

  async incrementTemplateUsage(id: string): Promise<void> {
    try {
      const templateKey = `${this.templatesPrefix}${id}`;
      const existingData = await this.redis.get(templateKey);
      
      if (existingData) {
        const template = JSON.parse(existingData);
        template.usage_count = (template.usage_count || 0) + 1;
        template.updated_at = new Date().toISOString();
        
        await this.redis.set(templateKey, JSON.stringify(template));
      }
    } catch (error) {
      console.warn('Error incrementing template usage:', error);
      // Don't throw - this is not critical
    }
  }

  // Utility methods
  extractPlaceholders(template: string): string[] {
    // Extract all @placeholder patterns
    const placeholderRegex = /@(\w+)/g;
    const placeholders: string[] = [];
    let match;
    
    while ((match = placeholderRegex.exec(template)) !== null) {
      const placeholder = `@${match[1]}`;
      if (!placeholders.includes(placeholder)) {
        placeholders.push(placeholder);
      }
    }
    
    return placeholders.sort();
  }

  validateTemplate(template: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check for basic syntax issues
    if (!template || template.trim().length === 0) {
      errors.push('Template cannot be empty');
    }
    
    // Check for balanced parentheses, brackets, etc.
    const brackets = { '(': ')', '[': ']', '{': '}' };
    const stack: string[] = [];
    
    for (const char of template) {
      if (char in brackets) {
        stack.push(char);
      } else if (Object.values(brackets).includes(char)) {
        const lastOpen = stack.pop();
        if (!lastOpen || brackets[lastOpen as keyof typeof brackets] !== char) {
          errors.push('Unmatched brackets or parentheses');
          break;
        }
      }
    }
    
    if (stack.length > 0) {
      errors.push('Unclosed brackets or parentheses');
    }
    
    // Check for potentially problematic patterns
    if (template.includes('@@')) {
      errors.push('Double @ symbols found - use single @ for placeholders');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  getBuiltInTemplates(): PromptTemplate[] {
    const now = new Date().toISOString();
    
    return [
      {
        id: 'builtin-data-transformation',
        name: 'Data Transformation',
        description: 'General purpose data transformation template',
        template: `Transform the following data according to the provided instructions.

Row data: @row
Instructions: @description
Additional notes: @notes

Please provide the transformed result.`,
        placeholders: ['@row', '@description', '@notes'],
        created_at: now,
        updated_at: now,
        usage_count: 0
      },
      {
        id: 'builtin-validation-check',
        name: 'Data Validation',
        description: 'Template for validating data against reference values',
        template: `Validate the following data against the reference information.

Current data: @row
Validation rules: @description
Reference data: @prompt_additional_data
Reference column: @output_validation_field

Provide validation result and corrected value if needed.`,
        placeholders: ['@row', '@description', '@prompt_additional_data', '@output_validation_field'],
        created_at: now,
        updated_at: now,
        usage_count: 0
      },
      {
        id: 'builtin-text-formatting',
        name: 'Text Formatting',
        description: 'Template for formatting and standardizing text',
        template: `Format the following text according to the style guide.

Text to format: @row
Formatting instructions: @description
Style guide: @styleGuide
Target audience: @audience

Provide the formatted text.`,
        placeholders: ['@row', '@description', '@styleGuide', '@audience'],
        created_at: now,
        updated_at: now,
        usage_count: 0
      }
    ];
  }
}

// Singleton instance
let promptServiceInstance: PromptService | null = null;

export function getPromptService(): PromptService {
  if (!promptServiceInstance) {
    promptServiceInstance = new PromptService();
  }
  return promptServiceInstance;
} 