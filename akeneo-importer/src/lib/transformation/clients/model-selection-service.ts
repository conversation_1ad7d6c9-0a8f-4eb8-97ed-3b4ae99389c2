export type LLMProvider = 'openrouter' | 'groq';

export interface LLMProviderConfig {
  provider: LLMProvider;
  models: any[];
  defaultModel: string;
  enabledModels: string[];
}

// Enhanced Model Selection Service with Provider Support
export class ModelSelectionService {
  private cacheServiceUrl: string;
  private authToken: string;
  private redis: any;

  constructor() {
    if (!process.env.LLM_CACHE_SERVICE_URL) {
      console.warn('⚠️ LLM_CACHE_SERVICE_URL is not defined in ModelSelectionService. Some features may not work.');
    }
    this.cacheServiceUrl = process.env.LLM_CACHE_SERVICE_URL || '';
    this.authToken = process.env.LLM_CACHE_AUTH_TOKEN || 'llm-cache-default-token-2024';
    
    // Initialize Redis connection for storing model settings
    try {
      // Import RedisManager for consistent connection handling
      const { RedisManager } = require('../../redis/redis-manager');
      const { REDIS_DB } = require('../../../types');
      this.redis = RedisManager.getClient(REDIS_DB.CACHE);
      console.log('✅ ModelSelectionService Redis connection initialized');
    } catch (error) {
      console.warn('Redis not available for model settings storage:', error);
      this.redis = null;
    }
  }

  async getCurrentProvider(): Promise<LLMProvider> {
    try {
      if (!this.redis) {
        return (process.env.GROQ_DEFAULT_MODEL ? 'groq' : 'openrouter') as LLMProvider;
      }
      
      const provider = await this.redis.get('llm:current_provider');
      if (provider && ['openrouter', 'groq'].includes(provider)) {
        return provider as LLMProvider;
      }
      
      // Default based on environment
      return (process.env.GROQ_DEFAULT_MODEL ? 'groq' : 'openrouter') as LLMProvider;
    } catch (error) {
      console.error('Error getting current provider:', error);
      return 'openrouter';
    }
  }

  async setCurrentProvider(provider: LLMProvider): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.set('llm:current_provider', provider);
        console.log(`✅ Set current LLM provider to: ${provider}`);
      }
    } catch (error) {
      console.error('Error setting current provider:', error);
    }
  }

  async getEnabledModels(provider?: LLMProvider): Promise<string[]> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      if (!this.redis) {
        // Fallback to environment-based defaults
        if (currentProvider === 'groq') {
          return [
            process.env.GROQ_DEFAULT_MODEL || 'qwen/qwen3-32b',
            'llama-3.3-70b-versatile',
            'llama-3.1-70b-versatile',
            'llama-3.1-8b-instant'
          ];
        } else {
          return [
            process.env.DEFAULT_MODEL || 'qwen/qwen3-32b',
            'gpt-4o-mini',
            'gpt-4o',
            'claude-3-5-sonnet-20241022'
          ];
        }
      }
      
      const enabledModels = await this.redis.get(`llm:enabled_models:${currentProvider}`);
      if (enabledModels) {
        return JSON.parse(enabledModels);
      }
      
      // Default enabled models for each provider
      const defaultModels = {
        groq: [
          process.env.GROQ_DEFAULT_MODEL || 'qwen/qwen3-32b',
          'llama-3.3-70b-versatile',
          'llama-3.1-70b-versatile',
          'llama-3.1-8b-instant'
        ],
        openrouter: [
          process.env.DEFAULT_MODEL || 'qwen/qwen3-32b',
          'gpt-4o-mini',
          'gpt-4o',
          'claude-3-5-sonnet-20241022',
          'deepseek/deepseek-r1-0528'
        ]
      };
      
      return defaultModels[currentProvider] || defaultModels.openrouter;
    } catch (error) {
      console.error('Error getting enabled models:', error);
      return [process.env.DEFAULT_MODEL || 'qwen/qwen3-32b'];
    }
  }

  async setEnabledModels(modelIds: string[], provider?: LLMProvider): Promise<void> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      if (this.redis) {
        await this.redis.set(`llm:enabled_models:${currentProvider}`, JSON.stringify(modelIds));
        console.log(`✅ Set enabled models for ${currentProvider}:`, modelIds);
      }
    } catch (error) {
      console.error('Error setting enabled models:', error);
    }
  }

  async getDefaultModel(provider?: LLMProvider): Promise<string> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      if (!this.redis) {
        // Fallback to environment variables
        if (currentProvider === 'groq') {
          return process.env.GROQ_DEFAULT_MODEL || 'qwen/qwen3-32b';
        } else {
          return process.env.DEFAULT_MODEL || 'qwen/qwen3-32b';
        }
      }
      
      const defaultModel = await this.redis.get(`llm:default_model:${currentProvider}`);
      if (defaultModel) {
        return defaultModel;
      }
      
      // Environment-based defaults
      if (currentProvider === 'groq') {
        return process.env.GROQ_DEFAULT_MODEL || 'qwen/qwen3-32b';
      } else {
        return process.env.DEFAULT_MODEL || 'qwen/qwen3-32b';
      }
    } catch (error) {
      console.error('Error getting default model:', error);
      return 'qwen/qwen3-32b';
    }
  }

  async setDefaultModel(modelId: string, provider?: LLMProvider): Promise<void> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      if (this.redis) {
        await this.redis.set(`llm:default_model:${currentProvider}`, modelId);
        console.log(`✅ Set default model for ${currentProvider}: ${modelId}`);
      }
    } catch (error) {
      console.error('Error setting default model:', error);
    }
  }

  async getDefaultTemperature(provider?: LLMProvider): Promise<number> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      if (!this.redis) {
        // Fallback to default temperature
        return 1;
      }
      
      const defaultTemperature = await this.redis.get(`llm:default_temperature:${currentProvider}`);
      if (defaultTemperature !== null) {
        const temp = parseFloat(defaultTemperature);
        // Validate temperature range
        if (temp >= 0 && temp <= 2) {
          return temp;
        }
      }
      
      // Default temperature if not set or invalid
      return 1;
    } catch (error) {
      console.error('Error getting default temperature:', error);
      return 1;
    }
  }

  async setDefaultTemperature(temperature: number, provider?: LLMProvider): Promise<void> {
    try {
      const currentProvider = provider || await this.getCurrentProvider();
      
      // Validate temperature range
      if (temperature < 0 || temperature > 2) {
        throw new Error('Temperature must be between 0 and 2');
      }
      
      if (this.redis) {
        await this.redis.set(`llm:default_temperature:${currentProvider}`, temperature.toString());
        console.log(`✅ Set default temperature for ${currentProvider}: ${temperature}`);
      }
    } catch (error) {
      console.error('Error setting default temperature:', error);
      throw error;
    }
  }

  async getAvailableModels(provider?: LLMProvider, forceRefresh: boolean = false): Promise<any[]> {
    const currentProvider = provider || await this.getCurrentProvider();
    
    if (currentProvider === 'groq') {
      const { GroqClient } = await import('./groq-client');
      const groqClient = new GroqClient();
      return groqClient.getAvailableModels(forceRefresh);
    } else {
      const { OpenRouterClient } = await import('./openrouter-client');
      const openRouterClient = new OpenRouterClient();
      return openRouterClient.getAvailableModels(forceRefresh);
    }
  }

  async getLLMClient(provider?: LLMProvider): Promise<any> {
    const currentProvider = provider || await this.getCurrentProvider();
    
    if (currentProvider === 'groq') {
      const { GroqClient } = await import('./groq-client');
      return new GroqClient();
    } else {
      const { OpenRouterClient } = await import('./openrouter-client');
      return new OpenRouterClient();
    }
  }
} 