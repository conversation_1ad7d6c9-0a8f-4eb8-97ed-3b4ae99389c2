import OpenAI from 'openai';
import crypto from 'crypto';
import { LoggingService } from '../../logging/logging-service';
import { ModelSelectionService } from './model-selection-service';
import { ResponseParser } from '../response-parser';

export interface GroqModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  active: boolean;
  context_window?: number;
}

export interface StructuredLLMResponse {
  reasoning: string;
  answer: string;
  metadata?: {
    model: string;
    reasoning_format: string;
    response_format: string;
    from_cache: boolean;
  };
}

export interface GroqChatOptions {
  reasoning_format?: 'hidden' | 'raw' | 'parsed';
  response_format?: {
    type: 'json_schema' | 'json_object';
    json_schema?: {
      name: string;
      strict?: boolean;
      schema: any;
    };
  };
}

// Model capability detection
export interface ModelCapabilities {
  supportsJsonSchema: boolean;
  supportsJsonObject: boolean;
  supportsReasoningFormat: boolean;
  supportsReasoningEffort: boolean;
}

export interface TransformationResult {
  success: boolean;
  transformedData: any;
  model: string;
  fromCache: boolean;
  processingTime: number;
  cacheKey?: string;
}

export class GroqClient {
  private client: OpenAI;
  private cacheServiceUrl: string;
  private authToken: string;

  constructor() {
    if (!process.env.GROQ_API_KEY) {
      throw new Error('GROQ_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      baseURL: "https://api.groq.com/openai/v1",
      apiKey: process.env.GROQ_API_KEY,
    });

    // Initialize cache service connection (optional)
    this.cacheServiceUrl = process.env.LLM_CACHE_SERVICE_URL || '';
    this.authToken = process.env.LLM_CACHE_AUTH_TOKEN || 'llm-cache-default-token-2024';
  }

  private getModelCapabilities(model: string): ModelCapabilities {
    // Groq model capabilities based on known models
    const capabilities: ModelCapabilities = {
      supportsJsonSchema: false,
      supportsJsonObject: false,
      supportsReasoningFormat: false,
      supportsReasoningEffort: false
    };

    // Most Groq models support JSON object format
    if (model.includes('llama') || model.includes('qwen') || model.includes('gemma')) {
      capabilities.supportsJsonObject = true;
    }

    // Reasoning models support reasoning format
    if (model.includes('qwen') || model.includes('deepseek')) {
      capabilities.supportsReasoningFormat = true;
    }

    return capabilities;
  }

  private generateCacheKey(messages: any[], model: string, temperature: number = 0.1, responseFormat: string = 'json_object', reasoningFormat?: string): string {
    const content = JSON.stringify({ messages, model, temperature, responseFormat, reasoningFormat });
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private async getCachedResult(cacheKey: string): Promise<any | null> {
    if (!this.cacheServiceUrl) return null;
    
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/get/${cacheKey}`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data && result.data.content) {
          return result.data;
        }
      }
      
      return null;
    } catch (error) {
      console.warn('Cache service unavailable:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  private async setCachedResult(cacheKey: string, data: any, model?: string): Promise<void> {
    if (!this.cacheServiceUrl) return;
    
    try {
      await fetch(`${this.cacheServiceUrl}/api/cache/set`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({ 
          key: cacheKey, 
          data: {
            ...data,
            model: model || 'unknown',
            timestamp: new Date().toISOString()
          }
        }),
      });
    } catch (error) {
      console.warn('Failed to cache result:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async chat(
    messages: any[],
    model?: string,
    useJsonFormat: boolean = true,
    bypassCache: boolean = false,
    options?: GroqChatOptions,
    temperature?: number
  ): Promise<string> {
    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('groq'));
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('groq'));
    const { content } = await this.chatWithMetadata(messages, selectedModel, useJsonFormat, bypassCache, options, finalTemperature);
    return content;
  }

  async chatWithMetadata(
    messages: any[],
    model?: string,
    useJsonFormat: boolean = true,
    bypassCache: boolean = false,
    options?: GroqChatOptions,
    temperature?: number
  ): Promise<{ content: string; fromCache: boolean }> {
    const startTime = Date.now();
    
    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('groq'));
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('groq'));
    const responseFormat = useJsonFormat ? 'json_object' : 'text';
    const cacheKey = this.generateCacheKey(messages, selectedModel, finalTemperature, responseFormat, options?.reasoning_format);

    // Check cache first (unless bypassed)
    if (!bypassCache) {
      const cached = await this.getCachedResult(cacheKey);
      if (cached && cached.content && cached.content.trim()) {
        console.log(`✅ Cache hit for Groq model ${selectedModel}`);
        return { content: cached.content, fromCache: true };
      }
    }

    // LLM logging is now handled by the transformation engine
    
    const capabilities = this.getModelCapabilities(selectedModel);
    const requestOptions: any = {
      model: selectedModel,
      messages: messages,
      temperature: finalTemperature,
    };

    // Add response format if supported
    if (useJsonFormat && capabilities.supportsJsonObject) {
      requestOptions.response_format = { type: 'json_object' };
    }

    // Add reasoning format if supported and requested
    if (options?.reasoning_format && capabilities.supportsReasoningFormat) {
      requestOptions.reasoning_format = options.reasoning_format;
    }

    const response = await this.client.chat.completions.create(requestOptions);

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('Groq LLM response was empty');
    }

    // Store in cache
    await this.setCachedResult(cacheKey, { content, model: selectedModel });
    
    const duration = Date.now() - startTime;
    // LLM completion logging is now handled by the transformation engine

    return { content, fromCache: false };
  }

  async chatStructured(
    messages: any[],
    model?: string,
    bypassCache: boolean = false,
    showReasoning: boolean = true,
    temperature?: number,
    logger?: LoggingService
  ): Promise<StructuredLLMResponse> {
    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('groq'));
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('groq'));
    const capabilities = this.getModelCapabilities(selectedModel);
    
    let response: { content: string; fromCache: boolean };
    
    if (capabilities.supportsReasoningFormat && showReasoning) {
      // Use native reasoning format for supported models
      response = await this.chatWithMetadata(
        messages,
        selectedModel,
        false, // Don't force JSON for reasoning models
        bypassCache,
        { reasoning_format: 'parsed' },
        finalTemperature
      );
      
      try {
        // Try to parse as structured response first
        const parsed = JSON.parse(response.content);
        if (parsed.reasoning && parsed.answer) {
          return {
            reasoning: parsed.reasoning,
            answer: parsed.answer,
            metadata: {
              model: selectedModel,
              reasoning_format: 'native',
              response_format: 'structured',
              from_cache: response.fromCache
            }
          };
        }
      } catch {
        // If parsing fails, treat as raw response and extract
      }
    }
    
    // Fallback: Use structured prompting
    const structuredPrompt = ResponseParser.generateStructuredPrompt(
      messages[messages.length - 1].content,
      showReasoning
    );
    
    const structuredMessages = [
      ...messages.slice(0, -1),
      { ...messages[messages.length - 1], content: structuredPrompt }
    ];
    
    response = await this.chatWithMetadata(
      structuredMessages,
      selectedModel,
      false, // Use text format for structured prompting
      bypassCache,
      undefined,
      finalTemperature
    );
    
    // Parse the structured response
    const parsed = ResponseParser.extractAnswerFromResponse(response.content);
    
    if (logger) {
      logger.log({
        event: 'llm_request',
        model: selectedModel,
        messages: structuredMessages,
        response: response.content,
        reasoning: parsed.reasoning,
        answer: parsed.answer,
        fromCache: response.fromCache,
        timestamp: new Date().toISOString()
      });
    }
    
    return {
      reasoning: parsed.reasoning,
      answer: parsed.answer,
      metadata: {
        model: selectedModel,
        reasoning_format: 'structured_prompt',
        response_format: 'parsed',
        from_cache: response.fromCache
      }
    };
  }

  async transformData(
    data: any,
    prompt: string,
    model?: string,
    bypassCache: boolean = false
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('groq'));
    
    const messages = [
      {
        role: "system" as const,
        content: "You are a data transformation assistant. You will receive a detailed prompt with all necessary context and data. Your response must be only the single, transformed value. Do not add any extra text, explanation, or JSON formatting."
      },
      {
        role: "user" as const,
        content: prompt
      }
    ];

    try {
      const response = await this.chat(messages, selectedModel, false, bypassCache);
      const transformedData = response;
      const processingTime = Date.now() - startTime;

      return {
        success: true,
        transformedData,
        model: selectedModel,
        fromCache: false,
        processingTime,
        cacheKey: this.generateCacheKey(messages, selectedModel, 0.1, 'text')
      };
    } catch (error) {
      console.error('Groq data transformation error:', error);
      return {
        success: false,
        transformedData: null,
        model: selectedModel,
        fromCache: false,
        processingTime: Date.now() - startTime
      };
    }
  }

  async getAvailableModels(forceRefresh: boolean = false): Promise<GroqModel[]> {
    try {
      const response = await fetch('https://api.groq.com/openai/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error(`Groq API error: ${response.status} ${response.statusText}`);
        // Fallback to known models
        return [
          { id: 'qwen/qwen3-32b', object: 'model', created: 0, owned_by: 'groq', active: true },
          { id: 'llama-3.3-70b-versatile', object: 'model', created: 0, owned_by: 'groq', active: true },
          { id: 'llama-3.1-70b-versatile', object: 'model', created: 0, owned_by: 'groq', active: true },
          { id: 'llama-3.1-8b-instant', object: 'model', created: 0, owned_by: 'groq', active: true }
        ];
      }

      const data = await response.json();
      
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid response format from Groq API');
      }

      const models: GroqModel[] = data.data
        .filter((model: any) => model.active)
        .map((model: any) => ({
          id: model.id,
          object: model.object,
          created: model.created,
          owned_by: model.owned_by,
          active: model.active,
          context_window: model.context_window
        }));

      console.log(`✅ Fetched ${models.length} active models from Groq API`);
      return models;
    } catch (error) {
      console.error('Failed to fetch Groq models:', error);
      return [
        { id: 'qwen/qwen3-32b', object: 'model', created: 0, owned_by: 'groq', active: true }
      ];
    }
  }

  async clearCache(pattern?: string): Promise<number> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/clear${pattern ? `?pattern=${pattern}` : ''}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to clear cache: ${response.statusText}`);
      }

      const result = await response.json();
      return result.clearedCount || 0;
    } catch (error) {
      console.error('Cache clear error:', error);
      throw new Error(`Failed to clear cache: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: number;
    hitRate?: number;
  }> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/stats`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get cache stats: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        totalKeys: result.stats?.totalKeys || 0,
        memoryUsage: result.stats?.memoryUsage || 0,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        totalKeys: 0,
        memoryUsage: 0,
      };
    }
  }

  async getDefaultModel(): Promise<string> {
    return process.env.GROQ_DEFAULT_MODEL || 'qwen/qwen3-32b';
  }
} 