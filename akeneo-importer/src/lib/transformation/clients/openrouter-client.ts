import OpenAI from 'openai';
import crypto from 'crypto';
import type { LLMRequest, LLMResponse } from '@/types';
import { ModelSelectionService } from './model-selection-service';

export interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  pricing?: {
    prompt: string;
    completion: string;
  };
  context_length?: number;
  architecture?: string;
}

export interface TransformationResult {
  success: boolean;
  transformedData: any;
  model: string;
  fromCache: boolean;
  processingTime: number;
  cacheKey?: string;
}

export class OpenRouterClient {
  private client: OpenAI;
  private cacheServiceUrl: string;
  private authToken: string;

  constructor() {
    // Initialize OpenAI client for direct calls
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    });

    // Initialize cache service connection (optional)
    this.cacheServiceUrl = process.env.LLM_CACHE_SERVICE_URL || '';
    this.authToken = process.env.LLM_CACHE_AUTH_TOKEN || 'llm-cache-default-token-2024';
  }

  private generateCacheKey(messages: any[], model: string, temperature: number = 0.1, responseFormat: string = 'json_object'): string {
    // Create a more comprehensive cache key that includes message content hash
    const content = JSON.stringify({ 
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })), 
      model, 
      temperature, 
      responseFormat 
    });
    
    const hash = crypto.createHash('sha256').update(content).digest('hex');
    
    // Log first 8 characters for debugging
    console.log(`🔑 Generated cache key ${hash.substring(0, 8)}... for model ${model}`);
    console.log(`🔍 Cache key content preview:`, {
      model,
      temperature,
      responseFormat,
      messagesCount: messages.length,
      firstMessageRole: messages[0]?.role,
      firstMessageLength: messages[0]?.content?.length,
      firstMessagePreview: messages[0]?.content?.substring(0, 100) + '...',
      userMessageLength: messages[1]?.content?.length,
      userMessagePreview: messages[1]?.content?.substring(0, 100) + '...'
    });
    
    return hash;
  }

  private async getCachedResult(cacheKey: string): Promise<any | null> {
    if (!this.cacheServiceUrl) return null;
    
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/get/${cacheKey}`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`🔍 Cache response for key ${cacheKey.substring(0, 8)}...:`, {
          success: result.success,
          hasData: !!result.data,
          hasContent: !!(result.data && result.data.content),
          contentLength: result.data && result.data.content ? result.data.content.length : 0
        });
        
        // Only return cached data if the cache service indicates success AND has actual data
        if (result.success && result.data && result.data.content) {
          return result.data;
        }
      } else {
        console.log(`🔍 Cache miss for key ${cacheKey.substring(0, 8)}...: HTTP ${response.status}`);
      }
      
      // Return null for any other case (404, no success, no data, etc.)
      // This will trigger a fresh LLM call
      return null;
    } catch (error) {
      console.warn('Cache service unavailable:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  private async setCachedResult(cacheKey: string, data: any): Promise<void> {
    if (!this.cacheServiceUrl) return;
    
    try {
      await fetch(`${this.cacheServiceUrl}/api/cache/set`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({ key: cacheKey, data }),
      });
    } catch (error) {
      console.warn('Failed to cache result:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async chat(
    messages: any[],
    model?: string,
    useJsonFormat: boolean = true,
    bypassCache: boolean = false,
    temperature?: number
  ): Promise<string> {
    const startTime = Date.now();
    
    // Enforce mandatory cache usage
    if (bypassCache) {
      console.warn('CACHE BYPASS ATTEMPTED in OpenRouterClient: Forcing request through cache as per new policy.');
    }

    const modelService = new ModelSelectionService();
    const selectedModel = model || (await modelService.getDefaultModel('openrouter'));
    const finalTemperature = temperature ?? (await modelService.getDefaultTemperature('openrouter'));
    const responseFormat = useJsonFormat ? 'json_object' : 'text';
    const cacheKey = this.generateCacheKey(messages, selectedModel, finalTemperature, responseFormat);

    // Always check cache first
    const cached = await this.getCachedResult(cacheKey);
    if (cached && cached.content && cached.content.trim()) {
        // Cache hit logging is now handled by the transformation engine
        return cached.content;
    } else if (cached) {
        // Cache hit with empty content - this will be logged as a cache miss
    }

    // LLM logging is now handled by the transformation engine
    
    const response = await this.client.chat.completions.create({
      model: selectedModel,
      messages: messages,
      temperature: finalTemperature,
      response_format: { type: responseFormat },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('LLM response was empty');
    }

    // Store in cache
    await this.setCachedResult(cacheKey, { content, model: selectedModel });
    
    const duration = Date.now() - startTime;
    // LLM completion logging is now handled by the transformation engine

    return content;
  }

  async transformData(
    data: any,
    prompt: string,
    model?: string,
    bypassCache: boolean = false
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    const selectedModel = model || process.env.DEFAULT_MODEL || 'qwen/qwen3-32b';
    
    // Construct messages for transformation
    // The 'prompt' is already fully contextualized by the TransformationEngine
    const messages = [
      {
        role: "system" as const,
        content: "You are a data transformation assistant. You will receive a detailed prompt with all necessary context and data. Your response must be only the single, transformed value. Do not add any extra text, explanation, or JSON formatting."
      },
      {
        role: "user" as const,
        content: prompt
      }
    ];

    try {
      // For this direct transformation, we expect a raw value, not JSON
      const response = await this.chat(messages, selectedModel, false, bypassCache);

      // The response is the transformed data directly
      const transformedData = response;

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        transformedData,
        model: selectedModel,
        fromCache: false, // The chat method handles cache detection
        processingTime,
        cacheKey: this.generateCacheKey(messages, selectedModel, 0.1, 'text')
      };
    } catch (error) {
      console.error('Data transformation error:', error);
      return {
        success: false,
        transformedData: null,
        model: selectedModel,
        fromCache: false,
        processingTime: Date.now() - startTime
      };
    }
  }

  async getAvailableModels(forceRefresh: boolean = false): Promise<OpenRouterModel[]> {
    try {
      // Fetch models directly from OpenRouter API
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        // Fallback to basic models if API fails
        return [
          { id: 'qwen/qwen3-32b', name: 'Qwen 3 32B', description: 'Qwen reasoning model with structured output (default)' },
          { id: 'deepseek/deepseek-r1-0528', name: 'DeepSeek R1', description: 'DeepSeek reasoning model' },
          { id: 'gpt-4o-mini', name: 'GPT-4O Mini', description: 'Fast and efficient model' },
          { id: 'gpt-4o', name: 'GPT-4O', description: 'Advanced model' },
          { id: 'gpt-4.1-mini', name: 'GPT-4.1 Mini', description: 'Latest mini model' },
          { id: 'gpt-4.1', name: 'GPT-4.1', description: 'Latest full model' },
          { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: 'Anthropic Claude model' },
          { id: 'meta-llama/llama-3.2-90b-vision-instruct', name: 'Llama 3.2 90B Vision', description: 'Meta Llama vision model' }
        ];
      }

      const data = await response.json();
      
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid response format from OpenRouter API');
      }

      // Transform OpenRouter API response to our format
      const models: OpenRouterModel[] = data.data.map((model: any) => ({
        id: model.id,
        name: model.name || model.id,
        description: model.description || '',
        pricing: model.pricing ? {
          prompt: model.pricing.prompt || '0',
          completion: model.pricing.completion || '0'
        } : undefined,
        context_length: model.context_length || 4096,
        architecture: model.architecture?.instruct_type || model.architecture?.modality
      }));

      console.log(`✅ Fetched ${models.length} models from OpenRouter API`);
      return models;
    } catch (error) {
      console.error('Failed to fetch available models:', error);
      // Return fallback models with Qwen as default
      return [
        { id: 'qwen/qwen3-32b', name: 'Qwen 3 32B', description: 'Qwen reasoning model with structured output (default)' }
      ];
    }
  }

  async clearCache(pattern?: string): Promise<number> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/cache/clear${pattern ? `?pattern=${pattern}` : ''}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to clear cache: ${response.statusText}`);
      }

      const result = await response.json();
      return result.clearedCount || 0;
    } catch (error) {
      console.error('Cache clear error:', error);
      throw new Error(`Failed to clear cache: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: number;
    hitRate?: number;
  }> {
    try {
      const response = await fetch(`${this.cacheServiceUrl}/api/stats`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get cache stats: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        totalKeys: result.stats?.totalKeys || 0,
        memoryUsage: result.stats?.memoryUsage || 0,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        totalKeys: 0,
        memoryUsage: 0,
      };
    }
  }
} 