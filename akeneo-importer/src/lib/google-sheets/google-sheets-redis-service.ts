import { RedisManager, getSessionsClient } from '@/lib/redis/redis-manager';
import { REDIS_DB } from '@/types';
import { GoogleSheetsService } from './google-sheets-service';
import { MappingDefinition, ValidationConfig } from '@/types';

export interface ConfigurationMetadata {
  lastRefresh: string;
  version: string;
  worksheetCount: number;
  mappingDefinitionsCount: number;
  validationTablesCount: number;
  spreadsheetId: string;
  checksum: string; // For change detection
}

export interface CachedValidationData {
  data: string[];
  worksheet: string;
  column: string;
  lastUpdate: string;
  checksum: string;
}

export class GoogleSheetsRedisService {
  private redis;
  private googleSheetsService: GoogleSheetsService;
  private readonly CACHE_TTL = 300; // 5 minutes
  private readonly CONFIG_VERSION = '1.0';

  constructor(googleSheetsService: GoogleSheetsService) {
    this.redis = getSessionsClient();
    this.googleSheetsService = googleSheetsService;
  }

  /**
   * Enhanced configuration storage with Redis backing
   * Stores all configuration data in Redis with proper TTL and versioning
   */
  async storeConfigurationInRedis(force: boolean = false): Promise<ConfigurationMetadata> {
    const cacheKey = 'google_sheets:configuration';
    const metadataKey = 'google_sheets:configuration:metadata';
    
    // Check if we need to refresh
    if (!force) {
      const existingMetadata = await this.redis.get(metadataKey);
      if (existingMetadata) {
        const metadata: ConfigurationMetadata = JSON.parse(existingMetadata);
        const age = Date.now() - new Date(metadata.lastRefresh).getTime();
        if (age < this.CACHE_TTL * 1000) {
          return metadata; // Return cached metadata if still fresh
        }
      }
    }

    try {
      // Load fresh configuration from Google Sheets
      const mappingDefinitions = await this.googleSheetsService.readMappingDefinitions(false);
      const worksheetNames = await this.googleSheetsService.getSheetNames(
        process.env.GOOGLE_MAPPING_SHEET_ID || ''
      );

      // Load all validation data
      const validationData: Record<string, CachedValidationData> = {};
      let validationTablesCount = 0;

      for (const definition of mappingDefinitions) {
        if (definition.validation_config?.worksheet_name) {
          const worksheetName = definition.validation_config.worksheet_name;
          const columnName = definition.validation_config.column_name || 'Code';
          
          try {
            const data = await this.googleSheetsService.getValidationData(
              worksheetName, 
              columnName, 
              false
            );
            
            const checksum = this.generateChecksum(data);
            validationData[worksheetName] = {
              data,
              worksheet: worksheetName,
              column: columnName,
              lastUpdate: new Date().toISOString(),
              checksum
            };
            validationTablesCount++;
          } catch (error) {
            console.warn(`Failed to load validation data for ${worksheetName}:`, error);
          }
        }
      }

      // Create configuration package
      const configurationPackage = {
        mappingDefinitions,
        worksheetNames,
        validationData,
        generatedAt: new Date().toISOString(),
        version: this.CONFIG_VERSION
      };

      // Generate checksum for change detection
      const checksum = this.generateChecksum(configurationPackage);

      // Store in Redis with TTL
      await this.redis.setex(
        cacheKey, 
        this.CACHE_TTL, 
        JSON.stringify(configurationPackage)
      );

      // Store individual validation tables for efficient access
      for (const [worksheetName, validationInfo] of Object.entries(validationData)) {
        await this.redis.setex(
          `google_sheets:validation:${worksheetName}`,
          this.CACHE_TTL,
          JSON.stringify(validationInfo)
        );
      }

      // Create and store metadata
      const metadata: ConfigurationMetadata = {
        lastRefresh: new Date().toISOString(),
        version: this.CONFIG_VERSION,
        worksheetCount: worksheetNames.length,
        mappingDefinitionsCount: mappingDefinitions.length,
        validationTablesCount,
        spreadsheetId: process.env.GOOGLE_MAPPING_SHEET_ID || '',
        checksum
      };

      await this.redis.setex(metadataKey, this.CACHE_TTL, JSON.stringify(metadata));

      console.log(`✅ Configuration stored in Redis: ${mappingDefinitions.length} definitions, ${validationTablesCount} validation tables`);
      return metadata;

    } catch (error) {
      console.error('❌ Failed to store configuration in Redis:', error);
      throw new Error(`Configuration storage failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Load configuration entirely from Redis
   * Zero Google Sheets API calls for cached data
   */
  async loadConfigurationFromRedis(): Promise<{
    mappingDefinitions: MappingDefinition[];
    worksheetNames: string[];
    validationData: Record<string, CachedValidationData>;
    metadata: ConfigurationMetadata;
  } | null> {
    try {
      const cacheKey = 'google_sheets:configuration';
      const metadataKey = 'google_sheets:configuration:metadata';

      const [configData, metadataData] = await Promise.all([
        this.redis.get(cacheKey),
        this.redis.get(metadataKey)
      ]);

      if (!configData || !metadataData) {
        return null; // Cache miss
      }

      const configuration = JSON.parse(configData);
      const metadata: ConfigurationMetadata = JSON.parse(metadataData);

      return {
        mappingDefinitions: configuration.mappingDefinitions,
        worksheetNames: configuration.worksheetNames,
        validationData: configuration.validationData,
        metadata
      };

    } catch (error) {
      console.error('Failed to load configuration from Redis:', error);
      return null;
    }
  }

  /**
   * Server-side configuration validation
   * Validates configuration integrity and structure
   */
  async validateConfiguration(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    summary: {
      totalColumns: number;
      requiredColumns: number;
      validationTables: number;
      missingValidationTables: string[];
    };
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Load configuration from Redis
      const config = await this.loadConfigurationFromRedis();
      
      if (!config) {
        errors.push('No configuration found in Redis. Please refresh configuration.');
        return {
          isValid: false,
          errors,
          warnings,
          summary: { totalColumns: 0, requiredColumns: 0, validationTables: 0, missingValidationTables: [] }
        };
      }

      const { mappingDefinitions, validationData } = config;
      const missingValidationTables: string[] = [];

      // Validate mapping definitions structure
      if (!Array.isArray(mappingDefinitions) || mappingDefinitions.length === 0) {
        errors.push('No mapping definitions found in configuration');
      }

      let requiredColumns = 0;
      const validationTableRefs = new Set<string>();

      // Validate each mapping definition
      for (const [index, definition] of mappingDefinitions.entries()) {
        // Required field validation
        if (!definition.column_name) {
          errors.push(`Mapping definition ${index + 1}: Missing column_name`);
        }
        
        if (typeof definition.required !== 'boolean') {
          warnings.push(`Mapping definition ${index + 1} (${definition.column_name}): 'required' should be boolean`);
        }

        if (definition.required) {
          requiredColumns++;
        }

        // Validation config validation
        if (definition.validation_config?.worksheet_name) {
          const worksheetName = definition.validation_config.worksheet_name;
          validationTableRefs.add(worksheetName);

          // Check if validation data exists
          if (!validationData[worksheetName]) {
            missingValidationTables.push(worksheetName);
            errors.push(`Missing validation data for worksheet: ${worksheetName} (referenced by ${definition.column_name})`);
          }
        }
      }

      // Check for unused validation tables
      for (const worksheetName of Object.keys(validationData)) {
        if (!validationTableRefs.has(worksheetName)) {
          warnings.push(`Validation table '${worksheetName}' is loaded but not referenced by any column`);
        }
      }

      // Performance warnings
      if (mappingDefinitions.length > 50) {
        warnings.push(`Large number of columns (${mappingDefinitions.length}). Consider optimizing for performance.`);
      }

      if (Object.keys(validationData).length > 20) {
        warnings.push(`Large number of validation tables (${Object.keys(validationData).length}). Consider consolidating.`);
      }

      const isValid = errors.length === 0;

      return {
        isValid,
        errors,
        warnings,
        summary: {
          totalColumns: mappingDefinitions.length,
          requiredColumns,
          validationTables: Object.keys(validationData).length,
          missingValidationTables
        }
      };

    } catch (error) {
      errors.push(`Validation failed: ${error instanceof Error ? error.message : String(error)}`);
      return {
        isValid: false,
        errors,
        warnings,
        summary: { totalColumns: 0, requiredColumns: 0, validationTables: 0, missingValidationTables: [] }
      };
    }
  }

  /**
   * Configuration change detection
   * Detects if remote configuration has changed since last cache
   */
  async detectConfigurationChanges(): Promise<{
    hasChanges: boolean;
    lastCachedChecksum: string | null;
    currentChecksum: string | null;
    changeDetails?: {
      mappingDefinitionsChanged: boolean;
      validationDataChanged: boolean;
      worksheetsChanged: boolean;
    };
  }> {
    try {
      // Get cached metadata
      const cachedConfig = await this.loadConfigurationFromRedis();
      const lastCachedChecksum = cachedConfig?.metadata.checksum || null;

      // Generate current checksum by loading fresh data
      const freshMappingDefinitions = await this.googleSheetsService.readMappingDefinitions(false);
      const freshWorksheetNames = await this.googleSheetsService.getSheetNames(
        process.env.GOOGLE_MAPPING_SHEET_ID || ''
      );

      const currentData = {
        mappingDefinitions: freshMappingDefinitions,
        worksheetNames: freshWorksheetNames
      };
      
      const currentChecksum = this.generateChecksum(currentData);

      return {
        hasChanges: lastCachedChecksum !== currentChecksum,
        lastCachedChecksum,
        currentChecksum,
        changeDetails: cachedConfig ? {
          mappingDefinitionsChanged: this.generateChecksum(cachedConfig.mappingDefinitions) !== this.generateChecksum(freshMappingDefinitions),
          validationDataChanged: false, // Detailed check would require loading all validation data
          worksheetsChanged: this.generateChecksum(cachedConfig.worksheetNames) !== this.generateChecksum(freshWorksheetNames)
        } : undefined
      };

    } catch (error) {
      console.error('Failed to detect configuration changes:', error);
      return {
        hasChanges: true, // Assume changes when detection fails
        lastCachedChecksum: null,
        currentChecksum: null
      };
    }
  }

  /**
   * Invalidate configuration cache
   * Force refresh on next access
   */
  async invalidateConfigurationCache(): Promise<void> {
    try {
      const keysToDelete = [
        'google_sheets:configuration',
        'google_sheets:configuration:metadata'
      ];

      // Also delete all validation cache keys
      const validationKeys = await this.redis.keys('google_sheets:validation:*');
      keysToDelete.push(...validationKeys);

      // Delete all keys
      if (keysToDelete.length > 0) {
        await this.redis.del(...keysToDelete);
        console.log(`🗑️  Invalidated ${keysToDelete.length} configuration cache keys`);
      }

      // Clear the in-memory cache in GoogleSheetsService as well
      this.googleSheetsService.clearCache();

    } catch (error) {
      console.error('Failed to invalidate configuration cache:', error);
      throw error;
    }
  }

  /**
   * Get configuration performance statistics
   */
  async getConfigurationStats(): Promise<{
    cacheStatus: 'hit' | 'miss' | 'expired';
    cacheAge: number; // seconds
    cacheSize: number; // bytes estimate
    validationTablesCount: number;
    lastRefresh: string | null;
    nextRefreshDue: string | null;
  }> {
    try {
      const config = await this.loadConfigurationFromRedis();
      
      if (!config) {
        return {
          cacheStatus: 'miss',
          cacheAge: 0,
          cacheSize: 0,
          validationTablesCount: 0,
          lastRefresh: null,
          nextRefreshDue: null
        };
      }

      const cacheAge = Math.floor((Date.now() - new Date(config.metadata.lastRefresh).getTime()) / 1000);
      const isExpired = cacheAge > this.CACHE_TTL;
      
      const cacheSize = JSON.stringify(config).length; // Rough estimate
      const nextRefreshDue = new Date(
        new Date(config.metadata.lastRefresh).getTime() + (this.CACHE_TTL * 1000)
      ).toISOString();

      return {
        cacheStatus: isExpired ? 'expired' : 'hit',
        cacheAge,
        cacheSize,
        validationTablesCount: config.metadata.validationTablesCount,
        lastRefresh: config.metadata.lastRefresh,
        nextRefreshDue
      };

    } catch (error) {
      console.error('Failed to get configuration stats:', error);
      throw error;
    }
  }

  /**
   * Generate checksum for change detection
   */
  private generateChecksum(data: any): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(JSON.stringify(data)).digest('hex').substring(0, 16);
  }
}

// Singleton instance
let googleSheetsRedisService: GoogleSheetsRedisService | null = null;

export function getGoogleSheetsRedisService(): GoogleSheetsRedisService {
  if (!googleSheetsRedisService) {
    const { getGoogleSheetsService } = require('./google-sheets-service');
    const googleSheetsService = getGoogleSheetsService();
    googleSheetsRedisService = new GoogleSheetsRedisService(googleSheetsService);
  }
  return googleSheetsRedisService;
} 