import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { GoogleSheetsConfig, GoogleSheetsAuth, MappingDefinition } from '@/types';
import { getGoogleSheetsClient } from '@/lib/redis';
import Redis from 'ioredis';

export class GoogleSheetsService {
  private oauth2Client: OAuth2Client;
  private sheets: any;
  private config: GoogleSheetsConfig;
  private mappingSheetId: string;
  private redisClient: Redis;
  private tokenKey = 'google:oauth:tokens';
  
  // Cache for performance
  private worksheetCache = new Map<string, any>();
  private validationCache = new Map<string, string[]>();
  private mappingDefinitionsCache: MappingDefinition[] | null = null;
  private cacheTimestamp: number | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor(config: GoogleSheetsConfig) {
    this.config = config;
    this.mappingSheetId = process.env.GOOGLE_MAPPING_SHEET_ID || '';
    this.redisClient = getGoogleSheetsClient();
    
    this.oauth2Client = new OAuth2Client(
      config.client_id,
      config.client_secret,
      config.redirect_uri
    );
    
    this.sheets = google.sheets({ version: 'v4', auth: this.oauth2Client });
  }

  /**
   * Generate OAuth URL for Google Sheets authentication
   */
  getAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/spreadsheets.readonly',
      'https://www.googleapis.com/auth/drive.readonly'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  /**
   * Handle OAuth callback and store credentials
   */
  async handleOAuthCallback(code: string): Promise<boolean> {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      this.oauth2Client.setCredentials(tokens);
      
      // Store tokens to file
      await this.storeCredentials({
        access_token: tokens.access_token!,
        refresh_token: tokens.refresh_token || undefined,
        expires_at: tokens.expiry_date || undefined
      });
      
      return true;
    } catch (error) {
      console.error('OAuth callback error:', error);
      return false;
    }
  }

  /**
   * Check if authenticated with Google Sheets
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const credentials = await this.loadCredentials();
      if (!credentials) return false;

      this.oauth2Client.setCredentials({
        access_token: credentials.access_token,
        refresh_token: credentials.refresh_token,
        expiry_date: credentials.expires_at
      });

      // Check if token is still valid
      const now = Date.now();
      if (credentials.expires_at && credentials.expires_at < now) {
        // Try to refresh token
        try {
          const { credentials: newCredentials } = await this.oauth2Client.refreshAccessToken();
          await this.storeCredentials({
            access_token: newCredentials.access_token!,
            refresh_token: newCredentials.refresh_token || credentials.refresh_token,
            expires_at: newCredentials.expiry_date || undefined
          });
          return true;
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Auth check error:', error);
      return false;
    }
  }

  /**
   * Load stored credentials from Redis
   */
  private async loadCredentials(): Promise<GoogleSheetsAuth | null> {
    try {
      const data = await this.redisClient.get(this.tokenKey);
      if (!data) return null;
      return JSON.parse(data);
    } catch (error) {
      console.error('Error loading credentials from Redis:', error);
      return null;
    }
  }

  /**
   * Store credentials to Redis
   */
  private async storeCredentials(auth: GoogleSheetsAuth): Promise<void> {
    try {
      const data = JSON.stringify(auth);
      await this.redisClient.set(this.tokenKey, data);
      
      // Set expiration if we have an expiry date
      if (auth.expires_at) {
        const ttlSeconds = Math.max(0, Math.floor((auth.expires_at - Date.now()) / 1000));
        if (ttlSeconds > 0) {
          await this.redisClient.expire(this.tokenKey, ttlSeconds);
        }
      }
    } catch (error) {
      console.error('Failed to store credentials to Redis:', error);
      throw error;
    }
  }

  /**
   * Get sheet names from a spreadsheet
   */
  async getSheetNames(spreadsheetId: string): Promise<string[]> {
    if (!await this.isAuthenticated()) {
      throw new Error('Not authenticated with Google Sheets');
    }

    try {
      const response = await this.sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets.properties.title'
      });

      return response.data.sheets?.map((sheet: any) => sheet.properties.title) || [];
    } catch (error) {
      console.error('Error getting sheet names:', error);
      throw error;
    }
  }

  /**
   * Read data from a specific sheet range
   */
  async readSheetData(spreadsheetId: string, range: string): Promise<any[][]> {
    if (!await this.isAuthenticated()) {
      throw new Error('Not authenticated with Google Sheets');
    }

    try {
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId,
        range,
        valueRenderOption: 'FORMATTED_VALUE'
      });

      return response.data.values || [];
    } catch (error) {
      console.error('Error reading sheet data:', error);
      throw error;
    }
  }

  /**
   * Read mapping definitions from the main configuration sheet
   */
  async readMappingDefinitions(useCache: boolean = true): Promise<MappingDefinition[]> {
    if (useCache && this.isCacheValid() && this.mappingDefinitionsCache) {
      return this.mappingDefinitionsCache;
    }

    try {
      const values = await this.readSheetData(this.mappingSheetId, 'Main!A:Z');
      
      if (values.length === 0) {
        throw new Error('No data found in mapping sheet');
      }

      const headers = values[0];
      const mappingDefinitions: MappingDefinition[] = [];

      // Process each row after the header
      for (let i = 1; i < values.length; i++) {
        const row = values[i];
        if (!row || row.length === 0) continue;

        const mapping: MappingDefinition = {
          column_name: row[headers.indexOf('column_name')] || '',
          display_name: row[headers.indexOf('display_name')] || '',
          data_type: row[headers.indexOf('data_type')] || 'text',
          required: row[headers.indexOf('required')]?.toLowerCase() === 'true' || false,
          description: row[headers.indexOf('description')] || ''
        };

        // Parse validation config if exists
        const validationWorksheet = row[headers.indexOf('validation_worksheet')];
        const validationColumn = row[headers.indexOf('validation_column')];
        
        if (validationWorksheet && validationColumn) {
          mapping.validation_config = {
            type: 'dropdown',
            worksheet_name: validationWorksheet,
            column_name: validationColumn
          };
        }

        mappingDefinitions.push(mapping);
      }

      // Cache the result
      this.mappingDefinitionsCache = mappingDefinitions;
      this.cacheTimestamp = Date.now();

      return mappingDefinitions;
    } catch (error) {
      console.error('Error reading mapping definitions:', error);
      throw error;
    }
  }

  /**
   * Get validation data from a specific worksheet and column
   */
  async getValidationData(worksheetName: string, columnName: string, useCache: boolean = true): Promise<string[]> {
    const cacheKey = `${worksheetName}:${columnName}`;
    
    if (useCache && this.validationCache.has(cacheKey)) {
      return this.validationCache.get(cacheKey)!;
    }

    try {
      const values = await this.readSheetData(this.mappingSheetId, `${worksheetName}!${columnName}:${columnName}`);
      
      // Extract unique non-empty values
      const validationData = values
        .flat()
        .filter(value => value && value.toString().trim() !== '')
        .map(value => value.toString().trim())
        .filter((value, index, array) => array.indexOf(value) === index);

      // Cache the result
      this.validationCache.set(cacheKey, validationData);

      return validationData;
    } catch (error) {
      console.error(`Error getting validation data for ${worksheetName}:${columnName}:`, error);
      throw error;
    }
  }

  /**
   * Get worksheet data as an array of objects
   */
  async getWorksheetData(worksheetName: string, useCache: boolean = true): Promise<Record<string, any>[]> {
    const cacheKey = `worksheet:${worksheetName}`;
    
    if (useCache && this.worksheetCache.has(cacheKey)) {
      return this.worksheetCache.get(cacheKey);
    }

    try {
      const values = await this.readSheetData(this.mappingSheetId, `${worksheetName}!A:Z`);
      
      if (values.length === 0) {
        return [];
      }

      const headers = values[0];
      const data: Record<string, any>[] = [];

      // Process each row after the header
      for (let i = 1; i < values.length; i++) {
        const row = values[i];
        if (!row || row.length === 0) continue;

        const rowData: Record<string, any> = {};
        headers.forEach((header, index) => {
          rowData[header] = row[index] || '';
        });

        data.push(rowData);
      }

      // Cache the result
      this.worksheetCache.set(cacheKey, data);

      return data;
    } catch (error) {
      console.error(`Error getting worksheet data for ${worksheetName}:`, error);
      throw error;
    }
  }

  /**
   * Get Akeneo column configurations
   */
  async getAkeneoColumnConfigurations(useCache: boolean = true): Promise<MappingDefinition[]> {
    try {
      const worksheetData = await this.getWorksheetData('Akeneo_Columns', useCache);
      
      return worksheetData.map(row => ({
        column_name: row.column_name || '',
        display_name: row.display_name || '',
        data_type: row.data_type || 'text',
        required: row.required?.toLowerCase() === 'true' || false,
        description: row.description || ''
      }));
    } catch (error) {
      console.error('Error getting Akeneo column configurations:', error);
      throw error;
    }
  }

  /**
   * Refresh all cached data
   */
  async refreshCache(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // Clear all caches
      this.clearCache();

      // Pre-load mapping definitions
      const mappingDefinitions = await this.readMappingDefinitions(false);
      
      // Pre-load validation data for all mappings with validation configs
      const validationResults: Record<string, any> = {};
      
      for (const mapping of mappingDefinitions) {
        if (mapping.validation_config?.worksheet_name && mapping.validation_config?.column_name) {
          try {
            const validationData = await this.getValidationData(
              mapping.validation_config.worksheet_name,
              mapping.validation_config.column_name,
              false
            );
            validationResults[`${mapping.validation_config.worksheet_name}:${mapping.validation_config.column_name}`] = validationData;
          } catch (error) {
            console.warn(`Failed to load validation data for ${mapping.column_name}:`, error);
          }
        }
      }

      return {
        success: true,
        message: 'Cache refreshed successfully',
        details: {
          mapping_definitions_count: mappingDefinitions.length,
          validation_sets_loaded: Object.keys(validationResults).length,
          validation_results: validationResults
        }
      };
    } catch (error) {
      console.error('Error refreshing cache:', error);
      return {
        success: false,
        message: `Failed to refresh cache: ${error}`
      };
    }
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this.worksheetCache.clear();
    this.validationCache.clear();
    this.mappingDefinitionsCache = null;
    this.cacheTimestamp = null;
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(): boolean {
    if (!this.cacheTimestamp) return false;
    return (Date.now() - this.cacheTimestamp) < this.CACHE_DURATION;
  }

  /**
   * Logout - remove stored credentials
   */
  async logout(): Promise<void> {
    try {
      await this.redisClient.del(this.tokenKey);
      this.clearCache();
    } catch (error) {
      // Key might not exist, which is fine
      console.warn('Could not remove token from Redis:', error);
    }
  }
}

// Configuration helper function
function getGoogleSheetsConfig(): GoogleSheetsConfig {
  // Try to get client secret from GSHEET_SECRET environment variable first (Flask compatibility)
  const gsheetSecret = process.env.GSHEET_SECRET;
  
  if (gsheetSecret) {
    try {
      const clientConfig = JSON.parse(gsheetSecret);
      
      // Extract configuration from nested structure (supports both 'web' and 'installed' client types)
      const clientType = clientConfig.web ? 'web' : (clientConfig.installed ? 'installed' : null);
      
      if (!clientType) {
        throw new Error('Invalid Google OAuth client configuration - missing client type');
      }
      
      const client = clientConfig[clientType];
      
      // Build redirect URI - always use our Next.js endpoint
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const redirectUri = process.env.GOOGLE_REDIRECT_URI || `${baseUrl}/api/google-sheets/auth/callback`;
      
      return {
        client_id: client.client_id,
        client_secret: client.client_secret,
        redirect_uri: redirectUri
      };
    } catch (error) {
      console.error('Error parsing GSHEET_SECRET:', error);
      throw new Error(`Invalid JSON in GSHEET_SECRET environment variable: ${error}`);
    }
  }
  
  // Fallback to individual environment variables
  const clientId = process.env.GOOGLE_CLIENT_ID;
  const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
  const redirectUri = process.env.GOOGLE_REDIRECT_URI;
  
  if (!clientId || !clientSecret || !redirectUri) {
    throw new Error('Missing Google OAuth configuration. Please set either GSHEET_SECRET or GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, and GOOGLE_REDIRECT_URI');
  }
  
  return {
    client_id: clientId,
    client_secret: clientSecret,
    redirect_uri: redirectUri
  };
}

// Singleton instance
let googleSheetsService: GoogleSheetsService | null = null;

// Main export function
export function getGoogleSheetsService(): GoogleSheetsService {
  if (!googleSheetsService) {
    const config = getGoogleSheetsConfig();
    googleSheetsService = new GoogleSheetsService(config);
  }
  
  return googleSheetsService;
}

// Export the service class as default as well for better compatibility
export default GoogleSheetsService; 