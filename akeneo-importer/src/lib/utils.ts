import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Retry utility function for HTTP 500 errors with exponential backoff
export async function retryOnHttp500<T>(
  operation: () => Promise<T>,
  maxRetries: number = 2,
  initialDelay: number = 2000,
  operationName: string = 'operation'
): Promise<T> {
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Attempting ${operationName} (attempt ${attempt + 1}/${maxRetries + 1})`);
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Check if this is an HTTP 500 error
      const isHttp500 = lastError.message.includes('HTTP 500') || 
                        lastError.message.includes('Internal Server Error') ||
                        (error as any)?.status === 500;
      
      if (!isHttp500) {
        console.log(`❌ ${operationName} failed with non-500 error, not retrying:`, lastError.message);
        throw lastError;
      }
      
      // If this is the last attempt, throw the error
      if (attempt === maxRetries) {
        console.log(`❌ ${operationName} failed after ${maxRetries + 1} attempts, giving up:`, lastError.message);
        throw lastError;
      }
      
      // Calculate delay with exponential backoff
      const delay = initialDelay * Math.pow(2, attempt);
      console.log(`⏳ ${operationName} failed with HTTP 500 (attempt ${attempt + 1}), retrying in ${delay}ms:`, lastError.message);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // This should never be reached, but just in case
  throw lastError || new Error(`${operationName} failed after ${maxRetries + 1} attempts`);
}
