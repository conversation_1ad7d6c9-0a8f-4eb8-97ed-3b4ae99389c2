export interface JobMetadata {
  id: string;
  name: string;
  status: 'draft' | 'processing' | 'completed' | 'error';
  original_filename: string;
  source_type: 'file_upload' | 'google_sheets';
  created_at: string;
  updated_at: string;
  last_accessed: string;
  row_count: number;
  column_count: number;
  processed_rows: number;
  progress: {
    total_cells: number;
    processed_cells: number;
    failed_cells: number;
    estimated_completion: string | null;
  };
}

export interface JobFilter {
  status?: string;
  search?: string;
  dateStart?: string;
  dateEnd?: string;
}

class JobService {
  private baseUrl = '/api/jobs';

  async getAllJobs(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    dateStart?: string;
    dateEnd?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${this.baseUrl}?${searchParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch jobs');
    }
    return response.json();
  }

  async listJobs(filter: JobFilter = {}, page: number = 1, limit: number = 20) {
    const params = {
      page,
      limit,
      ...filter
    };
    return this.getAllJobs(params);
  }

  async getJob(id: string): Promise<JobMetadata> {
    const response = await fetch(`${this.baseUrl}/${id}`);
    if (!response.ok) {
      throw new Error('Failed to fetch job');
    }
    return response.json();
  }

  async createJob(data: {
    name: string;
    source_type: 'file_upload' | 'google_sheets';
    original_filename?: string;
    google_sheet_url?: string;
  }): Promise<JobMetadata> {
    console.log('JobService: Creating job with data:', data);
    
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('JobService: Failed to create job:', response.status, errorText);
      throw new Error(`Failed to create job: ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('JobService: Job creation response:', result);
    
    // Handle both old and new response formats
    if (result.success && result.job) {
      return result.job;
    } else if (result.id) {
      // Handle old format
      return result;
    } else {
      console.error('JobService: Unexpected response format:', result);
      throw new Error('Unexpected response format from job creation');
    }
  }

  async renameJob(id: string, name: string): Promise<JobMetadata> {
    const response = await fetch(`${this.baseUrl}/${id}/rename`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name }),
    });

    if (!response.ok) {
      throw new Error('Failed to rename job');
    }
    return response.json();
  }

  async deleteJob(id: string): Promise<void> {
    console.log(`JobService: Attempting to delete job with ID: ${id}`);
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`JobService: Failed to delete job ${id}:`, response.status, errorText);
      throw new Error(`Failed to delete job: ${response.statusText} (${response.status})`);
    }
    
    console.log(`JobService: Successfully deleted job with ID: ${id}`);
  }

  async duplicateJob(id: string): Promise<JobMetadata> {
    const response = await fetch(`${this.baseUrl}/${id}/duplicate`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error('Failed to duplicate job');
    }
    return response.json();
  }

  async archiveJob(id: string): Promise<JobMetadata> {
    // For now, archiving just changes the status - in production this might move to different storage
    const job = await this.getJob(id);
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...job, status: 'archived' }),
    });

    if (!response.ok) {
      throw new Error('Failed to archive job');
    }
    return response.json();
  }

  async exportJobToExcel(id: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/${id}/export-excel`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error('Failed to export job to Excel');
    }
    return response.blob();
  }

  async exportJobToCSV(id: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/${id}/export-csv`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error('Failed to export job to CSV');  
    }
    return response.blob();
  }
}

export const jobService = new JobService(); 