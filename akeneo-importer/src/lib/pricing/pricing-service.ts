import pricingConfig from '../../../config/llm-pricing.json';

export interface ModelCapabilities {
  tool_use: boolean;
  parallel_tool_use: boolean;
  json_mode: boolean;
}

export interface ModelPricing {
  name: string;
  provider: string;
  speed_tokens_per_second: number;
  input_price_per_million: number;
  output_price_per_million: number;
  context_window: string;
  model_id?: string;
  capabilities?: ModelCapabilities;
}

export interface TTSModelPricing {
  name: string;
  provider: string;
  characters_per_second: number;
  price_per_million_characters: number;
}

export interface ASRModelPricing {
  name: string;
  provider: string;
  speed_factor: number;
  price_per_hour: number;
  minimum_seconds: number;
}

export interface PricingData {
  models: Record<string, ModelPricing>;
  tts_models: Record<string, TTSModelPricing>;
  asr_models: Record<string, ASRModelPricing>;
}

class PricingService {
  private pricingData: PricingData;
  private modelIdMappings: Record<string, string>;

  constructor() {
    this.pricingData = pricingConfig as PricingData;
    
    // Map Groq API model IDs to our pricing config keys
    this.modelIdMappings = {
      // Llama 4 models
      'meta-llama/llama-4-scout-17b-16e-instruct': 'llama-4-scout-17bx16e',
      'meta-llama/llama-4-maverick-17b-128e-instruct': 'llama-4-maverick-17bx128e',
      
      // Llama 3 models
      'llama3-8b-8192': 'llama-3-8b-8k',
      'llama3-70b-8192': 'llama-3-70b-8k',
      'llama-3.1-8b-instant': 'llama-3.1-8b-instant-128k',
      
      // Qwen models
      'qwen/qwen3-32b': 'qwen3-32b-131k',
      'qwen-qwq-32b': 'qwen-qwq-32b-preview-128k',
      
      // Gemma models  
      'gemma2-9b-it': 'gemma-2-9b-8k',
      
      // Other models already match
      'llama-3.3-70b-versatile': 'llama-3.3-70b-versatile-128k',
      'deepseek-r1-distill-llama-70b': 'deepseek-r1-distill-llama-70b',
      'mistral-saba-24b': 'mistral-saba-24b'
    };
  }

  /**
   * Get the pricing config key for a given model ID
   */
  private getPricingKey(modelId: string): string {
    return this.modelIdMappings[modelId] || modelId;
  }

  /**
   * Get pricing information for a specific model
   */
  getModelPricing(modelId: string): ModelPricing | null {
    const pricingKey = this.getPricingKey(modelId);
    return this.pricingData.models[pricingKey] || null;
  }

  /**
   * Get all model pricing information
   */
  getAllModelsPricing(): Record<string, ModelPricing> {
    return this.pricingData.models;
  }

  /**
   * Get TTS model pricing
   */
  getTTSModelPricing(modelId: string): TTSModelPricing | null {
    return this.pricingData.tts_models[modelId] || null;
  }

  /**
   * Get ASR model pricing
   */
  getASRModelPricing(modelId: string): ASRModelPricing | null {
    return this.pricingData.asr_models[modelId] || null;
  }

  /**
   * Calculate estimated cost for input/output tokens
   */
  calculateCost(modelId: string, inputTokens: number, outputTokens: number): number | null {
    const pricing = this.getModelPricing(modelId);
    if (!pricing) return null;

    const inputCost = (inputTokens / 1000000) * pricing.input_price_per_million;
    const outputCost = (outputTokens / 1000000) * pricing.output_price_per_million;
    
    return inputCost + outputCost;
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    if (price < 0.01) {
      return `$${price.toFixed(4)}`;
    } else if (price < 1) {
      return `$${price.toFixed(3)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  }

  /**
   * Get approximate tokens per dollar
   */
  getTokensPerDollar(price: number): string {
    if (price === 0) return 'Free';
    const tokensPerDollar = 1000000 / price;
    if (tokensPerDollar > 1000000) {
      return `${(tokensPerDollar / 1000000).toFixed(1)}M`;
    } else if (tokensPerDollar > 1000) {
      return `${(tokensPerDollar / 1000).toFixed(1)}K`;
    } else {
      return tokensPerDollar.toFixed(0);
    }
  }

  /**
   * Get capabilities for a specific model
   */
  getModelCapabilities(modelId: string): ModelCapabilities | null {
    const pricing = this.getModelPricing(modelId);
    return pricing?.capabilities || null;
  }

  /**
   * Check if a model supports a specific capability
   */
  hasCapability(modelId: string, capability: keyof ModelCapabilities): boolean {
    const capabilities = this.getModelCapabilities(modelId);
    return capabilities ? capabilities[capability] : false;
  }

  /**
   * Get models that support specific capabilities
   */
  getModelsByCapabilities(requiredCapabilities: Partial<ModelCapabilities>): ModelPricing[] {
    return Object.values(this.pricingData.models).filter(model => {
      if (!model.capabilities) return false;
      
      return Object.entries(requiredCapabilities).every(([capability, required]) => {
        if (required) {
          return model.capabilities![capability as keyof ModelCapabilities];
        }
        return true;
      });
    });
  }

  /**
   * Format capabilities for display
   */
  formatCapabilities(capabilities: ModelCapabilities): { [key: string]: string } {
    return {
      'Tool Use': capabilities.tool_use ? '✅' : '❌',
      'Parallel Tools': capabilities.parallel_tool_use ? '✅' : '❌',
      'JSON Mode': capabilities.json_mode ? '✅' : '❌'
    };
  }
}

export const pricingService = new PricingService();
export default pricingService; 