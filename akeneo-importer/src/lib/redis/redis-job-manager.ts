import { v4 as uuidv4 } from 'uuid';
import { getJobsClient } from './redis-manager';
import { JobMetadata, DataPage, RowData } from '@/types';

export class RedisJobManager {
  private redis = getJobsClient();

  async createJob(jobName: string, originalFilename?: string, sourceType: 'file_upload' | 'google_sheets' = 'file_upload'): Promise<string> {
    const jobId = uuidv4();
    const now = new Date().toISOString();
    
    const metadata: JobMetadata = {
      id: jobId,
      name: jobName,
      status: 'draft',
      original_filename: originalFilename,
      source_type: sourceType,
      created_at: now,
      updated_at: now,
      last_accessed: now,
      row_count: 0,
      column_count: 0,
      processed_rows: 0,
      progress: {
        total_cells: 0,
        processed_cells: 0,
        failed_cells: 0,
        estimated_completion: null
      }
    };

    await this.redis.setex(
      `job:${jobId}:meta`,
      3600, // 1 hour TTL
      JSON.stringify(metadata)
    );

    // Add to jobs list with score as timestamp for sorting
    await this.redis.zadd(
      'jobs:list',
      Date.now(),
      jobId
    );

    return jobId;
  }

  async getJobMetadata(jobId: string): Promise<JobMetadata | null> {
    try {
      const data = await this.redis.get(`job:${jobId}:meta`);
      
      if (!data || data === 'undefined' || data === 'null') {
        console.log(`No data found for job ${jobId}`);
        return null;
      }
      
      const parsed = JSON.parse(data);
      
      // Ensure all required fields exist with defaults
      const metadata: JobMetadata = {
        id: parsed.id || jobId,
        name: parsed.name || 'Untitled Job',
        status: parsed.status || 'draft',
        original_filename: parsed.original_filename,
        source_type: parsed.source_type || 'file_upload',
        created_at: parsed.created_at || new Date().toISOString(),
        updated_at: parsed.updated_at || new Date().toISOString(),
        last_accessed: parsed.last_accessed || new Date().toISOString(),
        row_count: parsed.row_count || 0,
        column_count: parsed.column_count || 0,
        processed_rows: parsed.processed_rows || 0,
        progress: parsed.progress || {
          total_cells: 0,
          processed_cells: 0,
          failed_cells: 0,
          estimated_completion: null
        },
        columns: parsed.columns,
        processing_options: parsed.processing_options
      };
      
      return metadata;
    } catch (error) {
      console.error('Error getting job metadata:', error);
      return null;
    }
  }

  async updateJobMetadata(jobId: string, updates: Partial<JobMetadata>): Promise<boolean> {
    try {
      const existing = await this.getJobMetadata(jobId);
      if (!existing) return false;

      const updated = {
        ...existing,
        ...updates,
        updated_at: new Date().toISOString(),
      };

      await this.redis.setex(
        `job:${jobId}:meta`,
        3600, // 1 hour TTL
        JSON.stringify(updated)
      );

      return true;
    } catch (error) {
      console.error('Error updating job metadata:', error);
      return false;
    }
  }

  async deleteJob(jobId: string): Promise<boolean> {
    try {
      // First, remove from jobs list to prevent it from appearing in listings
      await this.redis.zrem('jobs:list', jobId);
      
      // Then delete all related keys
      const keys = await this.redis.keys(`job:${jobId}:*`);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
      
      console.log(`Successfully deleted job ${jobId} and ${keys.length} related keys`);
      return true;
    } catch (error) {
      console.error('Error deleting job:', error);
      return false;
    }
  }

  async listJobs(limit: number = 100, offset: number = 0): Promise<JobMetadata[]> {
    try {
      // Get job IDs sorted by creation time (newest first)
      const jobIds = await this.redis.zrevrange(
        'jobs:list',
        offset,
        offset + limit - 1
      );

      if (jobIds.length === 0) return [];

      // Get metadata for all jobs in parallel
      const metadataPromises = jobIds.map(async (jobId) => {
        try {
          const metadata = await this.getJobMetadata(jobId);
          if (!metadata) {
            // If metadata is null, remove the job ID from the list to clean up
            console.log(`Cleaning up orphaned job ID ${jobId} from jobs list`);
            await this.redis.zrem('jobs:list', jobId);
            return null;
          }
          return metadata;
        } catch (error) {
          console.error(`Error getting metadata for job ${jobId}:`, error);
          // Clean up the orphaned job ID
          await this.redis.zrem('jobs:list', jobId);
          return null;
        }
      });
      
      const metadataList = await Promise.all(metadataPromises);

      // Filter out null results
      return metadataList.filter((metadata): metadata is JobMetadata => metadata !== null);
    } catch (error) {
      console.error('Error listing jobs:', error);
      return [];
    }
  }

  async renameJob(jobId: string, newName: string): Promise<boolean> {
    return this.updateJobMetadata(jobId, { name: newName });
  }

  async jobExists(jobId: string): Promise<boolean> {
    try {
      const exists = await this.redis.exists(`job:${jobId}:meta`);
      return exists === 1;
    } catch (error) {
      console.error('Error checking job existence:', error);
      return false;
    }
  }
}

export class RedisJobDataManager {
  private redis = getJobsClient();
  private jobId: string;

  constructor(jobId: string) {
    this.jobId = jobId;
  }

  async storeSourceData(data: RowData[], pageSize: number = 20): Promise<boolean> {
    try {
      if (!data || data.length === 0) return false;

      // Store data in pages
      const totalPages = Math.ceil(data.length / pageSize);
              const operations: Promise<unknown>[] = [];

      for (let page = 0; page < totalPages; page++) {
        const startIdx = page * pageSize;
        const endIdx = Math.min(startIdx + pageSize, data.length);
        const pageData = data.slice(startIdx, endIdx);

        const pageKey = `job:${this.jobId}:source:page:${page}`;
        operations.push(
          this.redis.setex(
            pageKey,
            1800, // 30 minutes TTL for data pages
            JSON.stringify(pageData)
          )
        );
      }

      // Store source columns
      if (data.length > 0) {
        const sourceColumns = Object.keys(data[0]);
        operations.push(
          this.redis.setex(
            `job:${this.jobId}:source_columns`,
            3600, // 1 hour TTL
            JSON.stringify(sourceColumns)
          )
        );
      }

      // Store pagination info
      const paginationInfo = {
        total_rows: data.length,
        page_size: pageSize,
        total_pages: totalPages,
        last_updated: new Date().toISOString()
      };

      operations.push(
        this.redis.setex(
          `job:${this.jobId}:pagination`,
          3600,
          JSON.stringify(paginationInfo)
        )
      );

      await Promise.all(operations);
      return true;
    } catch (error) {
      console.error('Error storing source data:', error);
      return false;
    }
  }

  async getDataPage(page: number): Promise<DataPage | null> {
    try {
      const [pageData, paginationData] = await Promise.all([
        this.redis.get(`job:${this.jobId}:source:page:${page}`),
        this.redis.get(`job:${this.jobId}:pagination`)
      ]);

      if (!pageData || !paginationData) return null;

      const rows: RowData[] = JSON.parse(pageData);
      const pagination = JSON.parse(paginationData);

      return {
        rows,
        total_rows: pagination.total_rows,
        page: page,
        page_size: pagination.page_size,
        total_pages: pagination.total_pages
      };
    } catch (error) {
      console.error('Error getting data page:', error);
      return null;
    }
  }

  async updateCell(rowIndex: number, columnName: string, value: unknown): Promise<boolean> {
    try {
      const pagination = await this.redis.get(`job:${this.jobId}:pagination`);
      if (!pagination) return false;

      const { page_size } = JSON.parse(pagination);
      const pageNumber = Math.floor(rowIndex / page_size);
      const rowInPage = rowIndex % page_size;

      const pageKey = `job:${this.jobId}:source:page:${pageNumber}`;
      const pageData = await this.redis.get(pageKey);
      
      if (!pageData) return false;

      const rows: RowData[] = JSON.parse(pageData);
      if (rowInPage >= rows.length) return false;

      rows[rowInPage][columnName] = value as string | number | boolean | null;

      await this.redis.setex(
        pageKey,
        1800, // 30 minutes TTL
        JSON.stringify(rows)
      );

      return true;
    } catch (error) {
      console.error('Error updating cell:', error);
      return false;
    }
  }

  async getSourceColumns(): Promise<string[]> {
    try {
      const data = await this.redis.get(`job:${this.jobId}:source_columns`);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error getting source columns:', error);
      return [];
    }
  }

  async getTotalRows(): Promise<number> {
    try {
      const paginationData = await this.redis.get(`job:${this.jobId}:pagination`);
      if (!paginationData) return 0;

      const pagination = JSON.parse(paginationData);
      return pagination.total_rows || 0;
    } catch (error) {
      console.error('Error getting total rows:', error);
      return 0;
    }
  }

  async getAllData(): Promise<RowData[]> {
    try {
      const paginationData = await this.redis.get(`job:${this.jobId}:pagination`);
      if (!paginationData) return [];

      const { total_pages } = JSON.parse(paginationData);
      const allData: RowData[] = [];

      for (let page = 0; page < total_pages; page++) {
        const pageData = await this.getDataPage(page);
        if (pageData) {
          allData.push(...pageData.rows);
        }
      }

      return allData;
    } catch (error) {
      console.error('Error getting all data:', error);
      return [];
    }
  }
} 