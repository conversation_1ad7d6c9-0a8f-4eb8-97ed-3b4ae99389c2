// @ts-nocheck
import { getTasksClient, RedisManager } from './redis-manager';
import { REDIS_DB, MemoryStats } from '@/types';
import type Redis from 'ioredis';

export class RedisPerformanceMonitor {
  private redis: Redis;
  
  constructor() {
    this.redis = getTasksClient();
  }

  async recordCacheHit(operation: string, responseTime: number): Promise<void> {
    try {
      const key = `perf:cache_hit:${operation}`;
      await this.redis.lpush(key, JSON.stringify({ 
        responseTime, 
        timestamp: Date.now() 
      }));
      await this.redis.ltrim(key, 0, 999); // Keep last 1000 entries
      await this.redis.expire(key, 24 * 60 * 60); // 24 hour TTL
    } catch (error) {
      console.error('Error recording cache hit:', error);
    }
  }

  async recordCacheMiss(operation: string, responseTime: number): Promise<void> {
    try {
      const key = `perf:cache_miss:${operation}`;
      await this.redis.lpush(key, JSON.stringify({ 
        responseTime, 
        timestamp: Date.now() 
      }));
      await this.redis.ltrim(key, 0, 999); // Keep last 1000 entries
      await this.redis.expire(key, 24 * 60 * 60); // 24 hour TTL
    } catch (error) {
      console.error('Error recording cache miss:', error);
    }
  }

  async recordOperation(operation: string, duration: number, success: boolean): Promise<void> {
    try {
      const key = `perf:operation:${operation}`;
      await this.redis.lpush(key, JSON.stringify({
        duration,
        success,
        timestamp: Date.now()
      }));
      await this.redis.ltrim(key, 0, 999);
      await this.redis.expire(key, 24 * 60 * 60);
    } catch (error) {
      console.error('Error recording operation:', error);
    }
  }

  async getPerformanceStats(): Promise<any> {
    try {
      const hitKeys = await this.redis.keys('perf:cache_hit:*');
      const missKeys = await this.redis.keys('perf:cache_miss:*');
      const operationKeys = await this.redis.keys('perf:operation:*');

      const stats = {
        cacheHitRate: 0,
        averageResponseTime: 0,
        totalOperations: 0,
        operationStats: {} as Record<string, any>,
        redisMemoryUsage: 0,
        connectionCount: 0,
        lastUpdated: new Date().toISOString()
      };

      // Calculate cache hit rates
      let totalHits = 0;
      let totalMisses = 0;
      let totalResponseTime = 0;
      let totalResponses = 0;

      // Process cache hits
      for (const key of hitKeys) {
        const entries = await this.redis.lrange(key, 0, -1);
        totalHits += entries.length;
        
        for (const entry of entries) {
          try {
            const data = JSON.parse(entry);
            totalResponseTime += data.responseTime;
            totalResponses++;
          } catch (error) {
            console.error('Error parsing cache hit entry:', error);
          }
        }
      }

      // Process cache misses
      for (const key of missKeys) {
        const entries = await this.redis.lrange(key, 0, -1);
        totalMisses += entries.length;
        
        for (const entry of entries) {
          try {
            const data = JSON.parse(entry);
            totalResponseTime += data.responseTime;
            totalResponses++;
          } catch (error) {
            console.error('Error parsing cache miss entry:', error);
          }
        }
      }

      // Calculate statistics
      stats.cacheHitRate = totalHits + totalMisses > 0 ? 
        (totalHits / (totalHits + totalMisses)) * 100 : 0;
      stats.averageResponseTime = totalResponses > 0 ? 
        totalResponseTime / totalResponses : 0;
      stats.totalOperations = totalHits + totalMisses;

      // Process operation statistics
      for (const key of operationKeys) {
        const operation = key.replace('perf:operation:', '');
        const entries = await this.redis.lrange(key, 0, -1);
        
        let successCount = 0;
        let failureCount = 0;
        let totalDuration = 0;
        
        for (const entry of entries) {
          try {
            const data = JSON.parse(entry);
            if (data.success) {
              successCount++;
            } else {
              failureCount++;
            }
            totalDuration += data.duration;
          } catch (error) {
            console.error('Error parsing operation entry:', error);
          }
        }
        
        stats.operationStats[operation] = {
          successCount,
          failureCount,
          successRate: (successCount / (successCount + failureCount)) * 100,
          averageDuration: entries.length > 0 ? totalDuration / entries.length : 0,
          totalOperations: entries.length
        };
      }

      // Get Redis memory usage
      try {
        const info = await this.redis.info('memory');
        const memoryMatch = info.match(/used_memory:(\d+)/);
        if (memoryMatch) {
          stats.redisMemoryUsage = parseInt(memoryMatch[1]);
        }
      } catch (error) {
        console.error('Error getting Redis memory info:', error);
      }

      return stats;
    } catch (error) {
      console.error('Error getting performance stats:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        lastUpdated: new Date().toISOString()
      };
    }
  }

  async getMemoryStats(): Promise<MemoryStats> {
    try {
      const promises = Object.values(REDIS_DB).map(async (db) => {
        try {
          const stats = await RedisManager.getStats(db);
          return stats;
        } catch (error) {
          console.error(`Error getting stats for DB ${db}:`, error);
          return { db, connected: false, key_count: 0 };
        }
      });

      const dbStats = await Promise.all(promises);
      
      // Get overall memory info
      const memoryInfo = await this.redis.info('memory');
      const usedMemoryMatch = memoryInfo.match(/used_memory:(\d+)/);
      const maxMemoryMatch = memoryInfo.match(/maxmemory:(\d+)/);
      
      const usedMemory = usedMemoryMatch ? parseInt(usedMemoryMatch[1]) : 0;
      const maxMemory = maxMemoryMatch ? parseInt(maxMemoryMatch[1]) : 0;
      
      const totalKeys = dbStats.reduce((sum, stat) => {
        return sum + (typeof stat.key_count === 'number' ? stat.key_count : 0);
      }, 0);

      return {
        used: usedMemory,
        total: maxMemory || usedMemory * 2, // Fallback if max not set
        percentage: maxMemory > 0 ? (usedMemory / maxMemory) * 100 : 0,
        redis_memory: usedMemory,
        active_connections: totalKeys
      };
    } catch (error) {
      console.error('Error getting memory stats:', error);
      return {
        used: 0,
        total: 0,
        percentage: 0,
        redis_memory: 0,
        active_connections: 0
      };
    }
  }

  async clearPerformanceStats(): Promise<boolean> {
    try {
      const allPerfKeys = await this.redis.keys('perf:*');
      if (allPerfKeys.length > 0) {
        await this.redis.del(...allPerfKeys);
      }
      return true;
    } catch (error) {
      console.error('Error clearing performance stats:', error);
      return false;
    }
  }

  // Health check methods
  async checkRedisHealth(): Promise<{ healthy: boolean; databases: any[] }> {
    const databases = [];
    let allHealthy = true;

    for (const [name, db] of Object.entries(REDIS_DB)) {
      try {
        const isHealthy = await RedisManager.ping(db);
        const stats = await RedisManager.getStats(db);
        
        databases.push({
          name,
          db,
          healthy: isHealthy,
          stats
        });
        
        if (!isHealthy) {
          allHealthy = false;
        }
      } catch (error) {
        databases.push({
          name,
          db,
          healthy: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        allHealthy = false;
      }
    }

    return {
      healthy: allHealthy,
      databases
    };
  }
}

// Export singleton instance
export const performanceMonitor = new RedisPerformanceMonitor(); 