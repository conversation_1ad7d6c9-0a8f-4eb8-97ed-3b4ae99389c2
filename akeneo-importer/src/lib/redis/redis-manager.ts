import Redis from 'ioredis';
import { REDIS_DB } from '@/types';

class RedisManager {
  private static instance: RedisManager;
  private clients: Map<number, Redis> = new Map();
  private connectionConfig: any;

  private constructor() {
    // Skip Redis connections during build time
    if (process.env.NODE_ENV === 'production' && !process.env.REDIS_HOST) {
      console.log('Skipping Redis connection during build...');
      return;
    }

    this.connectionConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      retryDelayOnClusterDown: 300,
      connectTimeout: 10000,
      lazyConnectTimeout: 10000,
    };
  }

  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager();
    }
    return RedisManager.instance;
  }

  public getClient(database: number): Redis {
    // Return mock client during build time
    if (process.env.NODE_ENV === 'production' && !process.env.REDIS_HOST) {
      return {
        get: () => Promise.resolve(null),
        set: () => Promise.resolve('OK'),
        setex: () => Promise.resolve('OK'),
        del: () => Promise.resolve(0),
        ping: () => Promise.resolve('PONG'),
        info: () => Promise.resolve(''),
        on: () => {},
        disconnect: () => Promise.resolve(),
      } as any;
    }

    if (!this.clients.has(database)) {
      const client = new Redis({
        ...this.connectionConfig,
        db: database,
      });

      client.on('error', (error) => {
        console.error(`Redis DB ${database} error:`, error);
      });

      client.on('connect', () => {
        console.log(`Redis DB ${database} connected`);
      });

      this.clients.set(database, client);
    }

    const client = this.clients.get(database);
    if (!client) {
      throw new Error(`Failed to get Redis client for database ${database}`);
    }
    return client;
  }

  public async ping(database: number): Promise<boolean> {
    try {
      const client = this.getClient(database);
      const result = await client.ping();
      return result === 'PONG';
    } catch (error) {
      console.error(`Redis ping failed for DB ${database}:`, error);
      return false;
    }
  }

  public async getStats(database: number): Promise<any> {
    try {
      const client = this.getClient(database);
      const info = await client.info('keyspace');
      const keyspaceMatch = info.match(new RegExp(`db${database}:keys=(\\d+)`));
      const keyCount = keyspaceMatch ? parseInt(keyspaceMatch[1]) : 0;

      return {
        db: database,
        connected: true,
        key_count: keyCount
      };
    } catch (error) {
      console.error(`Failed to get stats for DB ${database}:`, error);
      return {
        db: database,
        connected: false,
        key_count: 0
      };
    }
  }

  public async disconnect(): Promise<void> {
    const disconnectPromises = Array.from(this.clients.values()).map(client => 
      client.disconnect()
    );
    
    await Promise.all(disconnectPromises);
    this.clients.clear();
    console.log('All Redis connections closed');
  }

  public async flushDatabase(database: number): Promise<void> {
    const client = this.getClient(database);
    await client.flushdb();
  }

  public async getMemoryUsage(): Promise<{ [key: number]: number }> {
    const usage: { [key: number]: number } = {};
    
    for (const [db, client] of this.clients) {
      try {
        const info = await client.info('memory');
        // Parse memory info to get used memory
        const memoryMatch = info.match(/used_memory:(\d+)/);
        const memory = memoryMatch ? parseInt(memoryMatch[1]) : 0;
        usage[db] = memory;
      } catch (error) {
        console.error(`Failed to get memory usage for DB ${db}:`, error);
        usage[db] = 0;
      }
    }
    
    return usage;
  }

  // Static convenience methods
  public static getClient(database: number): Redis {
    return RedisManager.getInstance().getClient(database);
  }

  public static async ping(database: number): Promise<boolean> {
    return RedisManager.getInstance().ping(database);
  }

  public static async getStats(database: number): Promise<any> {
    return RedisManager.getInstance().getStats(database);
  }

  public static async disconnect(): Promise<void> {
    return RedisManager.getInstance().disconnect();
  }
}

// Cleanup on process exit
if (typeof process !== 'undefined') {
  const cleanup = async () => {
    await RedisManager.disconnect();
    process.exit(0);
  };

  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
  process.on('beforeExit', cleanup);
}

// Export individual clients for convenience
export const getJobsClient = () => RedisManager.getClient(REDIS_DB.JOBS);
export const getLLMCacheClient = () => RedisManager.getClient(REDIS_DB.LLM_CACHE);
export const getSessionsClient = () => RedisManager.getClient(REDIS_DB.SESSIONS);
export const getTasksClient = () => RedisManager.getClient(REDIS_DB.TASKS);
export const getGoogleSheetsClient = () => RedisManager.getClient(REDIS_DB.GOOGLE_SHEETS);

export { RedisManager }; 