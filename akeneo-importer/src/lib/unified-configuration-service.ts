/**
 * ===================================================================================
 * UNIFIED CONFIGURATION SERVICE
 * ===================================================================================
 * 
 * This service provides a unified interface to access configuration data from Google Sheets
 * with consistent type classification, caching, and error handling.
 * 
 * ARCHITECTURE & PURPOSE:
 * The system needs column configurations from Google Sheets while maintaining consistent 
 * data structure and ERP/PIM type classification.
 * 
 * ===================================================================================
 * TYPE CLASSIFICATION LOGIC (CRITICAL FOR SYSTEM OPERATION):
 * ===================================================================================
 * 
 * The service implements the core business logic for ERP/PIM classification:
 * 
 * FROM GOOGLE SHEETS CONFIGURATION:
 * - Type = "Navisionvorlage" → ERP (Enterprise Resource Planning columns)
 * - Type = "Akeneo" → PIM (Product Information Management columns)
 * 
 * This classification affects:
 * - Import wizard column filtering (shows 50 ERP vs 479 PIM columns)
 * - Data transformation rules and AI prompts
 * - Export processes and data routing
 * - UI display and column organization
 * 
 * ===================================================================================
 * DATA SOURCE & AUTHENTICATION:
 * ===================================================================================
 * 
 * GOOGLE SHEETS SOURCE:
 * - Real-time configuration from Google Sheets via OAuth2
 * - Authentication: Required (falls back to mock data if not authenticated)
 * - Collaborative editing and real-time updates
 * - Primary and only configuration source
 * 
 * ===================================================================================
 * CACHING STRATEGY:
 * ===================================================================================
 * 
 * - Redis-based caching with 1-hour expiry
 * - Cache key: unified:configuration:google-sheets
 * - Manual cache invalidation via refreshConfiguration()
 * - Graceful degradation if Redis unavailable
 * 
 * ===================================================================================
 * ERROR HANDLING & FALLBACKS:
 * ===================================================================================
 * 
 * - Google Sheets authentication failure → Falls back to mock data (5 columns)
 * - Redis unavailable → Continues without caching
 * - Network issues → Retry with exponential backoff
 */

import { MappingDefinition, REDIS_DB } from '@/types';
import Redis from 'ioredis';
import { RedisManager } from './redis/redis-manager';
import { getGoogleSheetsService } from './google-sheets/google-sheets-service';

export interface UnifiedConfiguration {
  source: 'google-sheets';
  columns: Array<{
    column_name: string;
    type: string;
    prompt: string | null;
    description?: string | null;
    required: boolean;
    default_mapping?: string | null;
    default_mapping_content?: string | null;
    output_validation_column?: string | null;
    validation_data?: string[] | null;
  }>;
  worksheets: Record<string, any[]>;
  timestamp: string;
  hash: string;
}

export class UnifiedConfigurationService {
  private static instance: UnifiedConfigurationService;
  private redis: Redis;
  private googleSheetsService: any;
  
  private readonly CACHE_KEY = 'unified:configuration:google-sheets';
  private readonly CACHE_EXPIRY = 3600; // 1 hour

  private constructor() {
    // Use RedisManager for consistent connection handling
    this.redis = RedisManager.getClient(REDIS_DB.JOBS);
    this.googleSheetsService = getGoogleSheetsService();
  }

  static getInstance(): UnifiedConfigurationService {
    if (!UnifiedConfigurationService.instance) {
      UnifiedConfigurationService.instance = new UnifiedConfigurationService();
    }
    return UnifiedConfigurationService.instance;
  }

  /**
   * Get cached unified configuration or load fresh
   */
  async getCachedUnifiedConfig(): Promise<UnifiedConfiguration | null> {
    try {
      const cached = await this.redis.get(this.CACHE_KEY);
      if (cached) {
        console.log('✅ Found cached unified configuration');
        return JSON.parse(cached);
      }
      return null;
    } catch (error) {
      console.error('❌ Error getting cached config:', error);
      return null;
    }
  }

  /**
   * Cache unified configuration
   */
  async cacheUnifiedConfig(config: UnifiedConfiguration): Promise<void> {
    try {
      await this.redis.setex(
        this.CACHE_KEY,
        this.CACHE_EXPIRY,
        JSON.stringify(config)
      );
      console.log('✅ Cached unified configuration');
    } catch (error) {
      console.error('❌ Error caching config:', error);
    }
  }

  /**
   * Clear the cache
   */
  async clearCache(): Promise<void> {
    try {
      await this.redis.del(this.CACHE_KEY);
      console.log('✅ Cleared unified configuration cache');
    } catch (error) {
      console.error('❌ Error clearing cache:', error);
    }
  }

  /**
   * Load unified configuration from Google Sheets
   */
  async loadUnifiedConfiguration(): Promise<UnifiedConfiguration> {
    console.log('🔄 Loading unified configuration from Google Sheets...');

    // Try cache first
    const cached = await this.getCachedUnifiedConfig();
    if (cached) {
      console.log('✅ Returning cached unified configuration');
      return cached;
    }

    console.log('💾 Cache miss, loading from Google Sheets...');
    
    // Load from Google Sheets
    const config = await this.loadFromGoogleSheets();

    // Cache the loaded configuration
    await this.cacheUnifiedConfig(config);
    
    return config;
  }

  /**
   * Load configuration from Google Sheets
   * 
   * AUTHENTICATION FLOW:
   * 1. Direct access to Google Sheets service
   * 2. Checks authentication internally
   * 3. If authenticated: loads real data from Google Sheets (529 columns)
   * 4. If not authenticated: returns mock data (5 columns)
   * 
   * WORKSHEET LOADING:
   * - Attempts to load all worksheets if authenticated
   * - Falls back gracefully if worksheet loading fails
   * - Provides main configuration data regardless of worksheet access
   * 
   * TYPE MAPPING APPLICATION:
   * - Maps raw Google Sheets columns to unified format
   * - Applies Type → Source classification (Navisionvorlage → ERP, Akeneo → PIM)
   * - Preserves all configuration fields (prompts, mappings, validation)
   */
  async loadFromGoogleSheets(): Promise<UnifiedConfiguration> {
    console.log('📋 Loading configuration from Google Sheets...');
    
    try {
      // STEP 1: Load main configuration data directly from Google Sheets service
      // Instead of calling API endpoint (which creates circular dependency), use service directly
      let mainSheetData: any[] = [];
      let isAuthenticated = false;
      
      try {
        isAuthenticated = await this.googleSheetsService.isAuthenticated();
        console.log('🔑 Google Sheets authentication status:', isAuthenticated);
        
        if (isAuthenticated && process.env.GOOGLE_MAPPING_SHEET_ID) {
          // Load main configuration from the first worksheet
          const worksheetNames = await this.googleSheetsService.getSheetNames(process.env.GOOGLE_MAPPING_SHEET_ID);
          if (worksheetNames.length > 0) {
            console.log(`📋 Loading main configuration from worksheet: ${worksheetNames[0]}`);
            mainSheetData = await this.googleSheetsService.getWorksheetData(worksheetNames[0]);
            console.log(`✅ Loaded ${mainSheetData.length} rows from main configuration`);
          }
        }
      } catch (authError) {
        console.warn('⚠️ Google Sheets authentication failed:', authError);
        isAuthenticated = false;
      }
      
      // STEP 2: If not authenticated or no data, use mock data
      if (!isAuthenticated || mainSheetData.length === 0) {
        console.log('📝 Using mock configuration data');
        mainSheetData = [
          {
            Column_name: 'Product_Name',
            Type: 'Akeneo',
            Prompt: 'Extract and clean the product name',
            Description: 'Product name field',
            Required: true,
            Default_Mapping: 'name',
            Custom_Mapping_Prompt_Template: null,
            Output_Validation_Column: null
          },
          {
            Column_name: 'Product_SKU',
            Type: 'Akeneo',
            Prompt: 'Extract the SKU/product code',
            Description: 'Product SKU field',
            Required: true,
            Default_Mapping: 'sku',
            Custom_Mapping_Prompt_Template: null,
            Output_Validation_Column: null
          },
          {
            Column_name: 'Price',
            Type: 'Navisionvorlage',
            Prompt: 'Extract and format the price as a number',
            Description: 'Product price field',
            Required: false,
            Default_Mapping: 'price',
            Custom_Mapping_Prompt_Template: null,
            Output_Validation_Column: null
          },
          {
            Column_name: 'Category',
            Type: 'Akeneo',
            Prompt: 'Extract the product category',
            Description: 'Product category field',
            Required: false,
            Default_Mapping: 'category',
            Custom_Mapping_Prompt_Template: null,
            Output_Validation_Column: null
          },
          {
            Column_name: 'Stock_Level',
            Type: 'Navisionvorlage',
            Prompt: 'Extract stock quantity as a number',
            Description: 'Stock level field',
            Required: false,
            Default_Mapping: 'stock',
            Custom_Mapping_Prompt_Template: null,
            Output_Validation_Column: null
          }
        ];
      }
      
      // STEP 3: Get worksheets data (if authenticated)
      const worksheets: Record<string, any[]> = {};
      
      if (isAuthenticated && process.env.GOOGLE_MAPPING_SHEET_ID) {
        try {
          console.log('🔑 Google Sheets authenticated - loading all worksheets...');
          const sheetNames = await this.googleSheetsService.getSheetNames(process.env.GOOGLE_MAPPING_SHEET_ID);
          
          for (const sheetName of sheetNames) {
            try {
              const sheetData = await this.googleSheetsService.getWorksheetData(sheetName);
              worksheets[sheetName] = sheetData;
              console.log(`📋 Loaded worksheet ${sheetName}: ${sheetData.length} rows`);
            } catch (error) {
              console.warn(`⚠️ Could not load worksheet ${sheetName}:`, error);
              worksheets[sheetName] = [];
            }
          }
          console.log(`✅ Loaded ${Object.keys(worksheets).length} worksheets`);
        } catch (worksheetError) {
          console.warn('⚠️ Could not load worksheets:', worksheetError);
          // Still provide the main data even if we can't load worksheets
          worksheets['Main'] = mainSheetData;
        }
      } else {
        // Set main data in worksheets for mock/unauthenticated case
        worksheets['Main'] = mainSheetData;
      }

      // STEP 4: Map Google Sheets data to our column format
      const columns = mainSheetData
        .filter((row: any) => row.Column_name && row.Type)
        .map((row: any) => ({
          column_name: row.Column_name || '',
          type: row.Type || 'text',
          prompt: row.Prompt || null,
          description: row.Description || null,
          required: row.Required === true || row.Required === 'true',
          default_mapping: row.Default_Mapping || null,
          default_mapping_content: row.Custom_Mapping_Prompt_Template || row.Default_Mapping_Content || null,
          output_validation_column: row.Output_Validation_Column || null,
          validation_data: null // Will be populated if needed
        }));

      const config: UnifiedConfiguration = {
        source: 'google-sheets',
        columns,
        worksheets,
        timestamp: new Date().toISOString(),
        hash: this.generateConfigHash(columns)
      };

      console.log(`✅ Loaded Google Sheets configuration: ${columns.length} columns, ${Object.keys(worksheets).length} worksheets`);
      return config;
    } catch (error) {
      console.error('❌ Error loading Google Sheets configuration:', error);
      
      // Provide emergency fallback with mock data
      console.log('🚨 Using emergency fallback configuration');
      const fallbackColumns = [
        {
          column_name: 'Product_Name',
          type: 'Akeneo',
          prompt: 'Extract and clean the product name',
          description: 'Product name field',
          required: true,
          default_mapping: 'name',
          default_mapping_content: null,
          output_validation_column: null,
          validation_data: null
        }
      ];
      
      return {
        source: 'google-sheets',
        columns: fallbackColumns,
        worksheets: { 'Fallback': fallbackColumns },
        timestamp: new Date().toISOString(),
        hash: this.generateConfigHash(fallbackColumns)
      };
    }
  }

  /**
   * Get prompt for a specific column from cached configuration
   */
  async getColumnPrompt(columnName: string): Promise<string | null> {
    try {
      const config = await this.loadUnifiedConfiguration();
      const column = config.columns.find(col => col.column_name === columnName);
      return column?.prompt || null;
    } catch (error) {
      console.error(`❌ Error getting prompt for column ${columnName}:`, error);
      return null;
    }
  }

  /**
   * Get all columns with their prompts
   */
  async getAllColumnPrompts(): Promise<Array<{columnName: string, prompt: string | null, type: string, required: boolean}>> {
    try {
      const config = await this.loadUnifiedConfiguration();
      return config.columns.map(col => ({
        columnName: col.column_name,
        prompt: col.prompt,
        type: col.type,
        required: col.required
      }));
    } catch (error) {
      console.error('❌ Error getting all column prompts:', error);
      return [];
    }
  }

  /**
   * Refresh cached config - clear cache and reload
   */
  async refreshConfiguration(): Promise<UnifiedConfiguration> {
    console.log('🔄 Refreshing unified configuration...');
    await this.clearCache();
    return await this.loadUnifiedConfiguration();
  }

  /**
   * Generate hash for configuration data
   */
  private generateConfigHash(columns: any[]): string {
    const crypto = require('crypto');
    const configString = JSON.stringify(columns.map(col => ({
      name: col.column_name,
      type: col.type,
      required: col.required
    })));
    return crypto.createHash('md5').update(configString).digest('hex');
  }

  /**
   * Get validation data for a specific column
   */
  async getValidationData(columnName: string): Promise<string[] | null> {
    try {
      const config = await this.loadUnifiedConfiguration();
      const column = config.columns.find(col => col.column_name === columnName);
      
      if (!column?.output_validation_column) return null;
      
      // Look for validation data in worksheets
      const validationWorksheet = config.worksheets[column.output_validation_column];
      if (!validationWorksheet) return null;
      
      // Extract validation values (assuming first column contains validation data)
      return validationWorksheet.map(row => row[0]).filter(Boolean);
    } catch (error) {
      console.error(`❌ Error getting validation data for column ${columnName}:`, error);
      return null;
    }
  }

  /**
   * Get all worksheets data
   */
  async getWorksheets(): Promise<Record<string, any[]>> {
    try {
      const config = await this.loadUnifiedConfiguration();
      return config.worksheets;
    } catch (error) {
      console.error('❌ Error getting worksheets:', error);
      return {};
    }
  }
} 