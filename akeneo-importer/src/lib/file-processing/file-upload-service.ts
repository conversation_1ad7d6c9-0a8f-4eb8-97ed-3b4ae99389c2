import { RedisManager } from '../redis/redis-manager';
import { REDIS_DB } from '@/types';
import { FileProcessor, ProcessedFileData } from './index';
import { v4 as uuidv4 } from 'uuid';

export interface UploadProgress {
  jobId: string;
  status: 'uploading' | 'processing' | 'storing' | 'completed' | 'error';
  progress: number;
  totalBytes: number;
  processedBytes: number;
  totalRows?: number;
  processedRows?: number;
  currentOperation: string;
  error?: string;
  estimatedCompletion?: string;
}

export interface UploadOptions {
  jobId?: string;
  chunkSize?: number;
  autoTransform?: boolean;
  columnFilter?: string[];
  worksheetName?: string;
}

export class FileUploadService {
  private redis = RedisManager.getClient(REDIS_DB.JOBS);
  private progressRedis = RedisManager.getClient(REDIS_DB.TASKS);

  async uploadFile(
    file: File,
    options: UploadOptions = {}
  ): Promise<{ jobId: string; progress: UploadProgress }> {
    const jobId = options.jobId || uuidv4();
    const chunkSize = options.chunkSize || 1000;

    // Initialize progress tracking
    const initialProgress: UploadProgress = {
      jobId,
      status: 'uploading',
      progress: 0,
      totalBytes: file.size,
      processedBytes: 0,
      currentOperation: 'Starting upload...'
    };

    await this.updateProgress(jobId, initialProgress);

    try {
      // Step 1: Validate file
      FileProcessor.validateFileSize(file, 100); // 100MB limit
      FileProcessor.validateFileType(file);

      await this.updateProgress(jobId, {
        ...initialProgress,
        progress: 10,
        currentOperation: 'Validating file...'
      });

      // Step 2: Process file
      const processedData = await this.processFileWithProgress(file, jobId, options);

      await this.updateProgress(jobId, {
        ...initialProgress,
        status: 'storing',
        progress: 70,
        totalRows: processedData.rowCount,
        currentOperation: 'Storing data in Redis...'
      });

      // Step 3: Store in Redis with chunking
      await this.storeDataInRedis(jobId, processedData, chunkSize);

      // Step 4: Complete
      const finalProgress: UploadProgress = {
        ...initialProgress,
        status: 'completed',
        progress: 100,
        processedBytes: file.size,
        totalRows: processedData.rowCount,
        processedRows: processedData.rowCount,
        currentOperation: 'Upload completed'
      };

      await this.updateProgress(jobId, finalProgress);

      return { jobId, progress: finalProgress };

    } catch (error) {
      const errorProgress: UploadProgress = {
        ...initialProgress,
        status: 'error',
        currentOperation: 'Upload failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      await this.updateProgress(jobId, errorProgress);
      throw error;
    }
  }

  private async processFileWithProgress(
    file: File,
    jobId: string,
    options: UploadOptions
  ): Promise<ProcessedFileData> {
    await this.updateProgress(jobId, {
      jobId,
      status: 'processing',
      progress: 30,
      totalBytes: file.size,
      processedBytes: 0,
      currentOperation: 'Processing file...'
    });

    let processedData: ProcessedFileData;

    if (options.worksheetName && file.name.match(/\.(xlsx|xls)$/i)) {
      processedData = await FileProcessor.processExcelSheet(file, options.worksheetName);
    } else {
      processedData = await FileProcessor.processFile(file);
    }

    await this.updateProgress(jobId, {
      jobId,
      status: 'processing',
      progress: 60,
      totalBytes: file.size,
      processedBytes: file.size,
      totalRows: processedData.rowCount,
      currentOperation: 'File processing completed'
    });

    return processedData;
  }

  private async storeDataInRedis(
    jobId: string,
    data: ProcessedFileData,
    chunkSize: number
  ): Promise<void> {
    // Store metadata
    const metadata = {
      id: jobId,
      name: data.fileName || 'Uploaded File',
      status: 'data-uploaded',
      original_filename: data.fileName,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      row_count: data.rowCount,
      column_count: data.columns.length,
      columns: data.columns,
      processing_options: {
        chunk_size: chunkSize,
        total_chunks: Math.ceil(data.data.length / chunkSize)
      }
    };

    await this.redis.setex(`job:${jobId}:meta`, 3600, JSON.stringify(metadata));

    // Store columns
    await this.redis.setex(`job:${jobId}:columns`, 3600, JSON.stringify(data.columns));

    // Store data in chunks for better performance
    const chunks = [];
    for (let i = 0; i < data.data.length; i += chunkSize) {
      const chunk = data.data.slice(i, i + chunkSize);
      const chunkIndex = Math.floor(i / chunkSize);
      const chunkKey = `job:${jobId}:chunk:${chunkIndex}`;
      
      await this.redis.setex(chunkKey, 1800, JSON.stringify(chunk)); // 30 min TTL
      chunks.push(chunkKey);

      // Update progress
      const progress = 70 + (30 * (i + chunkSize)) / data.data.length;
      await this.updateProgress(jobId, {
        jobId,
        status: 'storing',
        progress: Math.min(progress, 95),
        totalBytes: 0,
        processedBytes: 0,
        totalRows: data.rowCount,
        processedRows: i + chunkSize,
        currentOperation: `Storing chunk ${chunkIndex + 1}/${chunks.length + 1}...`
      });
    }

    // Store chunk index
    await this.redis.setex(`job:${jobId}:chunks`, 1800, JSON.stringify(chunks));
  }

  async getUploadProgress(jobId: string): Promise<UploadProgress | null> {
    const progressData = await this.progressRedis.get(`progress:${jobId}`);
    return progressData ? JSON.parse(progressData) : null;
  }

  async getWorksheetNames(file: File): Promise<string[]> {
    try {
      return await FileProcessor.getWorksheetNames(file);
    } catch (error) {
      console.error('Error getting worksheet names:', error);
      return [];
    }
  }

  private async updateProgress(jobId: string, progress: UploadProgress): Promise<void> {
    await this.progressRedis.setex(`progress:${jobId}`, 1800, JSON.stringify(progress));
    
    // Broadcast progress update (for future WebSocket implementation)
    await this.progressRedis.publish(`progress:${jobId}:updates`, JSON.stringify(progress));
  }

  async cleanupJob(jobId: string): Promise<void> {
    const keys = await this.redis.keys(`job:${jobId}:*`);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
    
    await this.progressRedis.del(`progress:${jobId}`);
  }
} 