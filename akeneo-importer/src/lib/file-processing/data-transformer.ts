import { RowData, ValidationConfig } from '@/types';

export type TransformationMode = '1to1' | 'ai_transform' | 'string_overwrite' | 'deactivated';

export interface TransformationRule {
  mode: TransformationMode;
  sourceColumn?: string;        // For 1to1 mapping
  staticValue?: string;         // For string overwrite
  aiPrompt?: string;           // For AI transformation
  validationConfig?: ValidationConfig;
}

export interface TransformationResult {
  success: boolean;
  transformedValue: any;
  error?: string;
  fromCache?: boolean;
  processingTime?: number;
}

export interface BatchTransformationResult {
  success: boolean;
  results: TransformationResult[];
  totalProcessed: number;
  successCount: number;
  errorCount: number;
  processingTime: number;
}

export class DataTransformer {
  
  static transformCell(
    rowData: RowData,
    targetColumn: string,
    rule: TransformationRule,
    rowIndex?: number
  ): TransformationResult {
    const startTime = Date.now();
    
    try {
      let transformedValue: any;

      switch (rule.mode) {
        case '1to1':
          transformedValue = this.transform1to1(rowData, rule.sourceColumn);
          break;
          
        case 'string_overwrite':
          transformedValue = this.transformStringOverwrite(rule.staticValue);
          break;
          
        case 'ai_transform':
          // Note: AI transformation would need to be handled externally
          // This is just the placeholder logic
          transformedValue = this.prepareAITransformContext(rowData, targetColumn, rule.aiPrompt);
          break;
          
        case 'deactivated':
          transformedValue = null;
          break;
          
        default:
          throw new Error(`Unknown transformation mode: ${rule.mode}`);
      }

      // Apply validation if configured
      if (rule.validationConfig && transformedValue !== null) {
        const validationResult = this.validateValue(transformedValue, rule.validationConfig);
        if (!validationResult.isValid) {
          return {
            success: false,
            transformedValue: null,
            error: `Validation failed: ${validationResult.error}`,
            processingTime: Date.now() - startTime
          };
        }
      }

      return {
        success: true,
        transformedValue,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        success: false,
        transformedValue: null,
        error: error instanceof Error ? error.message : 'Unknown transformation error',
        processingTime: Date.now() - startTime
      };
    }
  }

  static batchTransformColumn(
    rows: RowData[],
    targetColumn: string,
    rule: TransformationRule
  ): BatchTransformationResult {
    const startTime = Date.now();
    const results: TransformationResult[] = [];
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < rows.length; i++) {
      const result = this.transformCell(rows[i], targetColumn, rule, i);
      results.push(result);
      
      if (result.success) {
        successCount++;
        // Apply the transformation to the row data
        rows[i][targetColumn] = result.transformedValue;
      } else {
        errorCount++;
      }
    }

    return {
      success: errorCount === 0,
      results,
      totalProcessed: rows.length,
      successCount,
      errorCount,
      processingTime: Date.now() - startTime
    };
  }

  private static transform1to1(rowData: RowData, sourceColumn?: string): any {
    if (!sourceColumn) {
      throw new Error('Source column is required for 1-to-1 mapping');
    }
    
    if (!(sourceColumn in rowData)) {
      throw new Error(`Source column '${sourceColumn}' not found in row data`);
    }
    
    return rowData[sourceColumn];
  }

  private static transformStringOverwrite(staticValue?: string): any {
    if (staticValue === undefined) {
      throw new Error('Static value is required for string overwrite');
    }
    
    return staticValue;
  }

  private static prepareAITransformContext(
    rowData: RowData,
    targetColumn: string,
    aiPrompt?: string
  ): any {
    // This returns the context that would be sent to the AI service
    // The actual AI transformation would be handled by a separate service
    return {
      _requiresAITransformation: true,
      _context: {
        rowData,
        targetColumn,
        prompt: aiPrompt,
        timestamp: new Date().toISOString()
      }
    };
  }

  private static validateValue(value: any, config: ValidationConfig): { isValid: boolean; error?: string } {
    try {
      switch (config.type) {
        case 'text':
          if (typeof value !== 'string') {
            return { isValid: false, error: 'Value must be a string' };
          }
          
          if (config.min_length && value.length < config.min_length) {
            return { isValid: false, error: `Value must be at least ${config.min_length} characters` };
          }
          
          if (config.max_length && value.length > config.max_length) {
            return { isValid: false, error: `Value must be at most ${config.max_length} characters` };
          }
          
          if (config.pattern) {
            const regex = new RegExp(config.pattern);
            if (!regex.test(value)) {
              return { isValid: false, error: 'Value does not match required pattern' };
            }
          }
          break;

        case 'number':
          const numValue = typeof value === 'string' ? parseFloat(value) : value;
          if (isNaN(numValue)) {
            return { isValid: false, error: 'Value must be a number' };
          }
          break;

        case 'boolean':
          if (typeof value !== 'boolean' && value !== 'true' && value !== 'false' && value !== 0 && value !== 1) {
            return { isValid: false, error: 'Value must be a boolean' };
          }
          break;

        case 'dropdown':
          if (config.options && !config.options.includes(String(value))) {
            return { isValid: false, error: `Value must be one of: ${config.options.join(', ')}` };
          }
          break;
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: 'Validation error' };
    }
  }

  // Utility methods for working with transformation rules
  static createTransformationRule(
    mode: TransformationMode,
    options: Partial<TransformationRule> = {}
  ): TransformationRule {
    return {
      mode,
      ...options
    };
  }

  static isValidTransformationRule(rule: TransformationRule): boolean {
    switch (rule.mode) {
      case '1to1':
        return !!rule.sourceColumn;
      case 'string_overwrite':
        return rule.staticValue !== undefined;
      case 'ai_transform':
        return true; // AI prompt is optional
      case 'deactivated':
        return true;
      default:
        return false;
    }
  }

  // Data type detection utilities
  static detectDataType(value: any): 'string' | 'number' | 'boolean' | 'date' | 'null' {
    if (value === null || value === undefined || value === '') {
      return 'null';
    }

    // Check for boolean
    if (typeof value === 'boolean' || value === 'true' || value === 'false') {
      return 'boolean';
    }

    // Check for number
    if (typeof value === 'number' || (!isNaN(Number(value)) && value !== '')) {
      return 'number';
    }

    // Check for date (basic ISO date pattern)
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
      return 'date';
    }

    return 'string';
  }

  static analyzeColumnTypes(rows: RowData[], columnName: string): {
    dataType: string;
    confidence: number;
    uniqueValues: number;
    nullCount: number;
    sampleValues: any[];
  } {
    const values = rows.map(row => row[columnName]).filter(v => v !== null && v !== undefined && v !== '');
    const types: { [key: string]: number } = {};
    const uniqueValues = new Set(values);

    // Count types
    values.forEach(value => {
      const type = this.detectDataType(value);
      types[type] = (types[type] || 0) + 1;
    });

    // Find most common type
    const mostCommonType = Object.keys(types).reduce((a, b) => types[a] > types[b] ? a : b, 'string');
    const confidence = values.length > 0 ? (types[mostCommonType] || 0) / values.length : 0;

    return {
      dataType: mostCommonType,
      confidence,
      uniqueValues: uniqueValues.size,
      nullCount: rows.length - values.length,
      sampleValues: Array.from(uniqueValues).slice(0, 10) // First 10 unique values
    };
  }
} 