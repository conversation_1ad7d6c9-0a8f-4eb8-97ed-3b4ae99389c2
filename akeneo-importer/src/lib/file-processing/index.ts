import <PERSON> from 'papa<PERSON><PERSON>';
import * as XLSX from 'xlsx';
import { RowData } from '@/types';

export interface ProcessedFileData {
  data: RowData[];
  columns: string[];
  rowCount: number;
  fileName?: string;
}

export class FileProcessor {
  static async processFile(file: File): Promise<ProcessedFileData> {
    const fileExtension = this.getFileExtension(file.name);
    
    switch (fileExtension) {
      case 'csv':
        return this.processCSV(file);
      case 'xlsx':
      case 'xls':
        return this.processExcel(file);
      default:
        throw new Error(`Unsupported file type: ${fileExtension}`);
    }
  }

  private static getFileExtension(fileName: string): string {
    return fileName.split('.').pop()?.toLowerCase() || '';
  }

  private static async processCSV(file: File): Promise<ProcessedFileData> {
    return new Promise((resolve, reject) => {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim(),
        transform: (value: string) => value.trim(),
        complete: (results) => {
          try {
            const data = this.cleanData(results.data as RowData[]);
            const columns = results.meta.fields || [];
            
            if (data.length === 0) {
              throw new Error('No data found in CSV file');
            }

            resolve({
              data,
              columns,
              rowCount: data.length,
              fileName: file.name
            });
          } catch (error) {
            reject(error);
          }
        },
        error: (error) => {
          reject(new Error(`CSV parsing error: ${error.message}`));
        }
      });
    });
  }

  private static async processExcel(file: File): Promise<ProcessedFileData> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      
      // Use the first worksheet
      const firstSheetName = workbook.SheetNames[0];
      if (!firstSheetName) {
        throw new Error('No worksheets found in Excel file');
      }

      const worksheet = workbook.Sheets[firstSheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1, // Get raw array format first
        defval: '', // Default value for empty cells
        blankrows: false
      }) as unknown[][];

      if (jsonData.length === 0) {
        throw new Error('No data found in Excel file');
      }

      // First row contains headers
      const headers = jsonData[0].map((header: unknown) => 
        String(header || '').trim() || `Column_${jsonData[0].indexOf(header)}`
      );

      // Convert remaining rows to objects
      const data: RowData[] = jsonData.slice(1).map(row => {
        const rowObj: RowData = {};
        headers.forEach((header, index) => {
          rowObj[header] = this.normalizeValue(row[index]) as string | number | boolean | null;
        });
        return rowObj;
      });

      const cleanedData = this.cleanData(data);

      if (cleanedData.length === 0) {
        throw new Error('No valid data rows found in Excel file');
      }

      return {
        data: cleanedData,
        columns: headers,
        rowCount: cleanedData.length,
        fileName: file.name
      };
    } catch (error) {
      throw new Error(`Excel processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private static cleanData(data: RowData[]): RowData[] {
    return data
      .map(row => {
        const cleanedRow: RowData = {};
        Object.keys(row).forEach(key => {
          cleanedRow[key] = this.normalizeValue(row[key]) as string | number | boolean | null;
        });
        return cleanedRow;
      })
      .filter(row => {
        // Filter out completely empty rows
        return Object.values(row).some(value => 
          value !== null && value !== undefined && value !== ''
        );
      });
  }

  private static normalizeValue(value: unknown): unknown {
    // Handle null, undefined, NaN
    if (value === null || value === undefined || Number.isNaN(value)) {
      return null;
    }

    // Handle empty strings
    if (typeof value === 'string' && value.trim() === '') {
      return null;
    }

    // Handle numbers that come as strings
    if (typeof value === 'string') {
      const trimmed = value.trim();
      
      // Check if it's a number
      if (!isNaN(Number(trimmed)) && trimmed !== '') {
        return Number(trimmed);
      }
      
      return trimmed;
    }

    // Handle dates from Excel (serial numbers)
    if (typeof value === 'number' && value > 25567 && value < 2958465) {
      // Potential Excel date serial number
      const date = XLSX.SSF.parse_date_code(value);
      if (date) {
        return new Date(date.y, date.m - 1, date.d).toISOString().split('T')[0];
      }
    }

    return value;
  }

  static validateFileSize(file: File, maxSizeMB: number = 50): void {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      throw new Error(`File size exceeds ${maxSizeMB}MB limit`);
    }
  }

  static validateFileType(file: File, allowedTypes: string[] = ['csv', 'xlsx', 'xls']): void {
    const fileExtension = this.getFileExtension(file.name);
    if (!allowedTypes.includes(fileExtension)) {
      throw new Error(`File type .${fileExtension} is not supported. Allowed types: ${allowedTypes.join(', ')}`);
    }
  }

  static getWorksheetNames(file: File): Promise<string[]> {
    return new Promise(async (resolve, reject) => {
      try {
        if (!file.name.match(/\.(xlsx|xls)$/i)) {
          resolve([]);
          return;
        }

        const arrayBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        resolve(workbook.SheetNames);
      } catch (error) {
        reject(error);
      }
    });
  }

  static async processExcelSheet(file: File, sheetName: string): Promise<ProcessedFileData> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      
      if (!workbook.SheetNames.includes(sheetName)) {
        throw new Error(`Worksheet '${sheetName}' not found`);
      }

      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
        blankrows: false
      }) as unknown[][];

      if (jsonData.length === 0) {
        throw new Error(`No data found in worksheet '${sheetName}'`);
      }

      const headers = jsonData[0].map((header: unknown) => 
        String(header || '').trim() || `Column_${jsonData[0].indexOf(header)}`
      );

      const data: RowData[] = jsonData.slice(1).map(row => {
        const rowObj: RowData = {};
        headers.forEach((header, index) => {
          rowObj[header] = this.normalizeValue(row[index]) as string | number | boolean | null;
        });
        return rowObj;
      });

      const cleanedData = this.cleanData(data);

      return {
        data: cleanedData,
        columns: headers,
        rowCount: cleanedData.length,
        fileName: file.name
      };
    } catch (error) {
      throw new Error(`Excel sheet processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
} 