import { RedisManager } from '../redis/redis-manager';
import { REDIS_DB, RowData, DataPage, JobMetadata } from '@/types';

export interface DataStorageOptions {
  ttl?: number; // Time to live in seconds
  enableCompression?: boolean;
}

export interface StorageStats {
  totalRows: number;
  totalChunks: number;
  chunkSize: number;
  memoryUsage: number;
  lastAccessed: string;
}

export class RedisDataStorage {
  private redis = RedisManager.getClient(REDIS_DB.JOBS);
  private defaultTTL = 1800; // 30 minutes

  async getDataPage(
    jobId: string,
    page: number,
    pageSize: number = 20,
    columns?: string[]
  ): Promise<DataPage | null> {
    try {
      const startTime = Date.now();
      
      // Get job metadata
      const metadata = await this.getJobMetadata(jobId);
      if (!metadata) {
        return null;
      }

      const totalRows = metadata.row_count;
      const totalPages = Math.ceil(totalRows / pageSize);
      
      if (page < 1 || page > totalPages) {
        throw new Error(`Page ${page} out of range. Total pages: ${totalPages}`);
      }

      // Calculate which chunk(s) we need
      const startRowIndex = (page - 1) * pageSize;
      const endRowIndex = Math.min(startRowIndex + pageSize, totalRows);
      
      const rows = await this.getRowRange(jobId, startRowIndex, endRowIndex, columns);

      // Update access timestamp
      await this.updateLastAccessed(jobId);

      const responseTime = Date.now() - startTime;
      console.log(`Data page loaded in ${responseTime}ms: Job ${jobId}, Page ${page}`);

      return {
        rows,
        total_rows: totalRows,
        page,
        page_size: pageSize,
        total_pages: totalPages
      };
    } catch (error) {
      console.error(`Error getting data page for job ${jobId}:`, error);
      return null;
    }
  }

  async getRowRange(
    jobId: string,
    startIndex: number,
    endIndex: number,
    columns?: string[]
  ): Promise<RowData[]> {
    const chunks = await this.getChunkKeys(jobId);
    if (!chunks.length) {
      return [];
    }

    const metadata = await this.getJobMetadata(jobId);
    if (!metadata?.processing_options?.chunk_size) {
      throw new Error('Missing chunk size information');
    }

    const chunkSize = metadata.processing_options.chunk_size;
    const startChunk = Math.floor(startIndex / chunkSize);
    const endChunk = Math.floor((endIndex - 1) / chunkSize);

    const rows: RowData[] = [];

    for (let chunkIndex = startChunk; chunkIndex <= endChunk; chunkIndex++) {
      if (chunkIndex >= chunks.length) break;

      const chunkKey = chunks[chunkIndex];
      const chunkData = await this.redis.get(chunkKey);
      
      if (!chunkData) {
        console.warn(`Chunk ${chunkIndex} not found for job ${jobId}`);
        continue;
      }

      const parsedChunk: RowData[] = JSON.parse(chunkData);
      
      // Calculate row range within this chunk
      const chunkStartIndex = chunkIndex * chunkSize;
      const rowStartInChunk = Math.max(0, startIndex - chunkStartIndex);
      const rowEndInChunk = Math.min(parsedChunk.length, endIndex - chunkStartIndex);

      const chunkRows = parsedChunk.slice(rowStartInChunk, rowEndInChunk);
      
      // Filter columns if specified
      if (columns && columns.length > 0) {
        const filteredRows = chunkRows.map(row => {
          const filteredRow: RowData = {};
          columns.forEach(col => {
            if (col in row) {
              filteredRow[col] = row[col];
            }
          });
          return filteredRow;
        });
        rows.push(...filteredRows);
      } else {
        rows.push(...chunkRows);
      }
    }

    return rows;
  }

  async getJobMetadata(jobId: string): Promise<JobMetadata | null> {
    const metaData = await this.redis.get(`job:${jobId}:meta`);
    return metaData ? JSON.parse(metaData) : null;
  }

  async getJobColumns(jobId: string): Promise<string[]> {
    const columnsData = await this.redis.get(`job:${jobId}:columns`);
    return columnsData ? JSON.parse(columnsData) : [];
  }

  async getStorageStats(jobId: string): Promise<StorageStats | null> {
    const metadata = await this.getJobMetadata(jobId);
    if (!metadata) return null;

    const chunks = await this.getChunkKeys(jobId);
    const chunkSize = metadata.processing_options?.chunk_size || 1000;

    // Estimate memory usage
    let memoryUsage = 0;
    for (const chunkKey of chunks.slice(0, 3)) { // Sample first 3 chunks
      const chunkData = await this.redis.get(chunkKey);
      if (chunkData) {
        memoryUsage += Buffer.byteLength(chunkData, 'utf8');
      }
    }
    
    // Extrapolate total memory usage
    const avgChunkSize = memoryUsage / Math.min(3, chunks.length);
    const totalMemoryUsage = avgChunkSize * chunks.length;

    return {
      totalRows: metadata.row_count,
      totalChunks: chunks.length,
      chunkSize,
      memoryUsage: Math.round(totalMemoryUsage / 1024), // KB
      lastAccessed: new Date().toISOString()
    };
  }

  async updateJobStatus(jobId: string, status: JobMetadata['status']): Promise<void> {
    const metadata = await this.getJobMetadata(jobId);
    if (!metadata) return;

    metadata.status = status;
    metadata.updated_at = new Date().toISOString();

    await this.redis.setex(`job:${jobId}:meta`, this.defaultTTL, JSON.stringify(metadata));
  }

  async extendTTL(jobId: string, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.defaultTTL;
    const keys = await this.redis.keys(`job:${jobId}:*`);
    
    const pipeline = this.redis.pipeline();
    keys.forEach(key => {
      pipeline.expire(key, ttl);
    });
    
    await pipeline.exec();
  }

  async deleteJob(jobId: string): Promise<void> {
    const keys = await this.redis.keys(`job:${jobId}:*`);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }

  async listJobs(): Promise<JobMetadata[]> {
    const metaKeys = await this.redis.keys('job:*:meta');
    const jobs: JobMetadata[] = [];

    for (const key of metaKeys) {
      const metaData = await this.redis.get(key);
      if (metaData) {
        try {
          jobs.push(JSON.parse(metaData));
        } catch (error) {
          console.error(`Error parsing job metadata for ${key}:`, error);
        }
      }
    }

    return jobs.sort((a, b) => 
      new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    );
  }

  private async getChunkKeys(jobId: string): Promise<string[]> {
    const chunksData = await this.redis.get(`job:${jobId}:chunks`);
    return chunksData ? JSON.parse(chunksData) : [];
  }

  private async updateLastAccessed(jobId: string): Promise<void> {
    const metadata = await this.getJobMetadata(jobId);
    if (metadata) {
      metadata.updated_at = new Date().toISOString();
      await this.redis.setex(`job:${jobId}:meta`, this.defaultTTL, JSON.stringify(metadata));
    }
  }

  async searchJobs(query: string): Promise<JobMetadata[]> {
    const allJobs = await this.listJobs();
    const lowerQuery = query.toLowerCase();

    return allJobs.filter(job => 
      job.name.toLowerCase().includes(lowerQuery) ||
      (job.original_filename && job.original_filename.toLowerCase().includes(lowerQuery))
    );
  }

  async getJobCount(): Promise<number> {
    const metaKeys = await this.redis.keys('job:*:meta');
    return metaKeys.length;
  }

  async storeDataInChunks(
    jobId: string, 
    data: RowData[], 
    columns: string[], 
    options: { chunk_size: number, ttl: number }
  ): Promise<boolean> {
    try {
      if (!data || data.length === 0) {
        console.log('No data to store');
        return false;
      }

      const { chunk_size, ttl } = options;
      const chunkKeys: string[] = [];
      
      // Store data in chunks
      for (let i = 0; i < data.length; i += chunk_size) {
        const chunk = data.slice(i, i + chunk_size);
        const chunkIndex = Math.floor(i / chunk_size);
        const chunkKey = `job:${jobId}:chunk:${chunkIndex}`;
        
        await this.redis.setex(chunkKey, ttl, JSON.stringify(chunk));
        chunkKeys.push(chunkKey);
      }

      // Store chunk index
      await this.redis.setex(`job:${jobId}:chunks`, ttl, JSON.stringify(chunkKeys));

      // Store columns
      await this.redis.setex(`job:${jobId}:columns`, ttl, JSON.stringify(columns));

      // Create/update job metadata
      const now = new Date().toISOString();
      const metadata: JobMetadata = {
        id: jobId,
        name: `Job ${jobId}`,
        status: 'data-uploaded',
        created_at: now,
        updated_at: now,
        last_accessed: now,
        row_count: data.length,
        column_count: columns.length,
        processed_rows: 0,
        processing_options: {
          chunk_size: chunk_size,
          total_chunks: chunkKeys.length
        },
        progress: {
          total_cells: 0,
          processed_cells: 0,
          failed_cells: 0,
          estimated_completion: null
        }
      };

      await this.redis.setex(`job:${jobId}:meta`, ttl, JSON.stringify(metadata));

      console.log(`Stored ${data.length} rows in ${chunkKeys.length} chunks for job ${jobId}`);
      return true;
    } catch (error) {
      console.error('Error storing data in chunks:', error);
      return false;
    }
  }

  // Custom data storage methods for job-specific data like AI transform states
  async setJobData(jobId: string, key: string, data: any, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.defaultTTL;
    const redisKey = `job:${jobId}:${key}`;
    await this.redis.setex(redisKey, ttl, JSON.stringify(data));
  }

  async getJobData<T = any>(jobId: string, key: string): Promise<T | null> {
    const redisKey = `job:${jobId}:${key}`;
    const data = await this.redis.get(redisKey);
    return data ? JSON.parse(data) : null;
  }

  async deleteJobData(jobId: string, key: string): Promise<void> {
    const redisKey = `job:${jobId}:${key}`;
    await this.redis.del(redisKey);
  }

  /**
   * Delete a specific row by index from the job data
   */
  async deleteRow(jobId: string, rowIndex: number): Promise<boolean> {
    try {
      const metadata = await this.getJobMetadata(jobId);
      if (!metadata?.processing_options?.chunk_size) {
        throw new Error('Missing chunk size information');
      }

      const chunkSize = metadata.processing_options.chunk_size;
      const chunkIndex = Math.floor(rowIndex / chunkSize);
      const rowInChunk = rowIndex % chunkSize;

      // Get the chunk containing the row
      const chunkKey = `job:${jobId}:chunk:${chunkIndex}`;
      const chunkData = await this.redis.get(chunkKey);
      
      if (!chunkData) {
        return false;
      }

      const parsedChunk: RowData[] = JSON.parse(chunkData);
      
      if (rowInChunk >= parsedChunk.length) {
        return false;
      }

      // Remove the row from the chunk
      parsedChunk.splice(rowInChunk, 1);

      // Update the chunk
      await this.redis.setex(chunkKey, this.defaultTTL, JSON.stringify(parsedChunk));

      // Update metadata
      const newRowCount = metadata.row_count - 1;
      const updatedMetadata = {
        ...metadata,
        row_count: newRowCount,
        updated_at: new Date().toISOString()
      };

      await this.redis.setex(`job:${jobId}:meta`, this.defaultTTL, JSON.stringify(updatedMetadata));

      console.log(`Deleted row ${rowIndex} from job ${jobId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting row ${rowIndex} from job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * Delete all rows from the job data (clear cache)
   */
  async deleteAllRows(jobId: string): Promise<boolean> {
    try {
      // Get all chunk keys
      const chunkKeys = await this.getChunkKeys(jobId);
      
      if (chunkKeys.length === 0) {
        return false;
      }

      // Delete all chunks
      await this.redis.del(...chunkKeys);

      // Delete chunk index
      await this.redis.del(`job:${jobId}:chunks`);

      // Delete columns
      await this.redis.del(`job:${jobId}:columns`);

      // Update metadata to reflect no data
      const metadata = await this.getJobMetadata(jobId);
      if (metadata) {
        const updatedMetadata = {
          ...metadata,
          row_count: 0,
          column_count: 0,
          status: 'cleared',
          updated_at: new Date().toISOString(),
          processing_options: {
            ...metadata.processing_options,
            total_chunks: 0
          }
        };

        await this.redis.setex(`job:${jobId}:meta`, this.defaultTTL, JSON.stringify(updatedMetadata));
      }

      console.log(`Deleted all rows from job ${jobId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting all rows from job ${jobId}:`, error);
      return false;
    }
  }
} 