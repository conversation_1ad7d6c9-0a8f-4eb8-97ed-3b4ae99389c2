import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { UnifiedColumn, UnifiedColumnStatistics } from '@/types';

interface ConnectionStatus {
  connected: boolean;
  configured: boolean;
  attributes_count?: number;
  families_count?: number;
  categories_count?: number;
  last_sync?: string;
  last_test?: string;
}

interface ConfigurationState {
  // Configuration data
  columns: UnifiedColumn[];
  statistics: UnifiedColumnStatistics;
  lastRefresh: string | null;
  
  // Connection Status
  connectionStatus: ConnectionStatus;
  
  // Loading state
  isLoading: boolean;
  isTestingConnection: boolean;
  error: string | null;
  
  // Actions
  setColumns: (columns: UnifiedColumn[]) => void;
  setStatistics: (statistics: UnifiedColumnStatistics) => void;
  setLastRefresh: (timestamp: string) => void;
  setConnectionStatus: (status: ConnectionStatus) => void;
  setLoading: (loading: boolean) => void;
  setTestingConnection: (testing: boolean) => void;
  setError: (error: string | null) => void;
  clearCache: () => void;
  
  // Connection actions
  testConnection: () => Promise<boolean>;
  refreshConnection: () => Promise<boolean>;
  
  // Reset
  reset: () => void;
}

const initialState = {
  columns: [],
  statistics: {
    totalERPColumns: 0,
    totalPIMApiColumns: 0,
    totalPIMConfigured: 0,
    totalPIMUnconfigured: 0,
    configurationCoverage: 0
  },
  lastRefresh: null,
  connectionStatus: {
    connected: false,
    configured: false
  },
  isLoading: false,
  isTestingConnection: false,
  error: null,
};

export const useConfigurationStore = create<ConfigurationState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        setColumns: (columns) => set({ columns }),
        
        setStatistics: (statistics) => set({ statistics }),
        
        setLastRefresh: (lastRefresh) => set({ lastRefresh }),
        
        setConnectionStatus: (status) => set({ 
          connectionStatus: { 
            ...status, 
            last_test: new Date().toISOString() 
          } 
        }),
        
        setLoading: (loading) => set({ isLoading: loading }),
        
        setTestingConnection: (testing) => set({ isTestingConnection: testing }),
        
        setError: (error) => set({ error }),
        
        clearCache: () => set(initialState),
        
        testConnection: async () => {
          const { setTestingConnection, setConnectionStatus, setError } = get();
          
          setTestingConnection(true);
          setError(null);
          
          try {
            const response = await fetch('/api/akeneo/test-connection', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            const result = await response.json();
            
            const connectionStatus: ConnectionStatus = {
              connected: result.connected || false,
              configured: result.configured || false,
              attributes_count: result.sample_data?.attributes_available ? 1 : 0,
              last_sync: result.timestamp,
              last_test: new Date().toISOString()
            };
            
            setConnectionStatus(connectionStatus);
            
            if (!result.success) {
              setError(result.message || 'Connection test failed');
            }
            
            return result.success || false;
          } catch (error) {
            console.error('Connection test error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Connection test failed';
            
            setConnectionStatus({
              connected: false,
              configured: false,
              last_test: new Date().toISOString()
            });
            setError(errorMessage);
            
            return false;
          } finally {
            setTestingConnection(false);
          }
        },
        
        refreshConnection: async () => {
          const { setTestingConnection, setConnectionStatus, setError } = get();
          
          setTestingConnection(true);
          setError(null);
          
          try {
            const response = await fetch('/api/akeneo/test-connection', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            const result = await response.json();
            
            const connectionStatus: ConnectionStatus = {
              connected: result.connected || false,
              configured: result.configured !== false,
              attributes_count: result.status?.attributes_count,
              families_count: result.status?.families_count,
              categories_count: result.status?.categories_count,
              last_sync: result.timestamp,
              last_test: new Date().toISOString()
            };
            
            setConnectionStatus(connectionStatus);
            
            if (!result.success) {
              setError(result.message || 'Connection refresh failed');
            }
            
            return result.success || false;
          } catch (error) {
            console.error('Connection refresh error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Connection refresh failed';
            
            setConnectionStatus({
              connected: false,
              configured: false,
              last_test: new Date().toISOString()
            });
            setError(errorMessage);
            
            return false;
          } finally {
            setTestingConnection(false);
          }
        },
        
        reset: () => set(initialState)
      }),
      {
        name: 'configuration-storage',
        // Only persist the configuration data, not loading states
        partialize: (state) => ({
          columns: state.columns,
          statistics: state.statistics,
          lastRefresh: state.lastRefresh,
          connectionStatus: state.connectionStatus
        })
      }
    ),
    {
      name: 'configuration-store'
    }
  )
); 