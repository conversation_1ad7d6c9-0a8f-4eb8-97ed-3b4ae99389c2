import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { ImportWizardState, MappingDefinition, ValidationConfig } from '@/types';

// Add mapping type enum
export type MappingType = 'default' | 'one-to-one' | 'ai-transform';

// Update OneToOneMapping interface to include mapping type
export interface OneToOneMappingWithType {
  targetColumn: string;
  sourceColumn: string | null;
  mappingType: MappingType;
  manuallySelected: boolean;
  aiSuggested: boolean;
  confidence?: number;
}

// Add step completion tracking
export interface StepCompletionState {
  step1_fileUpload: boolean;
  step2_targetColumns: boolean;
  step3_aiTransform: boolean;
  step4_dataPreview: boolean;
}

// Add step state snapshots for conflict detection
export interface StepStateSnapshot {
  step: number;
  timestamp: string;
  data: Record<string, any>;
}

// Add AI transform specific state
export interface AiTransformColumnState {
  columnName: string;
  promptTemplate: string;
  isConfigured: boolean;
  isSaved: boolean;
}

// Add target system type
export type TargetSystemType = "navision" | "akeneo" | "ebay" | "amazon";

interface ImportWizardStore extends ImportWizardState {
  // Additional store-specific properties
  selectedTargetColumns: string[];
  selectedTargetSystemType: TargetSystemType | null;
  oneToOneMappings: Record<string, string>;
  // Add mapping types tracking
  columnMappingTypes: Record<string, MappingType>;
  aiTransformMappings: Record<string, string>;
  sourceData: any[];
  
  // Excel sheet selection and preview
  availableSheets: string[];
  selectedSheet: string | null;
  previewData: any[];
  
  // AI Transform step specific state
  aiTransformColumns: Record<string, AiTransformColumnState>;
  currentAiTransformColumnIndex: number;
  selectedLlmModel: string | null;
  
  // New state management properties
  stepCompletionState: StepCompletionState;
  stepStateSnapshots: StepStateSnapshot[];
  urlSyncEnabled: boolean;
  
  // Actions
  setCurrentStep: (step: number, updateUrl?: boolean) => void;
  setJobId: (jobId: string) => void;
  setJobName: (jobName: string | null) => void;
  setSourceColumns: (columns: string[]) => void;
  setSourceData: (data: any[]) => void;
  setMappingDefinitions: (definitions: MappingDefinition[]) => void;
  updateColumnMappings: (mappings: Record<string, string>) => void;
  updateValidationConfigs: (configs: ValidationConfig[]) => void;
  setSelectedTargetColumns: (columns: string[]) => void;
  setSelectedTargetSystemType: (systemType: TargetSystemType | null) => void;
  setOneToOneMappings: (mappings: Record<string, string>) => void;
  // Add action for mapping types
  setColumnMappingTypes: (mappingTypes: Record<string, MappingType>) => void;
  setAiTransformMappings: (mappings: Record<string, string>) => void;
  setIsProcessing: (isProcessing: boolean) => void;
  setError: (error: string | null) => void;
  setAvailableSheets: (sheets: string[]) => void;
  setSelectedSheet: (sheet: string | null) => void;
  setPreviewData: (data: any[]) => void;
  
  // AI Transform actions
  setAiTransformColumns: (columns: Record<string, AiTransformColumnState>) => void;
  updateAiTransformColumn: (columnName: string, state: Partial<AiTransformColumnState>) => void;
  setCurrentAiTransformColumnIndex: (index: number) => void;
  setSelectedLlmModel: (model: string | null) => void;
  loadAiTransformState: (jobId: string) => Promise<void>;
  
  // New actions for state management
  markStepCompleted: (step: number) => void;
  isStepCompleted: (step: number) => boolean;
  saveStepSnapshot: (step: number, data: Record<string, any>) => void;
  getStepSnapshot: (step: number) => StepStateSnapshot | null;
  getConflictingSteps: (targetStep: number) => number[];
  setUrlSyncEnabled: (enabled: boolean) => void;
  syncStepWithUrl: (step: number) => void;
  initializeFromUrl: (jobId: string | null, step: number | null) => void;
  
  // Utility methods
  shouldSkipOneToOneMapping: () => boolean;
  getNextStepAfterTargetSelection: () => number;
  
  reset: () => void;
}

const initialState: ImportWizardState = {
  currentStep: 1,
  jobId: null,
  jobName: null,
  sourceColumns: [],
  mappingDefinitions: [],
  columnMappings: {},
  validationConfigs: {},
  selectedTargetColumns: [],
  selectedTargetSystemType: null,
  oneToOneMappings: {},
  aiTransformMappings: {},
  isProcessing: false,
  error: null,
};

const initialStepCompletionState: StepCompletionState = {
  step1_fileUpload: false,
  step2_targetColumns: false,
  step3_aiTransform: false,
  step4_dataPreview: false,
};

export const useImportWizardStore = create<ImportWizardStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        sourceData: [],
        columnMappingTypes: {},
        availableSheets: [],
        selectedSheet: null,
        previewData: [],
        aiTransformColumns: {},
        currentAiTransformColumnIndex: 0,
        selectedLlmModel: null,
        stepCompletionState: initialStepCompletionState,
        stepStateSnapshots: [],
        urlSyncEnabled: true,

        // Actions
        setCurrentStep: (step: number, updateUrl: boolean = true) => {
          set({ currentStep: step });
          if (updateUrl && get().urlSyncEnabled) {
            get().syncStepWithUrl(step);
          }
        },
        
        setJobId: (jobId: string) => set({ jobId }),
        setJobName: (jobName: string | null) => set({ jobName }),
        setSourceColumns: (columns: string[]) => {
          // Add some sample columns if none are provided for testing
          const finalColumns = columns.length > 0 ? columns : [
            'Product Name', 'SKU', 'Description', 'Price', 'Category', 
            'Brand', 'Weight', 'Color', 'Size', 'Stock Quantity'
          ];
          set({ sourceColumns: finalColumns });
        },
        setSourceData: (data: any[]) => set({ sourceData: data }),
        setMappingDefinitions: (definitions: MappingDefinition[]) => set({ mappingDefinitions: definitions }),
        updateColumnMappings: (mappings: Record<string, string>) => 
          set((state) => ({ columnMappings: { ...state.columnMappings, ...mappings } })),
        updateValidationConfigs: (configs: ValidationConfig[]) => {
          const validationConfigs: Record<string, ValidationConfig> = {};
          configs.forEach(config => {
            if (config.column_name) {
              validationConfigs[config.column_name] = config;
            }
          });
          set({ validationConfigs });
        },
        setSelectedTargetColumns: (columns: string[]) => set({ selectedTargetColumns: columns }),
        setSelectedTargetSystemType: (systemType: TargetSystemType | null) => set({ selectedTargetSystemType: systemType }),
        setOneToOneMappings: (mappings: Record<string, string>) => set({ oneToOneMappings: mappings }),
        setColumnMappingTypes: (mappingTypes: Record<string, MappingType>) => set({ columnMappingTypes: mappingTypes }),
        setAiTransformMappings: (mappings: Record<string, string>) => set({ aiTransformMappings: mappings }),
        setIsProcessing: (isProcessing: boolean) => set({ isProcessing }),
        setError: (error: string | null) => set({ error }),
        setAvailableSheets: (sheets: string[]) => set({ availableSheets: sheets }),
        setSelectedSheet: (sheet: string | null) => set({ selectedSheet: sheet }),
        setPreviewData: (data: any[]) => set({ previewData: data }),
        
        // AI Transform actions
        setAiTransformColumns: (columns: Record<string, AiTransformColumnState>) => set({ aiTransformColumns: columns }),
        updateAiTransformColumn: (columnName: string, state: Partial<AiTransformColumnState>) => {
          set((currentState) => ({
            aiTransformColumns: {
              ...currentState.aiTransformColumns,
              [columnName]: {
                ...currentState.aiTransformColumns[columnName],
                ...state
              }
            }
          }));
        },
        setCurrentAiTransformColumnIndex: (index: number) => set({ currentAiTransformColumnIndex: index }),
        setSelectedLlmModel: (model: string | null) => set({ selectedLlmModel: model }),
        loadAiTransformState: async (jobId: string) => {
          try {
            // Load existing prompt templates from the database
            const response = await fetch(`/api/unified-config/prompts?job_id=${jobId}`);
            if (response.ok) {
              const result = await response.json();
              if (result.success && result.data) {
                const columns: Record<string, AiTransformColumnState> = {};
                
                // Initialize all AI transform columns
                const state = get();
                const aiTransformColumnNames = state.selectedTargetColumns.filter(col => 
                  state.columnMappingTypes[col] === 'ai-transform'
                );
                
                aiTransformColumnNames.forEach(columnName => {
                  const savedTemplate = result.data.find((item: any) => item.column_name === columnName);
                  columns[columnName] = {
                    columnName,
                    promptTemplate: savedTemplate?.prompt_template || '',
                    isConfigured: !!savedTemplate?.prompt_template,
                    isSaved: !!savedTemplate?.prompt_template
                  };
                });
                
                set({ aiTransformColumns: columns });
              }
            }
          } catch (error) {
            console.error('Failed to load AI transform state:', error);
          }
        },
        
        // New state management actions
        markStepCompleted: (step: number) => {
          const stepKey = `step${step}_${getStepKey(step)}` as keyof StepCompletionState;
          set((state) => ({
            stepCompletionState: {
              ...state.stepCompletionState,
              [stepKey]: true
            }
          }));
        },
        
        isStepCompleted: (step: number) => {
          const stepKey = `step${step}_${getStepKey(step)}` as keyof StepCompletionState;
          return get().stepCompletionState[stepKey] || false;
        },
        
        saveStepSnapshot: (step: number, data: Record<string, any>) => {
          const snapshot: StepStateSnapshot = {
            step,
            timestamp: new Date().toISOString(),
            data: { ...data }
          };
          
          set((state) => ({
            stepStateSnapshots: [
              ...state.stepStateSnapshots.filter(s => s.step !== step),
              snapshot
            ]
          }));
        },
        
        getStepSnapshot: (step: number) => {
          return get().stepStateSnapshots.find(s => s.step === step) || null;
        },
        
        getConflictingSteps: (targetStep: number) => {
          const currentStep = get().currentStep;
          const conflictingSteps: number[] = [];
          
          // Define step dependencies - which steps invalidate which subsequent steps
          const stepDependencies: Record<number, number[]> = {
            1: [2, 3, 4], // File upload invalidates all subsequent steps (now 4 steps total)
            2: [3, 4],    // Target column selection invalidates AI transform and data preview
            3: [4],       // AI transform affects data preview
            4: []         // Data preview doesn't affect other steps
          };
          
          // If going back to an earlier step, check which completed steps would be affected
          if (targetStep < currentStep) {
            for (let step = targetStep + 1; step <= currentStep; step++) {
              if (get().isStepCompleted(step)) {
                conflictingSteps.push(step);
              }
            }
          }
          
          return conflictingSteps;
        },
        
        setUrlSyncEnabled: (enabled: boolean) => set({ urlSyncEnabled: enabled }),
        
        syncStepWithUrl: (step: number) => {
          if (typeof window !== 'undefined') {
            const url = new URL(window.location.href);
            const jobId = get().jobId;
            
            if (jobId) {
              url.searchParams.set('job_id', jobId);
            }
            url.searchParams.set('step', step.toString());
            
            window.history.replaceState({}, '', url.toString());
          }
        },
        
        initializeFromUrl: (jobId: string | null, step: number | null) => {
          set({ urlSyncEnabled: false }); // Temporarily disable URL sync during initialization
          
          if (jobId) {
            set({ jobId });
          }
          
          if (step && step >= 1 && step <= 4) {
            set({ currentStep: step });
          }
          
          // Re-enable URL sync after initialization
          setTimeout(() => set({ urlSyncEnabled: true }), 100);
        },
        
        // Utility methods
        shouldSkipOneToOneMapping: () => {
          const { selectedTargetSystemType } = get();
          // Skip one-to-one mapping for Navision and Akeneo
          return selectedTargetSystemType === 'navision' || selectedTargetSystemType === 'akeneo';
        },
        
        getNextStepAfterTargetSelection: () => {
          const shouldSkip = get().shouldSkipOneToOneMapping();
          // If we should skip one-to-one mapping, go directly to AI transform (step 4)
          // Otherwise go to one-to-one mapping (step 3)
          return shouldSkip ? 4 : 3;
        },
        
        reset: () => set({ 
          ...initialState, 
          sourceData: [], 
          columnMappingTypes: {},
          availableSheets: [], 
          selectedSheet: null, 
          previewData: [],
          aiTransformColumns: {},
          currentAiTransformColumnIndex: 0,
          stepCompletionState: initialStepCompletionState,
          stepStateSnapshots: [],
          urlSyncEnabled: true
        }),
      }),
      {
        name: 'import-wizard-storage',
      }
    )
  )
);

// Helper function to get step key names
function getStepKey(step: number): string {
  const stepKeys: Record<number, string> = {
    1: 'fileUpload',
    2: 'targetColumns', 
    3: 'aiTransform',
    4: 'dataPreview'
  };
  return stepKeys[step] || 'unknown';
} 