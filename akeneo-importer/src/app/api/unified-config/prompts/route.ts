import { NextRequest, NextResponse } from 'next/server';

// In-memory storage for development (replace with actual database)
const promptTemplates: Record<string, { 
  column_name: string; 
  prompt_template: string; 
  selected_model: string | null;
  job_id: string; 
  saved_at: string 
}> = {};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { column_name, prompt_template, selected_model, job_id } = body;

    if (!column_name || !prompt_template) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: column_name and prompt_template'
      }, { status: 400 });
    }

    console.log('Saving prompt template:', {
      column_name,
      prompt_template: prompt_template.substring(0, 100) + '...',
      selected_model,
      job_id
    });

    // Store in memory (replace with actual database save)
    const key = `${job_id}-${column_name}`;
    promptTemplates[key] = {
      column_name,
      prompt_template,
      selected_model: selected_model || null,
      job_id,
      saved_at: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      message: `Prompt template saved for column: ${column_name}`,
      data: promptTemplates[key]
    });

  } catch (error) {
    console.error('Error saving prompt template:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to save prompt template',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const column_name = searchParams.get('column_name');
    const job_id = searchParams.get('job_id');

    if (!column_name || !job_id) {
      return NextResponse.json({
        success: false,
        error: 'Missing required parameters: column_name and job_id'
      }, { status: 400 });
    }

    console.log('Fetching prompt template for:', { column_name, job_id });

    // Retrieve from memory (replace with actual database fetch)
    const key = `${job_id}-${column_name}`;
    const template = promptTemplates[key];

    if (template) {
      return NextResponse.json({
        success: true,
        message: 'Prompt template retrieved',
        data: template
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Template not found',
        data: null
      }, { status: 404 });
    }

  } catch (error) {
    console.error('Error fetching prompt template:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch prompt template',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 