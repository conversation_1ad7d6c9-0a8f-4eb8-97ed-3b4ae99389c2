import { NextResponse } from 'next/server';
import { UnifiedConfigurationService } from '@/lib/unified-configuration-service';

/**
 * GET /api/unified-config
 * Get unified configuration combining ERP and PIM columns
 */
export async function GET(request: Request) {
  try {
    console.log('📋 GET /api/unified-config - Loading unified configuration');
    
    const { searchParams } = new URL(request.url);
    const showERP = searchParams.get('showERP') === 'true';
    const showPIM = searchParams.get('showPIM') === 'true';
    const showBoth = searchParams.get('showBoth') === 'true';
    
    const service = UnifiedConfigurationService.getInstance();
    const config = await service.loadUnifiedConfiguration();
    
    // Filter columns based on query parameters
    let filteredColumns = config.columns;
    
    if (!showBoth) {
      if (showERP && !showPIM) {
        // Show only ERP columns (Type = "Navisionvorlage")
        filteredColumns = config.columns.filter(col => 
          col.type === 'Navisionvorlage' || col.type === 'navisionvorlage' || col.type?.toLowerCase() === 'navisionvorlage'
        );
      } else if (showPIM && !showERP) {
        // Show only PIM columns (Type = "Akeneo")
        filteredColumns = config.columns.filter(col => 
          col.type === 'Akeneo' || col.type === 'akeneo' || col.type?.toLowerCase() === 'akeneo'
        );
      }
    }
    
    console.log(`✅ Successfully loaded unified configuration - ${filteredColumns.length}/${config.columns.length} columns (source: ${config.source})`);
    
    return NextResponse.json({
      success: true,
      data: {
        ...config,
        columns: filteredColumns
      }
    });
  } catch (error) {
    console.error('❌ Error loading unified configuration:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to load unified configuration',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST /api/unified-config/refresh
 * Refresh unified configuration data
 */
export async function POST() {
  try {
    console.log('🔄 POST /api/unified-config - Force refreshing configuration');
    
    const service = UnifiedConfigurationService.getInstance();
    const config = await service.refreshConfiguration();
    
    console.log('✅ Successfully refreshed unified configuration');
    
    return NextResponse.json({
      success: true,
      message: 'Configuration refreshed successfully',
      data: config
    });
  } catch (error) {
    console.error('❌ Error refreshing unified configuration:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to refresh unified configuration',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 