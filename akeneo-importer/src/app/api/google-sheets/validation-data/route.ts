import { NextRequest, NextResponse } from 'next/server';
import { getGoogleSheetsService } from '@/lib/google-sheets/google-sheets-service';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const worksheetName = searchParams.get('worksheet');
    const columnName = searchParams.get('column');
    const useCache = searchParams.get('use_cache') !== 'false';

    if (!worksheetName || !columnName) {
      return NextResponse.json({
        success: false,
        message: 'Missing required parameters: worksheet and column'
      }, { status: 400 });
    }

    const googleSheetsService = getGoogleSheetsService();
    
    if (!await googleSheetsService.isAuthenticated()) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated with Google Sheets'
      }, { status: 401 });
    }

    const validationData = await googleSheetsService.getValidationData(worksheetName, columnName, useCache);

    return NextResponse.json({
      success: true,
      data: validationData,
      count: validationData.length,
      worksheet: worksheetName,
      column: columnName
    });
  } catch (error) {
    console.error('Error fetching validation data:', error);
    return NextResponse.json({
      success: false,
      message: `Error fetching validation data: ${error}`
    }, { status: 500 });
  }
}