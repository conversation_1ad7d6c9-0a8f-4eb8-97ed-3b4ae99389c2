import { NextResponse } from 'next/server';
import { getGoogleSheetsService } from '@/lib/google-sheets/google-sheets-service';

export async function GET() {
  try {
    const googleSheetsService = getGoogleSheetsService();
    
    // Check if authenticated
    const isAuthenticated = await googleSheetsService.isAuthenticated();
    if (!isAuthenticated) {
      console.log('⚠️ Google Sheets not authenticated');
      return NextResponse.json({ error: 'Google Sheets authentication required' }, { status: 401 });
    }

    console.log('🔑 Google Sheets authenticated - loading real configuration...');
    
    // Load the main configuration sheet
    const spreadsheetId = process.env.GOOGLE_MAPPING_SHEET_ID;
    if (!spreadsheetId) {
      throw new Error('GOOGLE_MAPPING_SHEET_ID environment variable not set');
    }
    
    // Get the "Main" worksheet data
    const mainSheetData = await googleSheetsService.getWorksheetData('Main');
    
    // Transform to expected format
    const configuration = mainSheetData
      .filter(row => row.Column_name && row.Type) // Only include rows with column name and type
      .map(row => ({
        Column_name: row.Column_name || '',
        Type: row.Type || 'text',
        Required: row.Required === true || row.Required === 'true' || row.Required === 'TRUE',
        Default_Mapping: row.Default_Mapping || null,
        Default_Mapping_Content: row.Custom_Mapping_Prompt_Template || row.Default_Mapping_Content || null,
        Prompt: row.Prompt || null,
        Description: row.Description || null,
        Output_Validation_Column: row.Output_Validation_Column || null
      }));

    console.log(`✅ Loaded ${configuration.length} columns from Google Sheets Main worksheet`);
    
    // For now, use empty lookup tables (could be enhanced to load from other sheets)
    const lookupTables = {};

    return NextResponse.json({
      configuration,
      lookupTables
    });
  } catch (error) {
    console.error('Failed to load configuration:', error);
    return NextResponse.json({ error: 'Failed to load configuration' }, { status: 500 });
  }
} 