import { NextRequest, NextResponse } from 'next/server';
import { getGoogleSheetsRedisService } from '@/lib/google-sheets/google-sheets-redis-service';

/**
 * Redis-backed Configuration Management API
 * Handles server-side configuration storage, validation, and change detection
 */

// POST - Store/Refresh Configuration in Redis
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const force = body.force || false;

    const redisService = getGoogleSheetsRedisService();
    const metadata = await redisService.storeConfigurationInRedis(force);

    return NextResponse.json({
      success: true,
      message: 'Configuration stored in Redis successfully',
      metadata,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Failed to store configuration in Redis:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET - Load Configuration from Redis
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeValidationData = searchParams.get('include_validation') === 'true';
    const statsOnly = searchParams.get('stats_only') === 'true';

    const redisService = getGoogleSheetsRedisService();

    if (statsOnly) {
      // Return only performance statistics
      const stats = await redisService.getConfigurationStats();
      return NextResponse.json({
        success: true,
        data: { stats },
        timestamp: new Date().toISOString()
      });
    }

    // Load full configuration from Redis
    const config = await redisService.loadConfigurationFromRedis();
    
    if (!config) {
      return NextResponse.json({
        success: false,
        error: 'No configuration found in Redis cache',
        message: 'Please refresh configuration to load from Google Sheets',
        cacheStatus: 'miss',
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    // Prepare response data
    const responseData: any = {
      mappingDefinitions: config.mappingDefinitions,
      worksheetNames: config.worksheetNames,
      metadata: config.metadata,
      cacheStatus: 'hit'
    };

    // Include validation data if requested
    if (includeValidationData) {
      responseData.validationData = config.validationData;
    }

    // Get performance stats
    const stats = await redisService.getConfigurationStats();
    responseData.stats = stats;

    return NextResponse.json({
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Failed to load configuration from Redis:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// PUT - Validate Configuration
export async function PUT(request: NextRequest) {
  try {
    const redisService = getGoogleSheetsRedisService();
    const validation = await redisService.validateConfiguration();

    return NextResponse.json({
      success: true,
      data: validation,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Configuration validation failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Validation failed',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// DELETE - Invalidate Configuration Cache
export async function DELETE(request: NextRequest) {
  try {
    const redisService = getGoogleSheetsRedisService();
    await redisService.invalidateConfigurationCache();

    return NextResponse.json({
      success: true,
      message: 'Configuration cache invalidated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Failed to invalidate configuration cache:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Cache invalidation failed',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
} 