import { NextRequest, NextResponse } from 'next/server';
import { getGoogleSheetsService } from '@/lib/google-sheets/google-sheets-service';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const useCache = searchParams.get('use_cache') !== 'false';

    const googleSheetsService = getGoogleSheetsService();
    
    if (!await googleSheetsService.isAuthenticated()) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated with Google Sheets'
      }, { status: 401 });
    }

    const mappingDefinitions = await googleSheetsService.readMappingDefinitions(useCache);

    return NextResponse.json({
      success: true,
      data: mappingDefinitions,
      count: mappingDefinitions.length
    });
  } catch (error) {
    console.error('Error fetching mapping definitions:', error);
    return NextResponse.json({
      success: false,
      message: `Error fetching mapping definitions: ${error}`
    }, { status: 500 });
  }
}