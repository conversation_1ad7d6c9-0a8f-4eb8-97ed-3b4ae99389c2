import { NextResponse } from 'next/server';
import { getGoogleSheetsService } from '@/lib/google-sheets/google-sheets-service';

export async function GET() {
  try {
    const googleSheetsService = getGoogleSheetsService();
    
    if (!await googleSheetsService.isAuthenticated()) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated with Google Sheets'
      }, { status: 401 });
    }

    // Try to get sheet names from the mapping spreadsheet to test the connection
    const mappingSheetId = process.env.GOOGLE_MAPPING_SHEET_ID;
    
    if (!mappingSheetId) {
      return NextResponse.json({
        success: false,
        message: 'GOOGLE_MAPPING_SHEET_ID environment variable is not set'
      }, { status: 500 });
    }

    const sheetNames = await googleSheetsService.getSheetNames(mappingSheetId);
    
    // Try to load mapping definitions
    const mappingDefinitions = await googleSheetsService.readMappingDefinitions(false);

    return NextResponse.json({
      success: true,
      message: 'Successfully connected to Google Sheets',
      data: {
        sheet_names: sheetNames,
        mapping_sheet_id: mappingSheetId,
        mapping_definitions_count: mappingDefinitions.length,
        mapping_definitions: mappingDefinitions.slice(0, 5) // Return first 5 as sample
      }
    });
  } catch (error) {
    console.error('Error testing Google Sheets connection:', error);
    return NextResponse.json({
      success: false,
      message: `Error testing connection: ${error}`
    }, { status: 500 });
  }
} 