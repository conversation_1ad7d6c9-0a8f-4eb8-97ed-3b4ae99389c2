"use client";

import { Suspense } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { ImportWizardContent } from "@/components/import-wizard/import-wizard-content";

function ImportWizardContentWrapper() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ImportWizardContent />
    </Suspense>
  );
}

export default function ImportWizardPage() {
  return (
    <DashboardLayout>
      <ImportWizardContentWrapper />
    </DashboardLayout>
  );
} 