'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { ConfigurationSourceManager } from '@/components/unified-config/configuration-source-manager';
import { ColumnVisualizationTable } from '@/components/unified-config/column-visualization-table';
import { EbayColumnSettings } from '@/components/unified-config/ebay-column-settings';
import { AmazonColumnSettings } from '@/components/unified-config/amazon-column-settings';
import { ModelManagementSettings } from '@/components/settings/model-management-settings';
import { PromptManagementSettings } from '@/components/settings/prompt-management-settings';
import { CacheManagementSettings } from '@/components/settings/cache-management-settings';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function SettingsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">
            Manage your application settings, configuration sources, and column settings for different import systems
          </p>
        </div>
        
        <Tabs defaultValue="source-manager" className="space-y-4">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="source-manager">Configuration Source</TabsTrigger>
            <TabsTrigger value="product-import-columns">Akeneo/ Nav</TabsTrigger>
            <TabsTrigger value="ebay-columns">eBay</TabsTrigger>
            <TabsTrigger value="amazon-columns">Amazon</TabsTrigger>
            <TabsTrigger value="model-management">AI Models</TabsTrigger>
            <TabsTrigger value="prompt-management">AI Prompts</TabsTrigger>
            <TabsTrigger value="cache-management">Cache & Performance</TabsTrigger>
          </TabsList>
          
          <TabsContent value="source-manager" className="space-y-4">
            <ConfigurationSourceManager />
          </TabsContent>
          
          <TabsContent value="product-import-columns" className="space-y-4">
            <ColumnVisualizationTable />
          </TabsContent>
          
          <TabsContent value="ebay-columns" className="space-y-4">
            <EbayColumnSettings />
          </TabsContent>
          
          <TabsContent value="amazon-columns" className="space-y-4">
            <AmazonColumnSettings />
          </TabsContent>
          
          <TabsContent value="model-management" className="space-y-4">
            <ModelManagementSettings />
          </TabsContent>
          
          <TabsContent value="prompt-management" className="space-y-4">
            <PromptManagementSettings />
          </TabsContent>
          
          <TabsContent value="cache-management" className="space-y-4">
            <CacheManagementSettings />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}