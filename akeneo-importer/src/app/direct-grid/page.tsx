"use client";

import { useState } from "react";
import { DirectDataGrid } from "@/components/data-table/enhanced-data-grid";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>eft, Zap } from "lucide-react";
import Link from "next/link";

// Use the same interfaces as the component to avoid type conflicts
interface ConfigurationRow {
  Column_name: string;
  Type: "Navisionvorlage" | "Akeneo";
  Required: boolean;
  Default_Mapping: "Deactivated" | "String" | "1-to-1" | "AI Transform" | null;
  Default_Mapping_Content: string | null;
  Prompt: string | null;
  Output_Validation_Column: string | null;
}

interface GridConfig {
  targetColumns: ConfigurationRow[];
  sourceData: any[][];
  lookupTables: Record<string, any[]>;
  defaultMappings: Record<string, any>;
}

export default function DirectGridPage() {
  const [gridConfig, setGridConfig] = useState<GridConfig | null>(null);
  const [sourceColumns, setSourceColumns] = useState<string[]>([]);

  const handleUploadComplete = (config: GridConfig) => {
    setGridConfig(config);
    // Extract source columns from the first row of source data if available
    if (config.sourceData.length > 0) {
      // Assuming first row contains column names - adjust based on your data structure
      setSourceColumns(Array.from({ length: config.sourceData[0]?.length || 0 }, (_, i) => `Column_${i + 1}`));
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Link href="/">
                  <Button variant="ghost" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Back
                  </Button>
                </Link>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  <Zap className="h-3 w-3 mr-1" />
                  Direct Grid Mode
                </Badge>
              </div>
              <h1 className="text-3xl font-bold text-gray-900">
                Direct Data Grid Viewer
              </h1>
              <p className="text-gray-600 mt-1">
                Upload files and immediately view them in a high-performance, configuration-driven data grid
              </p>
            </div>
          </div>
        </div>

        {/* Features Overview */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-900">🚨 New: Direct Grid Workflow</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-blue-900 mb-2">Key Features:</h4>
                <ul className="space-y-1 text-blue-800">
                  <li>• <strong>No Wizard Steps</strong> - Direct file to grid</li>
                  <li>• <strong>Configuration-Driven</strong> - Google Sheets based setup</li>
                  <li>• <strong>Smart Column Display</strong> - Only Required=true columns</li>
                  <li>• <strong>High Performance</strong> - Google Sheets-level experience</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-blue-900 mb-2">Four Transformation Modes:</h4>
                <ul className="space-y-1 text-blue-800">
                  <li>• <strong>1-to-1 Source Mapping</strong> - Direct data display</li>
                  <li>• <strong>AI Transformation</strong> - LLM-powered processing</li>
                  <li>• <strong>String Overwrite</strong> - Static string values</li>
                  <li>• <strong>Column Deactivation</strong> - Hide/exclude columns</li>
                </ul>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-blue-200">
              <div className="flex items-center justify-between">
                <span className="text-blue-900 font-medium">Upload your own data file to get started</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Grid Status */}
        {gridConfig && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="text-green-800">
                    <strong>Grid Ready!</strong> Configuration loaded with{" "}
                    <Badge variant="secondary">{gridConfig.targetColumns.length}</Badge>{" "}
                    target columns and{" "}
                    <Badge variant="secondary">{gridConfig.sourceData.length}</Badge>{" "}
                    data rows.
                  </div>
                </div>
                <Badge className="bg-green-600">Active</Badge>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Grid Component */}
        <DirectDataGrid
          onUploadComplete={handleUploadComplete}
          initialGridConfig={gridConfig || undefined}
          sourceColumns={sourceColumns}
        />

        {/* Implementation Notes */}
        <Card className="mt-8 bg-gray-50 border-gray-200">
          <CardHeader>
            <CardTitle className="text-gray-700">Implementation Notes</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>
              <strong>Configuration Source:</strong> Loads from Google Sheets configuration (ID from .env.local).
            </p>
            <p>
              <strong>File Processing:</strong> Supports CSV and Excel files with immediate processing and grid display.
            </p>
            <p>
              <strong>Column Filtering:</strong> Only columns with Required=true in configuration are displayed by default.
            </p>
            <p>
              <strong>AI Integration:</strong> AI transformation endpoints return "test" in phase 1, full LLM integration in phase 2.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 