"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to jobs as the main entry point
    router.replace("/jobs");
  }, [router]);

  return (
    <DashboardLayout>
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to Jobs...</p>
        </div>
      </div>
    </DashboardLayout>
  );
} 