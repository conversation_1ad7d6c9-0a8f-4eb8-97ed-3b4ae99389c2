"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { 
  Menu, 
  X, 
  Upload, 
  FileSpreadsheet, 
  Database, 
  Settings,
  User,
  ChevronLeft,
  ChevronRight,
  Wifi,
  WifiOff
} from "lucide-react";
import { cn } from "@/lib/utils";

interface HealthStatus {
  status: 'ok' | 'error';
  cache: {
    status: 'connected' | 'disconnected';
    error: string | null;
  };
}

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const navigation = [
  { name: "Import Wizard", href: "/jobs", icon: FileSpreadsheet },
  { name: "Settings", href: "/settings", icon: Settings },
];

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const pathname = usePathname();

  useEffect(() => {
    const fetchHealth = async () => {
      try {
        const response = await fetch('/api/health');
        const data = await response.json();
        setHealthStatus(data);
      } catch (error) {
        console.error('Failed to fetch health status', error);
        setHealthStatus({
          status: 'error',
          cache: { status: 'disconnected', error: 'Failed to connect to backend' }
        });
      }
    };

    fetchHealth();
    const interval = setInterval(fetchHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="absolute inset-0 bg-gray-600 opacity-75" />
        </div>
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 bg-white shadow-lg transform lg:transform-none lg:static lg:inset-0 transition-all duration-300",
          // Mobile behavior
          sidebarOpen ? "translate-x-0 w-64" : "-translate-x-full lg:translate-x-0",
          // Desktop collapsible behavior
          sidebarCollapsed ? "lg:w-16" : "lg:w-64"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo and collapse toggle */}
          <div className="flex items-center justify-between h-16 px-4 border-b">
            <div className="flex items-center space-x-2">
              <Database className="h-8 w-8 text-blue-600 flex-shrink-0" />
              {(!sidebarCollapsed || sidebarOpen) && (
                <span className="text-xl font-bold text-gray-900 truncate">
                  Akeneo Importer
                </span>
              )}
            </div>
            
            {/* Desktop collapse toggle */}
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                className="hidden lg:flex"
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              >
                {sidebarCollapsed ? (
                  <ChevronRight className="h-4 w-4" />
                ) : (
                  <ChevronLeft className="h-4 w-4" />
                )}
              </Button>
              
              {/* Mobile close button */}
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setSidebarOpen(false)}
              >
                <X className="h-6 w-6" />
              </Button>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link key={item.name} href={item.href}>
                  <div
                    className={cn(
                      "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors group relative",
                      isActive
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-900",
                      sidebarCollapsed ? "justify-center lg:px-2" : ""
                    )}
                    title={sidebarCollapsed ? item.name : ""}
                  >
                    <Icon className={cn(
                      "h-5 w-5 flex-shrink-0",
                      sidebarCollapsed ? "" : "mr-3"
                    )} />
                    {(!sidebarCollapsed || sidebarOpen) && (
                      <span className="truncate">
                        {item.name}
                      </span>
                    )}
                    
                    {/* Tooltip for collapsed state */}
                    {sidebarCollapsed && (
                      <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                        {item.name}
                      </div>
                    )}
                  </div>
                </Link>
              );
            })}
          </nav>

          {/* System Status */}
          <div className="px-2 py-4 border-t">
            {healthStatus ? (
              <div className="flex items-center space-x-3 px-2">
                {healthStatus.cache.status === 'connected' ? (
                  <Wifi className="h-5 w-5 text-green-500" />
                ) : (
                  <WifiOff className="h-5 w-5 text-red-500" />
                )}
                {(!sidebarCollapsed || sidebarOpen) && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      Cache
                    </p>
                    <p className="text-xs text-gray-500 capitalize">
                      {healthStatus.cache.status}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-3 px-2">
                <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse" />
                {(!sidebarCollapsed || sidebarOpen) && (
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* User section */}
          <div className="border-t px-2 py-4">
            <div className={cn(
              "flex items-center space-x-3 mb-3 px-2",
              sidebarCollapsed ? "justify-center" : ""
            )}>
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
              </div>
              {(!sidebarCollapsed || sidebarOpen) && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    Public User
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    No authentication required
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Main content area */}
        <main className="flex-1 overflow-auto bg-gray-50 relative">
          {/* Mobile menu button - now floating */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden fixed top-4 left-4 z-30 bg-white shadow-md"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>
          
          <div className="w-full px-4 py-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
} 