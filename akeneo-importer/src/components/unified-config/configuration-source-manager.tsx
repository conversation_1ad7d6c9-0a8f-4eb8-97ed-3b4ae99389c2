'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileSpreadsheet, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  ExternalLink,
  Database
} from 'lucide-react';
import { useGoogleSheets } from '@/hooks/use-google-sheets';
import { useUnifiedConfig } from '@/hooks/use-unified-config';

export function ConfigurationSourceManager() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  // Hooks for Google Sheets configuration
  const googleSheets = useGoogleSheets();
  const unifiedConfig = useUnifiedConfig();

  useEffect(() => {
    checkGoogleSheetsStatus();
  }, []);

  const checkGoogleSheetsStatus = async () => {
    try {
      setLoading(true);
      await googleSheets.checkAuthStatus();
      
      if (googleSheets.authenticated) {
        try {
          await googleSheets.refreshCache();
        } catch (error) {
          console.error('Failed to load Google Sheets data:', error);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshSource = async () => {
    setIsRefreshing(true);
    try {
      await googleSheets.refreshCache();
      
      // Refresh unified configuration
      await unifiedConfig.refreshConfig();
      
    } catch (error) {
      console.error('Failed to refresh source:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getSourceStatusIcon = () => {
    if (googleSheets.authenticated) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
          <p>Loading configuration source...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Configuration Source
          </CardTitle>
          <CardDescription>
            Configuration is now exclusively managed through Google Sheets for real-time collaboration and updates.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Google Sheets Status */}
          <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2">
              <FileSpreadsheet className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">
                Google Sheets Configuration
              </span>
            </div>
            <Badge variant="outline" className="bg-blue-100 text-blue-800">
              Primary Source
            </Badge>
          </div>

          {/* Google Sheets Configuration Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileSpreadsheet className="h-5 w-5" />
                Google Sheets Configuration Management
                {getSourceStatusIcon()}
              </CardTitle>
              <CardDescription>
                Cloud-based configuration with real-time synchronization
              </CardDescription>
            </CardHeader>
            <CardContent>
              {googleSheets.authenticated ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {googleSheets.mappingDefinitions.length}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Mapping Definitions
                      </div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {googleSheets.worksheetNames.length}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Worksheets
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleRefreshSource}
                      disabled={isRefreshing}
                      className="flex-1"
                    >
                      {isRefreshing ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                      )}
                      Refresh Configuration
                    </Button>
                    <Button 
                      onClick={() => googleSheets.logout()}
                      variant="outline"
                    >
                      Disconnect
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileSpreadsheet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Not Connected to Google Sheets</h3>
                  <p className="text-muted-foreground mb-4">
                    Connect to Google Sheets to access cloud-based configuration with real-time synchronization.
                  </p>
                  <Button 
                    onClick={() => googleSheets.authenticate()}
                    disabled={googleSheets.loading}
                  >
                    {googleSheets.loading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <ExternalLink className="h-4 w-4 mr-2" />
                    )}
                    Connect to Google Sheets
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Warnings and Info */}
          {!googleSheets.authenticated && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Google Sheets authentication is required to access the configuration data. 
                The system will use mock data with limited functionality until connected.
              </AlertDescription>
            </Alert>
          )}

          {googleSheets.authenticated && googleSheets.mappingDefinitions.length === 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No mapping definitions found in Google Sheets. Please ensure your sheet contains the required configuration data.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 