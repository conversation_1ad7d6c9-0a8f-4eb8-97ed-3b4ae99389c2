'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  Filter, 
  Download,
  RefreshCw,
  Database,
  Package,
  CheckCircle,
  XCircle,
  AlertCircle,
  Brain,
  FileText,
  Info,
  MessageSquare
} from 'lucide-react';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

type SourceType = 'product-import' | 'ebay' | 'amazon';

interface ColumnData {
  column_name: string;
  display_name?: string;
  type: string;
  source: 'ERP' | 'PIM' | 'eBay' | 'Amazon';
  has_configuration: boolean;
  configured_source?: string;
  default_mapping?: string | null;
  default_mapping_content?: string | null;
  prompt?: string | null;
  required: boolean;
  output_validation_column?: string | null;
  description?: string | null;
  description_source?: 'google-sheets' | 'akeneo' | 'both' | null;
  label_description?: string | null;
  guideline_description?: string | null;
  combined_description?: string | null;
  has_additional_data?: boolean;
  additional_data_rows?: number;
  additional_data_columns?: string[];
  api_metadata?: {
    attribute_type?: string;
    localizable?: boolean;
    scopable?: boolean;
    group?: string | null;
    unique?: boolean;
    labels?: Record<string, string>;
    guidelines?: Record<string, string>;
    group_labels?: Record<string, string>;
  };
}

interface FilterState {
  search: string;
  sourceFilter: 'all' | 'erp' | 'pim' | 'ebay' | 'amazon';
  configFilter: 'all' | 'configured' | 'unconfigured';
  requiredFilter: 'all' | 'required' | 'optional';
}

interface ColumnVisualizationTableProps {
  isLoading?: boolean;
  onRefresh?: () => void;
  sourceType?: SourceType;
}

export function ColumnVisualizationTable({ 
  isLoading = false, 
  onRefresh,
  sourceType = 'product-import'
}: ColumnVisualizationTableProps) {
  const [columns, setColumns] = useState<ColumnData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    sourceFilter: 'all',
    configFilter: 'all',
    requiredFilter: 'all'
  });

  // Fetch column data based on source type
  const fetchColumnData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      let allColumns: ColumnData[] = [];

      if (sourceType === 'ebay') {
        // Fetch eBay columns
        const response = await fetch('/api/ebay/columns');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.columns) {
            allColumns = data.columns.map((col: any) => ({
              ...col,
              source: 'eBay' as const
            }));
          }
        }
      } else if (sourceType === 'amazon') {
        // Fetch Amazon columns
        const response = await fetch('/api/amazon/columns');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.columns) {
            allColumns = data.columns.map((col: any) => ({
              ...col,
              source: 'Amazon' as const
            }));
          }
        }
      } else {
        // Default to product-import (original logic)
        const [unifiedResponse, akeneoResponse] = await Promise.all([
          fetch('/api/unified-config?showBoth=true'),
          fetch('/api/akeneo/attributes?all=true')
        ]);

        // Process unified configuration
        if (unifiedResponse.ok) {
        const unifiedData = await unifiedResponse.json();
        if (unifiedData.success && unifiedData.data?.columns) {
          const allConfiguredColumns = unifiedData.data.columns.map((col: any) => ({
            column_name: col.column_name,
            display_name: col.column_name,
            type: col.type,
            // TYPE MAPPING LOGIC:
            // - Everything from Akeneo API = PIM
            // - From configuration (Excel/Google Sheets):
            //   - Type = "Navisionvorlage" → ERP 
            //   - Type = "Akeneo" → PIM
            source: col.type === 'Navisionvorlage' || 
                   col.type?.toLowerCase() === 'navisionvorlage' || 
                   col.type?.toLowerCase() === 'erp' ? 'ERP' : 'PIM',
            has_configuration: true,
            configured_source: unifiedData.data.source,
            default_mapping: col.default_mapping,
            default_mapping_content: col.default_mapping_content,
            prompt: col.prompt,
            required: col.required || false,
            output_validation_column: col.output_validation_column,
            description: col.description,
            description_source: col.description_source,
            label_description: col.label_description,
            guideline_description: col.guideline_description,
            combined_description: col.combined_description,
            has_additional_data: col.has_additional_data,
            additional_data_rows: col.additional_data_rows,
            additional_data_columns: col.additional_data_columns
          }));
          
          // Filter out invalid/empty columns
          const configuredColumns = allConfiguredColumns.filter((col: ColumnData) => 
            col.column_name && 
            col.column_name.trim() !== '' && 
            col.column_name !== 'undefined' &&
            col.type && 
            col.type.trim() !== ''
          );
          
          console.log(`Filtered configured columns: ${allConfiguredColumns.length} → ${configuredColumns.length} valid`);
          allColumns = configuredColumns;
        }
      }

      // Process Akeneo API data
      if (akeneoResponse.ok) {
        const akeneoData = await akeneoResponse.json();
        if (akeneoData.success && akeneoData.attributes) {
          const akeneoColumns = akeneoData.attributes.map((attr: any) => {
            // Enhanced description extraction from Akeneo API
            let description = '';
            let labelDescription = '';
            let guidelineDescription = '';
            
            // Extract label (typically the display name in German)
            if (attr.labels && attr.labels.de_DE) {
              labelDescription = attr.labels.de_DE;
            }
            
            // Extract guidelines (typically more detailed usage instructions)
            if (attr.guidelines && attr.guidelines.de_DE) {
              guidelineDescription = attr.guidelines.de_DE;
            }
            
            // Combine descriptions intelligently
            if (labelDescription && guidelineDescription) {
              description = `${labelDescription} - ${guidelineDescription}`;
            } else if (labelDescription) {
              description = labelDescription;
            } else if (guidelineDescription) {
              description = guidelineDescription;
            }

            return {
              column_name: attr.code,
              display_name: attr.labels?.de_DE || attr.code,
              type: 'Akeneo',
              // TYPE MAPPING LOGIC: Everything from Akeneo API = PIM
              source: 'PIM' as const,
              has_configuration: false,
              default_mapping: null,
              prompt: null,
              required: false,
              // Enhanced description handling
              description: description || null,
              description_source: description ? 'akeneo' as const : null,
              label_description: labelDescription || null,
              guideline_description: guidelineDescription || null,
              api_metadata: {
                attribute_type: attr.type,
                localizable: attr.localizable || false,
                scopable: attr.scopable || false,
                group: attr.group || null,
                unique: attr.unique || false,
                // Enhanced API metadata
                labels: attr.labels || {},
                guidelines: attr.guidelines || {},
                group_labels: attr.group_labels || {}
              }
            };
          });

          // Merge with configured columns, avoiding duplicates
          const configuredColumnNames = new Set(allColumns.map((col: any) => col.column_name));
          const newAkeneoColumns = akeneoColumns.filter((col: any) => !configuredColumnNames.has(col.column_name));
          
          // Update existing columns with API metadata AND description info
          allColumns = allColumns.map((col: any) => {
            const akeneoMatch = akeneoColumns.find((ak: any) => ak.column_name === col.column_name);
            if (akeneoMatch) {
              const updatedCol = { ...col, api_metadata: akeneoMatch.api_metadata };
              
              // Enhanced description merging
              // If the configured column doesn't have a description but Akeneo does, use Akeneo's
              if (!updatedCol.description && akeneoMatch.description) {
                updatedCol.description = akeneoMatch.description;
                updatedCol.description_source = 'akeneo';
                updatedCol.label_description = akeneoMatch.label_description;
                updatedCol.guideline_description = akeneoMatch.guideline_description;
              } else if (updatedCol.description && akeneoMatch.description) {
                // If both have descriptions, combine them intelligently
                updatedCol.combined_description = `${updatedCol.description} | Akeneo: ${akeneoMatch.description}`;
                updatedCol.description_source = 'both';
                updatedCol.label_description = akeneoMatch.label_description;
                updatedCol.guideline_description = akeneoMatch.guideline_description;
              }
              
              return updatedCol;
            }
            return col;
          });

          allColumns = [...allColumns, ...newAkeneoColumns];
        }
      }

      setColumns(allColumns);
      toast.success(`Loaded ${allColumns.length} columns`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch column data';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Filter columns based on current filters
  const filteredColumns = useMemo(() => {
    return columns.filter(col => {
      // Search filter
      if (filters.search && !col.column_name.toLowerCase().includes(filters.search.toLowerCase()) &&
          !(col.display_name?.toLowerCase().includes(filters.search.toLowerCase()))) {
        return false;
      }

      // Source filter
      if (filters.sourceFilter !== 'all') {
        if (filters.sourceFilter === 'erp' && col.source !== 'ERP') return false;
        if (filters.sourceFilter === 'pim' && col.source !== 'PIM') return false;
      }

      // Configuration filter
      if (filters.configFilter !== 'all') {
        if (filters.configFilter === 'configured' && !col.has_configuration) return false;
        if (filters.configFilter === 'unconfigured' && col.has_configuration) return false;
      }

      // Required filter
      if (filters.requiredFilter !== 'all') {
        if (filters.requiredFilter === 'required' && !col.required) return false;
        if (filters.requiredFilter === 'optional' && col.required) return false;
      }

      return true;
    });
  }, [columns, filters]);

  // Statistics
  const statistics = useMemo(() => {
    return {
      total: columns.length,
      erp: columns.filter(col => col.source === 'ERP').length,
      pim: columns.filter(col => col.source === 'PIM').length,
      configured: columns.filter(col => col.has_configuration).length,
      unconfigured: columns.filter(col => !col.has_configuration).length,
      required: columns.filter(col => col.required).length,
      withPrompts: columns.filter(col => col.prompt).length,
      // Enhanced description statistics
      withDescriptions: columns.filter(col => col.description).length,
      googleSheetsDescriptions: columns.filter(col => col.description && col.configured_source === 'google-sheets' && col.description_source !== 'akeneo').length,
      akeneoDescriptions: columns.filter(col => col.description && col.description_source === 'akeneo').length,
      bothDescriptions: columns.filter(col => col.description && col.description_source === 'both').length,
      withAdditionalData: columns.filter(col => col.has_additional_data).length,
      filtered: filteredColumns.length
    };
  }, [columns, filteredColumns]);

  // Load data on mount
  useEffect(() => {
    fetchColumnData();
  }, []);

  const handleRefresh = () => {
    fetchColumnData();
    onRefresh?.();
  };

  const exportData = () => {
    const dataToExport = {
      statistics,
      columns: filteredColumns,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `column-visualization-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Data exported successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {sourceType === 'ebay' ? 'eBay Column Settings' : 
                 sourceType === 'amazon' ? 'Amazon Column Settings' : 
                 'Column Settings for Product Import'}
              </CardTitle>
              <CardDescription>
                {sourceType === 'ebay' ? 'Manage eBay product listing columns and their configuration' :
                 sourceType === 'amazon' ? 'Manage Amazon product listing columns and their configuration' :
                 'View and filter all columns from ERP and PIM systems with their configuration status'}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={exportData}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading || isLoading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading || isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Statistics Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-9 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{statistics.total}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{statistics.erp}</div>
              <div className="text-xs text-muted-foreground">ERP</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{statistics.pim}</div>
              <div className="text-xs text-muted-foreground">PIM</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-emerald-600">{statistics.configured}</div>
              <div className="text-xs text-muted-foreground">Configured</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{statistics.unconfigured}</div>
              <div className="text-xs text-muted-foreground">Unconfigured</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{statistics.required}</div>
              <div className="text-xs text-muted-foreground">Required</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">{statistics.withPrompts}</div>
              <div className="text-xs text-muted-foreground">AI Prompts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-teal-600">{statistics.withDescriptions}</div>
              <div className="text-xs text-muted-foreground">Descriptions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cyan-600">{statistics.withAdditionalData}</div>
              <div className="text-xs text-muted-foreground">Extra Data</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Search Columns</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by column name..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Source System</Label>
              <Select 
                value={filters.sourceFilter} 
                onValueChange={(value: FilterState['sourceFilter']) => 
                  setFilters(prev => ({ ...prev, sourceFilter: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Systems ({statistics.total})</SelectItem>
                  <SelectItem value="erp">ERP Only ({statistics.erp})</SelectItem>
                  <SelectItem value="pim">PIM Only ({statistics.pim})</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Configuration Status</Label>
              <Select 
                value={filters.configFilter} 
                onValueChange={(value: FilterState['configFilter']) => 
                  setFilters(prev => ({ ...prev, configFilter: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Columns</SelectItem>
                  <SelectItem value="configured">Configured ({statistics.configured})</SelectItem>
                  <SelectItem value="unconfigured">Unconfigured ({statistics.unconfigured})</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Required Status</Label>
              <Select 
                value={filters.requiredFilter} 
                onValueChange={(value: FilterState['requiredFilter']) => 
                  setFilters(prev => ({ ...prev, requiredFilter: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="required">Required ({statistics.required})</SelectItem>
                  <SelectItem value="optional">Optional</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            Showing {statistics.filtered} of {statistics.total} columns
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Columns Table */}
      <Card>
        <CardHeader>
          <CardTitle>Columns ({statistics.filtered})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading || isLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>Loading columns...</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Column Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Required</TableHead>
                    <TableHead>Mapping</TableHead>
                    <TableHead>AI Prompt</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Validation</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredColumns.map((column, index) => (
                    <TableRow key={`${column.source}-${column.column_name}-${index}`}>
                      <TableCell className="font-medium">
                        <div className="max-w-xs truncate" title={column.column_name}>
                          {column.column_name}
                        </div>
                        {column.display_name && column.display_name !== column.column_name && (
                          <div className="text-xs text-muted-foreground truncate max-w-xs" title={column.display_name}>
                            {column.display_name}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={column.source === 'ERP' ? 'default' : 'secondary'}>
                          {column.type}
                        </Badge>
                        {column.api_metadata?.attribute_type && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {column.api_metadata.attribute_type}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {column.source === 'ERP' ? (
                            <Database className="h-4 w-4 text-blue-600" />
                          ) : (
                            <Package className="h-4 w-4 text-purple-600" />
                          )}
                          <span className="text-sm">{column.source}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {column.has_configuration ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-gray-400" />
                          )}
                          <Badge variant={column.has_configuration ? 'default' : 'outline'}>
                            {column.has_configuration ? 'Configured' : 'API Only'}
                          </Badge>
                        </div>
                        {column.configured_source && (
                          <div className="text-xs text-muted-foreground mt-1">
                            via {column.configured_source}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {column.required ? (
                          <Badge variant="destructive" className="text-xs">Required</Badge>
                        ) : (
                          <span className="text-muted-foreground text-sm">Optional</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {column.default_mapping && (
                          <Badge variant="outline" className="text-xs">
                            {column.default_mapping}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {column.prompt ? (
                            <div className="flex items-center gap-2">
                              <Brain className="h-4 w-4 text-blue-600" />
                              <div className="text-xs text-muted-foreground max-w-32 truncate" title={column.prompt}>
                                {column.prompt.substring(0, 50)}...
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">None</span>
                          )}
                          {column.default_mapping_content && (
                            <div className="flex items-center gap-2">
                              <MessageSquare className="h-4 w-4 text-purple-600" />
                              <Dialog>
                                <DialogTrigger asChild>
                                  <button className="text-xs text-purple-600 hover:underline flex items-center gap-1">
                                    Custom Template
                                    <Info className="h-3 w-3" />
                                  </button>
                                </DialogTrigger>
                                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                                  <DialogHeader>
                                    <DialogTitle>Custom Mapping Prompt Template</DialogTitle>
                                    <DialogDescription>
                                      Custom template for column: <strong>{column.column_name}</strong>
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div className="space-y-4">
                                    <div>
                                      <label className="text-sm font-medium">Template Content:</label>
                                      <div className="mt-2 p-3 bg-gray-50 rounded border font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
                                        {column.default_mapping_content}
                                      </div>
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      This template will override the default prompt template when processing the "{column.column_name}" column in AI transformations.
                                    </div>
                                  </div>
                                </DialogContent>
                              </Dialog>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {column.description ? (
                          <div className="flex items-center gap-2">
                            <Badge variant={
                              column.description_source === 'google-sheets' ? 'default' : 
                              column.description_source === 'akeneo' ? 'secondary' : 
                              column.description_source === 'both' ? 'destructive' : 'outline'
                            }>
                              {column.description_source === 'google-sheets' ? 'Google Sheets' : 
                               column.description_source === 'akeneo' ? 'Akeneo' : 
                               column.description_source === 'both' ? 'Both' : 'Available'}
                            </Badge>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                  <Info className="h-3 w-3" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                  <DialogTitle className="flex items-center gap-2">
                                    <MessageSquare className="h-5 w-5" />
                                    Description for {column.column_name}
                                  </DialogTitle>
                                  <DialogDescription>
                                    Source: {column.source} • Type: {column.type}
                                    {column.description_source && ` • Description from: ${column.description_source}`}
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4">
                                  {column.description && (
                                    <div>
                                      <h4 className="font-medium text-sm text-muted-foreground mb-2">Description</h4>
                                      <p className="text-sm leading-relaxed">{column.description}</p>
                                    </div>
                                  )}
                                  
                                  {column.label_description && column.guideline_description && (
                                    <>
                                      <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-2">Akeneo Label</h4>
                                          <p className="text-sm leading-relaxed">{column.label_description}</p>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-2">Akeneo Guidelines</h4>
                                          <p className="text-sm leading-relaxed">{column.guideline_description}</p>
                                      </div>
                                    </>
                                  )}
                                  
                                  {column.combined_description && (
                                    <div>
                                      <h4 className="font-medium text-sm text-muted-foreground mb-2">Combined Description</h4>
                                      <p className="text-sm leading-relaxed">{column.combined_description}</p>
                                    </div>
                                  )}
                                  
                                  {column.has_additional_data && (
                                    <div>
                                      <h4 className="font-medium text-sm text-muted-foreground mb-2">Additional Data</h4>
                                      <p className="text-sm">
                                        {column.additional_data_rows} rows in dedicated worksheet
                                        {column.additional_data_columns && column.additional_data_columns.length > 0 && (
                                          <span className="block text-xs text-muted-foreground mt-1">
                                            Columns: {column.additional_data_columns.join(', ')}
                                          </span>
                                        )}
                                      </p>
                                    </div>
                                  )}
                                  
                                  {column.api_metadata && (
                                    <div>
                                      <h4 className="font-medium text-sm text-muted-foreground mb-2">API Metadata</h4>
                                      <div className="text-xs space-y-1">
                                        <p>Type: {column.api_metadata.attribute_type}</p>
                                        {column.api_metadata.group && <p>Group: {column.api_metadata.group}</p>}
                                        {column.api_metadata.localizable && <p>✓ Localizable</p>}
                                        {column.api_metadata.scopable && <p>✓ Scopable</p>}
                                        {column.api_metadata.unique && <p>✓ Unique</p>}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </DialogContent>
                            </Dialog>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">No description</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {column.output_validation_column && (
                          <Badge variant="outline" className="text-xs">
                            {column.output_validation_column}
                          </Badge>
                        )}
                        {column.api_metadata && (
                          <div className="flex gap-1 mt-1">
                            {column.api_metadata.localizable && (
                              <Badge variant="outline" className="text-xs">Local</Badge>
                            )}
                            {column.api_metadata.scopable && (
                              <Badge variant="outline" className="text-xs">Scope</Badge>
                            )}
                            {column.api_metadata.unique && (
                              <Badge variant="outline" className="text-xs">Unique</Badge>
                            )}
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {filteredColumns.length === 0 && !loading && !isLoading && (
                <div className="text-center py-8 text-muted-foreground">
                  No columns found matching your filters
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 