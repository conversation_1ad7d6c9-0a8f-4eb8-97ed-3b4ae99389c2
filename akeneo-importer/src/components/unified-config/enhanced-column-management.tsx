'use client';

import React, { useState, useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Info } from 'lucide-react';
import { UnifiedColumn } from '@/types';

interface EnhancedColumnManagementProps {
  columns: UnifiedColumn[];
  isLoading?: boolean;
  onColumnSelect?: (column: UnifiedColumn) => void;
  onViewConfiguration?: (column: UnifiedColumn) => void;
  selectedColumn?: UnifiedColumn | null;
}

export function EnhancedColumnManagement({
  columns,
  isLoading = false,
  onColumnSelect,
  onViewConfiguration,
  selectedColumn
}: EnhancedColumnManagementProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'source'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter and sort columns
  const filteredAndSortedColumns = useMemo(() => {
    let filtered = columns;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = columns.filter(col => 
        col.column_name.toLowerCase().includes(query) ||
        (col.prompt && col.prompt.toLowerCase().includes(query)) ||
        (col.default_mapping_content && col.default_mapping_content.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: string;
      let bValue: string;

      switch (sortBy) {
        case 'name':
          aValue = a.column_name;
          bValue = b.column_name;
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'source':
          aValue = a.source;
          bValue = b.source;
          break;
        default:
          aValue = a.column_name;
          bValue = b.column_name;
      }

      const comparison = aValue.localeCompare(bValue);
      return sortDirection === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [columns, searchQuery, sortBy, sortDirection]);

  const handleSort = (field: 'name' | 'type' | 'source') => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: 'name' | 'type' | 'source') => {
    if (sortBy !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  const getMappingTypeBadge = (column: UnifiedColumn) => {
    if (!column.default_mapping) {
      return <Badge variant="outline">No mapping</Badge>;
    }

    switch (column.default_mapping) {
      case 'Deactivated':
        return <Badge variant="secondary">Deactivated</Badge>;
      case 'String':
        return <Badge variant="default">String</Badge>;
      case 'AI':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">AI Transform</Badge>;
      default:
        return <Badge variant="outline">{column.default_mapping}</Badge>;
    }
  };

  const getSourceBadge = (source: string) => {
    switch (source) {
      case 'config-only':
        return <Badge variant="secondary">Config Only</Badge>;
      case 'api-only':
        return <Badge variant="outline">API Only</Badge>;
      case 'api-with-config':
        return <Badge variant="default">API + Config</Badge>;
      default:
        return <Badge variant="outline">{source}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2">Loading columns...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (columns.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center p-8 text-muted-foreground">
            <p>No columns available.</p>
            <p className="text-sm mt-2">Check your configuration and Akeneo connection.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Column Management</CardTitle>
          <div className="flex items-center space-x-4">
            <Input
              placeholder="Search columns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
            <div className="text-sm text-muted-foreground">
              Showing {filteredAndSortedColumns.length} of {columns.length} columns
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Columns Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer select-none"
                  onClick={() => handleSort('name')}
                >
                  Column Name {getSortIcon('name')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer select-none"
                  onClick={() => handleSort('type')}
                >
                  Type {getSortIcon('type')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer select-none"
                  onClick={() => handleSort('source')}
                >
                  Source {getSortIcon('source')}
                </TableHead>
                <TableHead>Mapping</TableHead>
                <TableHead>Required</TableHead>
                <TableHead>Configuration</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedColumns.map((column) => (
                <TableRow 
                  key={`${column.type}-${column.column_name}`}
                  className={selectedColumn?.column_name === column.column_name ? 'bg-muted/50' : ''}
                >
                  <TableCell className="font-medium">
                    <div className="max-w-xs truncate" title={column.column_name}>
                      {column.column_name}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={column.type === 'Navisionvorlage' ? 'default' : 'secondary'}>
                      {column.type === 'Navisionvorlage' ? 'ERP' : 'PIM'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {getSourceBadge(column.source)}
                  </TableCell>
                  <TableCell>
                    {getMappingTypeBadge(column)}
                  </TableCell>
                  <TableCell>
                    {column.required ? (
                      <Badge variant="destructive" className="text-xs">Required</Badge>
                    ) : (
                      <span className="text-muted-foreground text-sm">Optional</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {column.has_configuration ? (
                      <div className="space-y-1">
                        {column.prompt && (
                          <div className="text-xs text-muted-foreground truncate max-w-xs" title={column.prompt}>
                            💬 {column.prompt}
                          </div>
                        )}
                        {column.default_mapping_content && (
                          <Dialog>
                            <DialogTrigger asChild>
                              <button className="text-xs text-purple-600 hover:underline flex items-center gap-1">
                                🎯 Custom Template
                                <Info className="h-3 w-3" />
                              </button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                              <DialogHeader>
                                <DialogTitle>Custom Mapping Prompt Template</DialogTitle>
                                <DialogDescription>
                                  Custom template for column: <strong>{column.column_name}</strong>
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div>
                                  <label className="text-sm font-medium">Template Content:</label>
                                  <div className="mt-2 p-3 bg-gray-50 rounded border font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
                                    {column.default_mapping_content}
                                  </div>
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  This template will override the default prompt template when processing the "{column.column_name}" column in AI transformations.
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        )}
                        {column.output_validation_column && (
                          <div className="text-xs text-blue-600">
                            ✓ Validation: {column.output_validation_column}
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-muted-foreground text-sm">No configuration</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      {onColumnSelect && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onColumnSelect(column)}
                        >
                          Select
                        </Button>
                      )}
                      {onViewConfiguration && column.has_configuration && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onViewConfiguration(column)}
                        >
                          View
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Selected Column Details */}
      {selectedColumn && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Selected Column Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="font-medium">Column Name</div>
                <div className="text-muted-foreground">{selectedColumn.column_name}</div>
              </div>
              <div>
                <div className="font-medium">Type</div>
                <div className="text-muted-foreground">{selectedColumn.type}</div>
              </div>
              <div>
                <div className="font-medium">Source</div>
                <div className="text-muted-foreground">{selectedColumn.source}</div>
              </div>
              <div>
                <div className="font-medium">Required</div>
                <div className="text-muted-foreground">{selectedColumn.required ? 'Yes' : 'No'}</div>
              </div>
              {selectedColumn.api_metadata && (
                <>
                  <div>
                    <div className="font-medium">Attribute Type</div>
                    <div className="text-muted-foreground">{selectedColumn.api_metadata.attribute_type}</div>
                  </div>
                  <div>
                    <div className="font-medium">Group</div>
                    <div className="text-muted-foreground">{selectedColumn.api_metadata.group}</div>
                  </div>
                  <div>
                    <div className="font-medium">Localizable</div>
                    <div className="text-muted-foreground">{selectedColumn.api_metadata.localizable ? 'Yes' : 'No'}</div>
                  </div>
                  <div>
                    <div className="font-medium">Scopable</div>
                    <div className="text-muted-foreground">{selectedColumn.api_metadata.scopable ? 'Yes' : 'No'}</div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 