'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from "@/components/ui/checkbox";
import { RefreshCw } from "lucide-react";
import { 
  ColumnTypeFilter, 
  UnifiedColumnStatistics 
} from '@/types';
import { useConfigurationStore } from "@/stores/configuration-store";

interface ColumnTypeFilterProps {
  filter: ColumnTypeFilter;
  onFilterChange: (filter: ColumnTypeFilter) => void;
  statistics: UnifiedColumnStatistics;
  connectionStatus: {
    connected: boolean;
    configured: boolean;
    attributes_count?: number;
    families_count?: number;
    categories_count?: number;
    last_sync?: string;
    last_test?: string;
  };
  isLoading?: boolean;
  onRefresh?: () => void;
}

export function ColumnTypeFilterComponent({
  filter,
  onFilterChange,
  statistics,
  connectionStatus,
  isLoading = false,
  onRefresh
}: ColumnTypeFilterProps) {
  const configStore = useConfigurationStore();
  
  const handleFilterChange = (newFilter: Partial<ColumnTypeFilter>) => {
    onFilterChange({ ...filter, ...newFilter });
  };

  const handleRefresh = async () => {
    if (onRefresh) {
      onRefresh();
    } else {
      // Use global store refresh if no local refresh provided
      await configStore.refreshConnection();
    }
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center justify-between">
            Akeneo Connection Status
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading || configStore.isTestingConnection}
            >
              {isLoading || configStore.isTestingConnection ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3" />
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center space-x-2">
            <Badge 
              variant={connectionStatus.connected ? "default" : "destructive"}
            >
              {connectionStatus.connected ? 'Connected' : 'Disconnected'}
            </Badge>
            {connectionStatus.attributes_count && (
              <span className="text-sm text-muted-foreground">
                {connectionStatus.attributes_count} attributes available
              </span>
            )}
          </div>
          {connectionStatus.last_sync && (
            <p className="text-xs text-muted-foreground mt-2">
              Last sync: {new Date(connectionStatus.last_sync).toLocaleString()}
            </p>
          )}
          {connectionStatus.last_test && (
            <p className="text-xs text-muted-foreground mt-1">
              Last test: {new Date(connectionStatus.last_test).toLocaleString()}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Column Type Filter */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Column Types</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="erp-only"
                  checked={filter.showERP && !filter.showBoth}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      handleFilterChange({ showERP: true, showPIM: false, showBoth: false });
                    }
                  }}
                />
                <label htmlFor="erp-only" className="text-sm font-medium cursor-pointer">
                  ERP Only
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">Navisionvorlage</span>
                <Badge variant="outline">{statistics.totalERPColumns}</Badge>
              </div>
            </div>

            <div className="flex items-center justify-between opacity-60">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="pim-only"
                  checked={false}
                  disabled={true}
                  onCheckedChange={() => {}}
                />
                <label htmlFor="pim-only" className="text-sm font-medium cursor-not-allowed text-gray-400">
                  PIM Only
                  <span className="ml-2 text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded-full">
                    Coming Soon
                  </span>
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">Akeneo</span>
                <Badge variant="outline">{statistics.totalPIMApiColumns}</Badge>
              </div>
            </div>

            <div className="flex items-center justify-between opacity-60">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="both"
                  checked={false}
                  disabled={true}
                  onCheckedChange={() => {}}
                />
                <label htmlFor="both" className="text-sm font-medium cursor-not-allowed text-gray-400">
                  Both
                  <span className="ml-2 text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded-full">
                    Coming Soon
                  </span>
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">ERP + PIM</span>
                <Badge variant="outline">{statistics.totalERPColumns + statistics.totalPIMApiColumns}</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Coverage */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Configuration Coverage</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Configured PIM</span>
              <Badge variant="default">{statistics.totalPIMConfigured}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Unconfigured PIM</span>
              <Badge variant="secondary">{statistics.totalPIMUnconfigured}</Badge>
            </div>
            <div className="pt-2">
              <div className="flex items-center justify-between text-sm mb-2">
                <span>Coverage</span>
                <span className="font-medium">{Math.round(statistics.configurationCoverage)}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all"
                  style={{ width: `${statistics.configurationCoverage}%` }}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Connection Warning */}
      {!connectionStatus.connected && (
        <Card className="border-destructive/50">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2 text-sm text-destructive">
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span>
                Akeneo disconnected. Only ERP columns are available. 
                Check your configuration in Settings.
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 