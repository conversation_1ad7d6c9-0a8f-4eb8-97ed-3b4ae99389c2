'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// Using simple div for alerts instead of Alert component
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useUnifiedConfig } from '@/hooks/use-unified-config';
import { useConfigurationStore } from '@/stores/configuration-store';
import { ColumnTypeFilterComponent } from './column-type-filter';
import { EnhancedColumnManagement } from './enhanced-column-management';
import { 
  RefreshCw, 
  Settings, 
  Database, 
  CheckCircle, 
  AlertCircle,
  BarChart3,
  Eye,
  EyeOff
} from 'lucide-react';
import { UnifiedColumn } from '@/types';

export function UnifiedConfigurationManager() {
  const configStore = useConfigurationStore();
  const {
    columns,
    statistics,
    isLoading,
    error,
    filter,
    setFilter,
    refreshConfig
  } = useUnifiedConfig();

  const [selectedColumn, setSelectedColumn] = useState<UnifiedColumn | null>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Use global connection status from store
  const connectionStatus = configStore.connectionStatus;
  const isConnecting = configStore.isTestingConnection;
  const connectionError = configStore.error;

  // Test connection using global store
  const testConnection = useCallback(async () => {
    await configStore.testConnection();
  }, [configStore]);

  // Refresh configuration
  const refreshAll = useCallback(async () => {
    await Promise.all([
      configStore.refreshConnection(),
      refreshConfig()
    ]);
  }, [configStore, refreshConfig]);

  const handleColumnSelect = (column: UnifiedColumn) => {
    setSelectedColumn(column);
  };

  const handleViewConfiguration = (column: UnifiedColumn) => {
    setSelectedColumn(column);
    // Could navigate to a detailed view or open a modal
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>Unified Configuration Management</span>
              </CardTitle>
              <CardDescription>
                Manage ERP and PIM columns from a unified interface with real-time Akeneo integration
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={testConnection}
                disabled={isConnecting}
              >
                {isConnecting ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Test Connection
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshAll}
                disabled={isLoading || isConnecting}
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh All
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>



      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-12 gap-6">
        {/* Sidebar */}
        <div className={`transition-all duration-300 ${
          sidebarCollapsed ? 'col-span-1' : 'col-span-3'
        }`}>
          <ColumnTypeFilterComponent
            filter={filter}
            onFilterChange={setFilter}
            statistics={statistics}
            connectionStatus={connectionStatus}
            isLoading={isLoading || isConnecting}
            onRefresh={testConnection}
          />
        </div>

        {/* Main Content Area */}
        <div className={`transition-all duration-300 ${
          sidebarCollapsed ? 'col-span-11' : 'col-span-9'
        }`}>
          <Tabs defaultValue="overview" className="space-y-4">
            <div className="flex items-center justify-between">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="columns">Column Management</TabsTrigger>
                <TabsTrigger value="statistics">Statistics</TabsTrigger>
              </TabsList>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              >
                <Settings className="h-4 w-4 mr-2" />
                {sidebarCollapsed ? 'Show' : 'Hide'} Sidebar
              </Button>
            </div>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">ERP Columns</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">
                      {statistics.totalERPColumns}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Navisionvorlage
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">PIM Columns</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {statistics.totalPIMApiColumns}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      From Akeneo API
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Configured</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-600">
                      {statistics.totalPIMConfigured}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      PIM with config
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Coverage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-orange-600">
                      {Math.round(statistics.configurationCoverage)}%
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Configuration coverage
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button variant="outline" className="justify-start">
                      <Settings className="h-4 w-4 mr-2" />
                      View ERP Columns
                    </Button>
                    <Button variant="outline" className="justify-start" disabled={!connectionStatus.connected}>
                      <Database className="h-4 w-4 mr-2" />
                      View PIM Columns
                    </Button>
                    <Button variant="outline" className="justify-start">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      View Statistics
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Connection Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Connection Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>Akeneo API</span>
                      <Badge variant={connectionStatus.connected ? "default" : "destructive"}>
                        {connectionStatus.connected ? 'Connected' : 'Disconnected'}
                      </Badge>
                    </div>
                    {connectionStatus.attributes_count && (
                      <div className="flex items-center justify-between">
                        <span>Available Attributes</span>
                        <Badge variant="secondary">{connectionStatus.attributes_count}</Badge>
                      </div>
                    )}
                    {connectionStatus.last_sync && (
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>Last Sync</span>
                        <span>{new Date(connectionStatus.last_sync).toLocaleString()}</span>
                      </div>
                    )}
                    {connectionStatus.last_test && (
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>Last Test</span>
                        <span>{new Date(connectionStatus.last_test).toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Column Management Tab */}
            <TabsContent value="columns" className="space-y-4">
              <EnhancedColumnManagement
                columns={columns}
                isLoading={isLoading}
                onColumnSelect={handleColumnSelect}
                onViewConfiguration={handleViewConfiguration}
                selectedColumn={selectedColumn}
              />
            </TabsContent>

            {/* Statistics Tab */}
            <TabsContent value="statistics" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Column Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span>ERP Columns</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ 
                                width: `${(statistics.totalERPColumns / (statistics.totalERPColumns + statistics.totalPIMApiColumns)) * 100}%` 
                              }}
                            />
                          </div>
                          <span className="text-sm font-medium">{statistics.totalERPColumns}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>PIM Columns</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ 
                                width: `${(statistics.totalPIMApiColumns / (statistics.totalERPColumns + statistics.totalPIMApiColumns)) * 100}%` 
                              }}
                            />
                          </div>
                          <span className="text-sm font-medium">{statistics.totalPIMApiColumns}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Configuration Coverage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-600">
                          {Math.round(statistics.configurationCoverage)}%
                        </div>
                        <p className="text-sm text-muted-foreground">
                          PIM columns with configuration
                        </p>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Configured</span>
                          <span>{statistics.totalPIMConfigured}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Unconfigured</span>
                          <span>{statistics.totalPIMUnconfigured}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
} 