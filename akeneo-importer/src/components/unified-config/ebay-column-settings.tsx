'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Search, 
  Filter, 
  Download,
  RefreshCw,
  Package,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface EbayColumn {
  code: string;
  display_name: string;
  type: string;
  required: boolean;
  description?: string;
}

interface FilterState {
  search: string;
  requiredFilter: 'all' | 'required' | 'optional';
}

export function EbayColumnSettings() {
  const [columns, setColumns] = useState<EbayColumn[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    requiredFilter: 'all'
  });

  // Fetch eBay column data
  const fetchColumnData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/ebay/columns');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.attributes) {
        setColumns(result.attributes);
        toast.success(`Loaded ${result.attributes.length} eBay columns`);
      } else {
        throw new Error(result.message || 'Failed to load eBay columns');
      }
    } catch (error) {
      console.error('Failed to fetch eBay columns:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      toast.error('Failed to load eBay columns');
    } finally {
      setLoading(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    fetchColumnData();
  }, []);

  // Filter columns based on search and filters
  const filteredColumns = columns.filter(col => {
    const matchesSearch = !filters.search || 
      col.code.toLowerCase().includes(filters.search.toLowerCase()) ||
      col.display_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      (col.description && col.description.toLowerCase().includes(filters.search.toLowerCase()));
    
    const matchesRequired = filters.requiredFilter === 'all' ||
      (filters.requiredFilter === 'required' && col.required) ||
      (filters.requiredFilter === 'optional' && !col.required);
    
    return matchesSearch && matchesRequired;
  });

  const handleRefresh = () => {
    fetchColumnData();
  };

  const exportData = () => {
    const dataStr = JSON.stringify(filteredColumns, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'ebay-columns.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success('eBay column data exported');
  };

  const requiredCount = columns.filter(col => col.required).length;
  const optionalCount = columns.filter(col => !col.required).length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>eBay Column Settings</span>
              </CardTitle>
              <CardDescription>
                Configure and manage eBay-specific columns for your imports
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={exportData} size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button 
                variant="outline" 
                onClick={handleRefresh} 
                disabled={loading}
                size="sm"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{columns.length}</div>
              <div className="text-sm text-blue-600">Total Columns</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{requiredCount}</div>
              <div className="text-sm text-red-600">Required Columns</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{optionalCount}</div>
              <div className="text-sm text-gray-600">Optional Columns</div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search columns..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>
            <Select 
              value={filters.requiredFilter} 
              onValueChange={(value) => setFilters(prev => ({ ...prev, requiredFilter: value as any }))}
            >
              <SelectTrigger className="w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Columns</SelectItem>
                <SelectItem value="required">Required Only</SelectItem>
                <SelectItem value="optional">Optional Only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Results count */}
          <div className="text-sm text-gray-600">
            Showing {filteredColumns.length} of {columns.length} columns
          </div>

          {/* Error state */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <span className="text-red-600 font-medium">Error loading eBay columns</span>
              </div>
              <div className="text-red-600 text-sm mt-1">{error}</div>
            </div>
          )}

          {/* Loading state */}
          {loading && (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
              <div className="text-gray-600">Loading eBay columns...</div>
            </div>
          )}

          {/* Table */}
          {!loading && !error && (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Column Code</TableHead>
                    <TableHead>Display Name</TableHead>
                    <TableHead>Required</TableHead>
                    <TableHead>Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredColumns.map((column) => (
                    <TableRow key={column.code}>
                      <TableCell className="font-medium">
                        {column.code}
                      </TableCell>
                      <TableCell>
                        {column.display_name}
                      </TableCell>
                      <TableCell>
                        {column.required ? (
                          <Badge variant="destructive" className="text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Required
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="text-xs">
                            <XCircle className="h-3 w-3 mr-1" />
                            Optional
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {column.description || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 