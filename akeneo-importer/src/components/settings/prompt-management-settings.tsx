'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import {
  Settings,
  Save,
  RefreshCw,
  Eye,
  Copy,
  AlertCircle,
  Info,
  MessageSquare
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

interface PromptManagementState {
  defaultPrompt: string;
  originalPrompt: string;
  systemPrompt: string;
  originalSystemPrompt: string;
  loading: boolean;
  saving: boolean;
  hasChanges: boolean;
  hasSystemChanges: boolean;
  previewData: any;
}

export function PromptManagementSettings() {
  const [state, setState] = useState<PromptManagementState>({
    defaultPrompt: '',
    originalPrompt: '',
    systemPrompt: '',
    originalSystemPrompt: '',
    loading: true,
    saving: false,
    hasChanges: false,
    hasSystemChanges: false,
    previewData: null
  });

  useEffect(() => {
    loadDefaultPrompt();
    loadSystemPrompt();
  }, []);

  const loadDefaultPrompt = async () => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const response = await fetch('/api/settings/prompt/default');
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({
          ...prev,
          defaultPrompt: data.default_prompt,
          originalPrompt: data.default_prompt,
          hasChanges: false
        }));
      } else {
        toast.error('Failed to load default prompt: ' + data.message);
      }
    } catch (error) {
      console.error('Error loading default prompt:', error);
      toast.error('Failed to load default prompt');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  const saveDefaultPrompt = async () => {
    setState(prev => ({ ...prev, saving: true }));
    try {
      const response = await fetch('/api/settings/prompt/default', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: state.defaultPrompt })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({
          ...prev,
          originalPrompt: state.defaultPrompt,
          hasChanges: false
        }));
        toast.success('Default prompt saved successfully');
      } else {
        toast.error('Failed to save prompt: ' + data.message);
      }
    } catch (error) {
      console.error('Error saving default prompt:', error);
      toast.error('Failed to save default prompt');
    } finally {
      setState(prev => ({ ...prev, saving: false }));
    }
  };

  const resetPrompt = () => {
    setState(prev => ({
      ...prev,
      defaultPrompt: prev.originalPrompt,
      hasChanges: false
    }));
  };

  const handlePromptChange = (value: string) => {
    setState(prev => ({
      ...prev,
      defaultPrompt: value,
      hasChanges: value !== prev.originalPrompt
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const generatePreview = () => {
    // Preview functionality disabled - requires real row data
    toast.error('Preview functionality requires real row data from an active import job');
  };

  const loadSystemPrompt = async () => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const response = await fetch('/api/settings/prompt/system');
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({
          ...prev,
          systemPrompt: data.system_prompt,
          originalSystemPrompt: data.system_prompt,
          hasSystemChanges: false
        }));
      } else {
        toast.error('Failed to load system prompt: ' + data.message);
      }
    } catch (error) {
      console.error('Error loading system prompt:', error);
      toast.error('Failed to load system prompt');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  const saveSystemPrompt = async () => {
    setState(prev => ({ ...prev, saving: true }));
    try {
      const response = await fetch('/api/settings/prompt/system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: state.systemPrompt })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({
          ...prev,
          originalSystemPrompt: state.systemPrompt,
          hasSystemChanges: false
        }));
        toast.success('System prompt saved successfully');
      } else {
        toast.error('Failed to save system prompt: ' + data.message);
      }
    } catch (error) {
      console.error('Error saving system prompt:', error);
      toast.error('Failed to save system prompt');
    } finally {
      setState(prev => ({ ...prev, saving: false }));
    }
  };

  const resetSystemPrompt = () => {
    setState(prev => ({
      ...prev,
      systemPrompt: prev.originalSystemPrompt,
      hasSystemChanges: false
    }));
  };

  const handleSystemPromptChange = (value: string) => {
    setState(prev => ({
      ...prev,
      systemPrompt: value,
      hasSystemChanges: value !== prev.originalSystemPrompt
    }));
  };

  return (
    <div className="space-y-6">
      {/* System Prompt Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-600" />
            System Prompt
          </CardTitle>
          <CardDescription>
            Configure the system prompt that provides context to the AI about its role and behavior.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {state.loading ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Loading...</AlertTitle>
              <AlertDescription>Loading system prompt settings.</AlertDescription>
            </Alert>
          ) : (
            <>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="system-prompt" className="text-sm font-medium">
                    System Prompt
                  </Label>
                  <p className="text-xs text-muted-foreground mt-1">
                    This prompt defines the AI's role and behavioral context. Default: "You are a senior ecommerce product data manager."
                  </p>
                </div>
                
                <Textarea
                  id="system-prompt"
                  value={state.systemPrompt}
                  onChange={(e) => handleSystemPromptChange(e.target.value)}
                  className="min-h-[100px] resize-y font-mono text-sm"
                  placeholder="Enter your system prompt here..."
                />
                
                <div className="text-xs text-muted-foreground">
                  Character count: {state.systemPrompt.length}
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={saveSystemPrompt}
                  disabled={state.saving || !state.hasSystemChanges}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {state.saving ? 'Saving...' : 'Save System Prompt'}
                </Button>
                <Button
                  variant="outline"
                  onClick={resetSystemPrompt}
                  disabled={!state.hasSystemChanges}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(state.systemPrompt)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Default Prompt Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Default AI Prompt Template
          </CardTitle>
          <CardDescription>
            Configure the default prompt template used for AI transformations.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {state.loading ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Loading...</AlertTitle>
              <AlertDescription>Loading current prompt template settings.</AlertDescription>
            </Alert>
          ) : (
            <>
              <div className="space-y-4">
                {/* Prompt Editor */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="default-prompt" className="text-sm font-medium">
                      Default Prompt Template
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      This template will be used as the base for all AI transformations. You can override it per column in the Column Prompts tab.
                    </p>
                  </div>
                  
                  <Textarea
                    id="default-prompt"
                    value={state.defaultPrompt}
                    onChange={(e) => handlePromptChange(e.target.value)}
                    className="min-h-[300px] resize-y font-mono text-sm"
                    placeholder="Enter your default AI prompt template here..."
                  />
                  
                  <div className="text-xs text-muted-foreground">
                    Character count: {state.defaultPrompt.length}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-2">
                  <Button
                    onClick={saveDefaultPrompt}
                    disabled={state.saving || !state.hasChanges}
                    className="flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    {state.saving ? 'Saving...' : 'Save'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={resetPrompt}
                    disabled={!state.hasChanges}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                  <Button
                    variant="outline"
                    onClick={generatePreview}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => copyToClipboard(state.defaultPrompt)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
              </div>
            </>
          )}

          {/* Preview Section */}
          {state.previewData && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Preview with Sample Data</AlertTitle>
              <AlertDescription>
                <div 
                  className="mt-2 p-3 bg-muted rounded text-sm font-mono whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: state.previewData.renderedPrompt || 'No preview available'
                  }}
                />
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Custom Mapping Prompt Templates */}
      <CustomMappingPromptTemplates />
    </div>
  );
}

interface CustomMappingPromptTemplatesState {
  loading: boolean;
  saving: boolean;
  columnTemplates: Array<{
    column_name: string;
    type: string;
    default_mapping_content: string | null;
    required: boolean;
  }>;
  selectedColumn: string | null;
  currentTemplate: string;
  hasChanges: boolean;
}

function CustomMappingPromptTemplates() {
  const [state, setState] = useState<CustomMappingPromptTemplatesState>({
    loading: true,
    saving: false,
    columnTemplates: [],
    selectedColumn: null,
    currentTemplate: '',
    hasChanges: false
  });

  const loadColumnTemplates = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await fetch('/api/unified-config');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data?.columns) {
          // Filter columns that have custom mapping templates
          const columnsWithTemplates = result.data.columns.filter((col: any) => 
            col.default_mapping_content && col.default_mapping_content.trim()
          );
          
          setState(prev => ({
            ...prev,
            columnTemplates: columnsWithTemplates,
            loading: false
          }));
        }
      }
    } catch (error) {
      console.error('Failed to load column templates:', error);
      toast.error('Failed to load custom mapping prompt templates');
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  const handleColumnSelect = (columnName: string) => {
    const column = state.columnTemplates.find(col => col.column_name === columnName);
    if (column) {
      setState(prev => ({
        ...prev,
        selectedColumn: columnName,
        currentTemplate: column.default_mapping_content || '',
        hasChanges: false
      }));
    }
  };

  const handleTemplateChange = (value: string) => {
    setState(prev => ({
      ...prev,
      currentTemplate: value,
      hasChanges: true
    }));
  };

  const saveTemplate = async () => {
    if (!state.selectedColumn) return;
    
    setState(prev => ({ ...prev, saving: true }));
    
    try {
      // For now, we'll just show a message since direct Google Sheets editing
      // would require additional setup. In a real implementation, you'd
      // save this back to Google Sheets or store in Redis as an override.
      toast.success(`Custom prompt template updated for ${state.selectedColumn}`, {
        description: 'Note: Changes are stored locally. Update your Google Sheets configuration to persist changes.'
      });
      
      setState(prev => ({
        ...prev,
        hasChanges: false,
        saving: false
      }));
    } catch (error) {
      console.error('Failed to save template:', error);
      toast.error('Failed to save custom prompt template');
      setState(prev => ({ ...prev, saving: false }));
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Template copied to clipboard');
  };

  useEffect(() => {
    loadColumnTemplates();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-purple-600" />
          Custom Mapping Prompt Templates
        </CardTitle>
        <CardDescription>
          Manage column-specific prompt templates that override the default template. These are loaded from your Google Sheets configuration.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {state.loading ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Loading...</AlertTitle>
            <AlertDescription>Loading custom mapping prompt templates from configuration.</AlertDescription>
          </Alert>
        ) : state.columnTemplates.length === 0 ? (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>No Custom Templates Found</AlertTitle>
            <AlertDescription>
              No columns have custom mapping prompt templates defined. Add content to the "Custom_Mapping_Prompt_Template" column in your Google Sheets configuration to create custom templates.
            </AlertDescription>
          </Alert>
        ) : (
          <>
            <div className="space-y-4">
              {/* Column Selection */}
              <div>
                <Label className="text-sm font-medium">Select Column</Label>
                <p className="text-xs text-muted-foreground mt-1">
                  Choose a column to view and edit its custom prompt template.
                </p>
                <Select 
                  value={state.selectedColumn || ""} 
                  onValueChange={handleColumnSelect}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Select a column with custom template..." />
                  </SelectTrigger>
                  <SelectContent>
                    {state.columnTemplates.map((col) => (
                      <SelectItem key={col.column_name} value={col.column_name}>
                        <div className="flex items-center gap-2">
                          <Badge variant={col.type === 'Navisionvorlage' ? 'secondary' : 'outline'} className="text-xs">
                            {col.type === 'Navisionvorlage' ? 'ERP' : 'PIM'}
                          </Badge>
                          <span>{col.column_name}</span>
                          {col.required && <Badge variant="destructive" className="text-xs">Required</Badge>}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Template Editor */}
              {state.selectedColumn && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="custom-template" className="text-sm font-medium">
                      Custom Prompt Template for {state.selectedColumn}
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      This template will override the default prompt template when processing the "{state.selectedColumn}" column.
                    </p>
                  </div>
                  
                  <Textarea
                    id="custom-template"
                    value={state.currentTemplate}
                    onChange={(e) => handleTemplateChange(e.target.value)}
                    className="min-h-[200px] resize-y font-mono text-sm"
                    placeholder="Enter custom prompt template for this column..."
                  />
                  
                  <div className="text-xs text-muted-foreground">
                    Character count: {state.currentTemplate.length}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={saveTemplate}
                      disabled={state.saving || !state.hasChanges}
                      className="flex items-center gap-2"
                    >
                      <Save className="h-4 w-4" />
                      {state.saving ? 'Saving...' : 'Save Template'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => copyToClipboard(state.currentTemplate)}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                    <Button
                      variant="outline"
                      onClick={loadColumnTemplates}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}