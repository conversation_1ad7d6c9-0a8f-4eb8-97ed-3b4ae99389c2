'use client';

import { useState, useEffect, useMemo, useRef } from 'react';
import { useLLMDefaults } from '@/hooks/use-llm-defaults';
import { LLM_CONSTANTS } from '@/lib/constants/llm-constants';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import {
  Bot,
  Search,
  RefreshCw,
  Play,
  AlertCircle,
  CheckCircle,
  XCircle,
  Settings,
  Zap,
  DollarSign,
  Wrench,
  Layers,
  FileJson,
  PowerOff
} from 'lucide-react';
import {
  OpenRouterModel,
  GroqModel,
  LLMModelWithStatus,
  ModelManagementState,
  LLMProviderStatus,
  <PERSON><PERSON>rov<PERSON>
} from '@/types';
import { UsageStatsDisplay } from './usage-stats-display';
import pricingService from '@/lib/pricing/pricing-service';

interface LocalModelManagementState {
  availableModels: (OpenRouterModel | GroqModel)[];
  enabledModels: string[];
  defaultModel: string;
  defaultTemperature: number;
  loading: boolean;
  searchTerm: string;
  currentProvider: LLMProvider;
}

export function ModelManagementSettings() {
  const { defaults: llmDefaults, updateTemperature } = useLLMDefaults();
  
  const [state, setState] = useState<LocalModelManagementState>({
    availableModels: [],
    enabledModels: [],
    defaultModel: '',
    defaultTemperature: llmDefaults.temperature || 1, // Use hook or fallback
    loading: false,
    searchTerm: '',
    currentProvider: 'groq' // Default to Groq as requested
  });
  const [status, setStatus] = useState<LLMProviderStatus | null>(null);
  
  // Add refs to prevent duplicate API calls during bulk operations
  const modelsLoadingRef = useRef<boolean>(false);
  const previousProviderRef = useRef<string>('');

  // Sync with LLM defaults hook
  useEffect(() => {
    if (!llmDefaults.loading) {
      setState(prev => ({
        ...prev,
        defaultTemperature: llmDefaults.temperature,
        currentProvider: llmDefaults.provider
      }));
    }
  }, [llmDefaults.loading, llmDefaults.temperature, llmDefaults.provider]);

  // Load initial data
  useEffect(() => {
    loadCurrentProvider();
  }, []);

  // Remove the problematic useEffect that was causing the infinite loop
  // Model loading is now handled only in loadCurrentProvider function

  const filteredModels = useMemo(() => {
    return state.availableModels.filter(model =>
      model.name.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
      model.id.toLowerCase().includes(state.searchTerm.toLowerCase())
    );
  }, [state.availableModels, state.searchTerm]);

  const loadCurrentProvider = async () => {
    try {
      const response = await fetch('/api/models/provider');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.provider !== state.currentProvider) {
        console.log(`🔄 Loading initial provider: ${data.provider}`);
        setState(prev => ({
          ...prev,
          currentProvider: data.provider
        }));
        // Trigger initial load of models for this provider
        setTimeout(() => {
          loadStatus();
          loadEnabledModels();
          loadDefaultTemperature();
          loadAvailableModels();
        }, 0);
      }
    } catch (error) {
      console.error('Error loading current provider:', error);
      // Keep default provider if loading fails
    }
  };

  const setCurrentProvider = async (provider: LLMProvider) => {
    if (provider === state.currentProvider) {
      console.log('⏭️ Provider already set to', provider);
      return;
    }
    
    try {
      const response = await fetch('/api/models/provider', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      if (data.success) {
        console.log(`🔄 Switching provider to: ${provider}`);
        setState(prev => ({
          ...prev,
          currentProvider: provider,
          availableModels: [],
          enabledModels: [],
          defaultModel: ''
        }));
        
        // Manually trigger model loading for new provider
        setTimeout(() => {
          loadStatus();
          loadEnabledModels();
          loadDefaultTemperature();
          loadAvailableModels();
        }, 0);
        
        toast.success(`Switched to ${provider === 'groq' ? 'Groq' : 'OpenRouter'} provider`);
      }
    } catch (error) {
      console.error('Error setting provider:', error);
      toast.error('Failed to switch provider');
    }
  };

  const loadStatus = async () => {
    try {
      const response = await fetch(`/api/models/status?provider=${state.currentProvider}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setStatus(data.status);
      } else {
        const errorStatus: LLMProviderStatus = { 
          configured: false, 
          api_key_present: false, 
          base_url: state.currentProvider === 'groq' ? 'https://api.groq.com/openai/v1' : 'https://openrouter.ai/api/v1', 
          connectivity: false, 
          provider: state.currentProvider,
          error: data.message || 'Unknown error'
        };
        setStatus(errorStatus);
      }
    } catch (error) {
      console.error('Error loading status:', error);
      const errorStatus: LLMProviderStatus = { 
        configured: false, 
        api_key_present: false, 
        base_url: state.currentProvider === 'groq' ? 'https://api.groq.com/openai/v1' : 'https://openrouter.ai/api/v1', 
        connectivity: false, 
        provider: state.currentProvider,
        error: error instanceof Error ? error.message : 'Failed to load status'
      };
      setStatus(errorStatus);
    }
  };

  const loadEnabledModels = async () => {
    try {
      const response = await fetch(`/api/models/enabled?provider=${state.currentProvider}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({
          ...prev,
          enabledModels: data.enabled_models.map((m: any) => m.id),
          defaultModel: data.default_model
        }));
      }
    } catch (error) {
      console.error('Error loading enabled models:', error);
      toast.error('Failed to load enabled models');
    }
  };

  const loadDefaultTemperature = async () => {
    try {
      const response = await fetch(`/api/models/temperature?provider=${state.currentProvider}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({
          ...prev,
          defaultTemperature: data.default_temperature
        }));
      }
    } catch (error) {
      console.error('Error loading default temperature:', error);
      // Don't show error toast for temperature as it's not critical
    }
  };

  const setDefaultTemperature = async (temperature: number) => {
    const result = await updateTemperature(temperature);
    if (result.success) {
      setState(prev => ({
        ...prev,
        defaultTemperature: temperature
      }));
      toast.success(`Default temperature set to ${temperature}`);
    } else {
      toast.error(result.error || 'Failed to set default temperature');
    }
  };

  const loadAvailableModels = async (forceRefresh = false) => {
    // Prevent duplicate calls during bulk operations unless forced refresh
    if (modelsLoadingRef.current && !forceRefresh) {
      console.log('⏭️ Skipping model loading - already in progress');
      return;
    }

    modelsLoadingRef.current = true;
    setState(prev => ({ ...prev, loading: true }));
    try {
      const url = forceRefresh 
        ? `/api/models/available?refresh=true&provider=${state.currentProvider}` 
        : `/api/models/available?provider=${state.currentProvider}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({ ...prev, availableModels: data.models }));
        if (forceRefresh) {
          const providerName = state.currentProvider === 'groq' ? 'Groq' : 'OpenRouter';
          toast.success(`Refreshed ${data.models.length} models from ${providerName}`);
        }
      } else {
        // Show specific error message from the API
        const errorMsg = data.message || 'Unknown error occurred';
        toast.error(`Failed to load available models: ${errorMsg}`);
        
        // If it's an API key issue, provide helpful guidance
        if (errorMsg.includes('API key') || errorMsg.includes('GROQ_API_KEY')) {
          toast.error(
            `Please check your ${state.currentProvider.toUpperCase()}_API_KEY environment variable`,
            { duration: 6000 }
          );
        }
      }
    } catch (error) {
      console.error('Error loading available models:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to load available models: ${errorMessage}`);
      
      // Clear models on error so user knows there's an issue
      setState(prev => ({ ...prev, availableModels: [] }));
    } finally {
      setState(prev => ({ ...prev, loading: false }));
      modelsLoadingRef.current = false;
    }
  };

  const handleModelToggle = async (modelId: string) => {
    const newEnabledModels = state.enabledModels.includes(modelId)
      ? state.enabledModels.filter(id => id !== modelId)
      : [...state.enabledModels, modelId];
    
    try {
      console.log('🔄 Toggling model:', modelId, 'New enabled models:', newEnabledModels);
      
      const response = await fetch('/api/models/enabled', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          model_ids: newEnabledModels,
          provider: state.currentProvider
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('❌ API Error Response:', errorData);
        throw new Error(`HTTP ${response.status}: ${errorData.message || response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ Model toggle response:', data);
      
      if (data.success) {
        // Update state with the actual enabled models from the response
        const actualEnabledModels = data.enabled_models || newEnabledModels;
        setState(prev => ({ ...prev, enabledModels: actualEnabledModels }));
        
        const action = actualEnabledModels.includes(modelId) ? 'enabled' : 'disabled';
        toast.success(`Model ${action} successfully`);
        
        // Show warning if some models were skipped
        if (data.warning) {
          toast.warning(data.warning);
        }
      } else {
        console.error('❌ API returned success=false:', data);
        toast.error('Failed to update model: ' + (data.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('❌ Error toggling model:', error);
      
      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch')) {
          toast.error('Network error: Unable to connect to the server. Please check your connection.');
        } else if (error.message.includes('HTTP 400')) {
          toast.error('Invalid request: The model ID may not be valid for the current provider.');
        } else if (error.message.includes('HTTP 500')) {
          toast.error('Server error: Please try again in a moment.');
        } else {
          toast.error('Failed to update model: ' + error.message);
        }
      } else {
        toast.error('Failed to update model: Unknown error occurred');
      }
    }
  };

  const handleSetDefault = async (modelId: string) => {
    if (!state.enabledModels.includes(modelId)) {
      toast.error('Model must be enabled before setting as default');
      return;
    }

    try {
      const response = await fetch('/api/models/default', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          model_id: modelId,
          provider: state.currentProvider
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      if (data.success) {
        setState(prev => ({ ...prev, defaultModel: modelId }));
        toast.success(`Set ${modelId} as default model`);
      } else {
        toast.error('Failed to set default model: ' + data.message);
      }
    } catch (error) {
      console.error('Error setting default model:', error);
      toast.error('Failed to set default model');
    }
  };

  const handleTestModel = async (modelId: string) => {
    try {
      const response = await fetch('/api/models/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          model_id: modelId,
          provider: state.currentProvider
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      if (data.success) {
        toast.success(`Model ${modelId} test successful`);
      } else {
        toast.error(`Model test failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Error testing model:', error);
      toast.error('Failed to test model');
    }
  };

  const enableRecommendedModels = async () => {
    // Prioritize qwen/qwen3-32b for Groq as the primary thinking model
    const recommendedPatterns = state.currentProvider === 'groq' 
      ? [
          // Primary Qwen thinking model - always first priority
          /^qwen\/qwen3-32b$/i,
          
          // Other Qwen models
          /^qwen/i,
          
          // Other high-quality models (optional)
          /^deepseek.*r1/i,
          /^meta-llama\/llama-4.*instruct/i,
          /^llama-3\.3.*versatile/i,
        ]
      : [
          /^qwen/i,                // qwen models
          /^deepseek/i,            // deepseek models  
          /^gpt-4o/i,              // gpt-4o models
          /^claude/i               // claude models
        ];
    
    const availableRecommended = state.availableModels.filter(model => 
      recommendedPatterns.some(pattern => pattern.test(model.id)) &&
      !model.id.toLowerCase().includes('guard') // Exclude Guard models as requested
    );
    
    if (availableRecommended.length === 0) {
      toast.error('No recommended models found in available models. Please check your API connection and refresh the model list.');
      return;
    }
    
    // For Groq, prioritize qwen/qwen3-32b if available, otherwise use first available
    let recommendedIds = availableRecommended.map(model => model.id);
    
    // Sort to ensure qwen/qwen3-32b comes first for Groq
    if (state.currentProvider === 'groq') {
      recommendedIds = recommendedIds.sort((a, b) => {
        if (a === 'qwen/qwen3-32b') return -1;
        if (b === 'qwen/qwen3-32b') return 1;
        return 0;
      });
    }
    
    try {
      const response = await fetch('/api/models/enabled', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          model_ids: recommendedIds,
          provider: state.currentProvider 
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      if (data.success) {
        setState(prev => ({ ...prev, enabledModels: recommendedIds }));
        
        // Set qwen/qwen3-32b as default for Groq, or first recommended model for others
        const preferredDefault = state.currentProvider === 'groq' && recommendedIds.includes('qwen/qwen3-32b') 
          ? 'qwen/qwen3-32b' 
          : recommendedIds[0];
          
        if (preferredDefault) {
          await handleSetDefault(preferredDefault);
        }
        
        toast.success(`Enabled ${recommendedIds.length} recommended models${state.currentProvider === 'groq' ? ' with qwen/qwen3-32b as primary' : ''}`);
      }
    } catch (error) {
      console.error('Error enabling recommended models:', error);
      toast.error('Failed to enable recommended models');
    }
  };

  const clearAllModels = async () => {
    if (state.enabledModels.length === 0) {
      toast.info('No models are currently enabled');
      return;
    }

    try {
      const response = await fetch('/api/models/enabled', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          model_ids: [],
          provider: state.currentProvider
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      if (data.success) {
        setState(prev => ({ ...prev, enabledModels: [], defaultModel: '' }));
        toast.success(`Disabled all ${state.currentProvider === 'groq' ? 'Groq' : 'OpenRouter'} models`);
      }
    } catch (error) {
      console.error('Error clearing models:', error);
      toast.error('Failed to disable all models');
    }
  };

  const getStatusIcon = () => {
    if (!status) return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    if (!status.configured || !status.api_key_present) return <XCircle className="h-4 w-4 text-destructive" />;
    if (!status.connectivity) return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (!status) return 'Loading...';
    if (!status.api_key_present) return `${status.provider === 'groq' ? 'GROQ_API_KEY' : 'OPENROUTER_API_KEY'} not configured`;
    if (!status.connectivity) return 'API connection failed';
    return `Connected (${status.models_count || 0} models available)`;
  };

  const getProviderIcon = (provider: LLMProvider) => {
    return provider === 'groq' ? <Zap className="h-4 w-4" /> : <Settings className="h-4 w-4" />;
  };

  const isOpenRouterModel = (model: OpenRouterModel | GroqModel): model is OpenRouterModel => {
    return 'context_length' in model && 'pricing' in model;
  };

  const isGroqModel = (model: OpenRouterModel | GroqModel): model is GroqModel => {
    return 'context_window' in model && 'object' in model;
  };

  return (
    <div className="space-y-6">
      {/* Provider Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            LLM Provider Selection
          </CardTitle>
          <CardDescription>
            Choose between OpenRouter and Groq for AI model access
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              variant={state.currentProvider === 'groq' ? 'default' : 'outline'}
              onClick={() => setCurrentProvider('groq')}
              className="flex items-center gap-2"
            >
              <Zap className="h-4 w-4" />
              Groq (Premium)
              {state.currentProvider === 'groq' && (
                <Badge variant="secondary" className="ml-2">Active</Badge>
              )}
            </Button>
            <Button
              variant={state.currentProvider === 'openrouter' ? 'default' : 'outline'}
              onClick={() => setCurrentProvider('openrouter')}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              OpenRouter
              {state.currentProvider === 'openrouter' && (
                <Badge variant="secondary" className="ml-2">Active</Badge>
              )}
            </Button>
          </div>
          
          {/* Status Display */}
          <Alert className={status?.connectivity ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}>
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <AlertTitle className="capitalize">
                {state.currentProvider === 'groq' ? 'Groq' : 'OpenRouter'} Status
              </AlertTitle>
            </div>
            <AlertDescription className="mt-2">
              {getStatusText()}
              {status?.error && (
                <div className="mt-1 text-sm text-destructive">
                  Error: {status.error}
                </div>
              )}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      {status?.configured && status?.api_key_present && status?.connectivity && (
        <UsageStatsDisplay provider={state.currentProvider} />
      )}

      {/* Temperature Configuration */}
      {status?.configured && status?.api_key_present && status?.connectivity && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Default Temperature
            </CardTitle>
            <CardDescription>
              Set the default temperature for {state.currentProvider === 'groq' ? 'Groq' : 'OpenRouter'} AI model responses
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="temperature" className="text-sm font-medium">
                  Temperature: {state.defaultTemperature}
                </Label>
                <Badge variant="outline" className="text-xs">
                  {state.defaultTemperature < 0.3 ? 'Focused' : 
                   state.defaultTemperature < 0.8 ? 'Balanced' : 'Creative'}
                </Badge>
              </div>
              <div className="flex items-center space-x-3">
                <input
                  type="range"
                  id="temperature"
                  min={LLM_CONSTANTS.TEMPERATURE.MIN}
                  max={LLM_CONSTANTS.TEMPERATURE.MAX}
                  step={LLM_CONSTANTS.TEMPERATURE.STEP}
                  value={state.defaultTemperature}
                  onChange={(e) => {
                    const newTemp = parseFloat(e.target.value);
                    setState(prev => ({ ...prev, defaultTemperature: newTemp }));
                  }}
                  onMouseUp={(e) => {
                    const newTemp = parseFloat((e.target as HTMLInputElement).value);
                    setDefaultTemperature(newTemp);
                  }}
                  className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <Input
                  type="number"
                  min={LLM_CONSTANTS.TEMPERATURE.MIN}
                  max={LLM_CONSTANTS.TEMPERATURE.MAX}
                  step={LLM_CONSTANTS.TEMPERATURE.STEP}
                  value={state.defaultTemperature}
                  onChange={(e) => {
                    const newTemp = Math.min(LLM_CONSTANTS.TEMPERATURE.MAX, Math.max(LLM_CONSTANTS.TEMPERATURE.MIN, parseFloat(e.target.value) || 0));
                    setState(prev => ({ ...prev, defaultTemperature: newTemp }));
                  }}
                  onBlur={(e) => {
                    const newTemp = Math.min(LLM_CONSTANTS.TEMPERATURE.MAX, Math.max(LLM_CONSTANTS.TEMPERATURE.MIN, parseFloat(e.target.value) || 0));
                    setDefaultTemperature(newTemp);
                  }}
                  className="w-20 text-sm"
                />
              </div>
              <div className="text-xs text-muted-foreground">
                Controls randomness: 0 = focused and deterministic, 2 = creative and random
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Model Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getProviderIcon(state.currentProvider)}
              {state.currentProvider === 'groq' ? 'Groq' : 'OpenRouter'} Model Management
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadAvailableModels(true)}
                disabled={state.loading}
              >
                <RefreshCw className={`h-4 w-4 ${state.loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={enableRecommendedModels}
              >
                Enable Recommended
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={clearAllModels}
                disabled={state.loading || state.enabledModels.length === 0}
                title={state.enabledModels.length === 0 ? "No models are enabled" : "Disable all enabled models"}
              >
                <PowerOff className="h-4 w-4 mr-1" />
                Disable All
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Manage available models for the {state.currentProvider === 'groq' ? 'Groq' : 'OpenRouter'} provider
            {state.currentProvider === 'groq' && (
              <span className="block text-green-600 mt-1">
                ⚡ Groq provides ultra-fast inference with generous free tier
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search models..."
              value={state.searchTerm}
              onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
            />
          </div>

          {/* Model List */}
          {state.loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading models...</span>
            </div>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredModels.map((model) => {
                const isEnabled = state.enabledModels.includes(model.id);
                const isDefault = state.defaultModel === model.id;
                
                return (
                  <div
                    key={model.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{model.name}</h4>
                        {isDefault && (
                          <Badge variant="default" className="text-xs">
                            Default
                          </Badge>
                        )}
                        {isGroqModel(model) && (
                          <Badge variant="secondary" className="text-xs">
                            {model.context_window ? 
                              `${model.context_window.toLocaleString()} tokens` : 
                              'Context: Unknown'
                            }
                          </Badge>
                        )}
                        {isOpenRouterModel(model) && model.context_length && (
                          <Badge variant="secondary" className="text-xs">
                            {model.context_length.toLocaleString()} tokens
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {model.description || model.id}
                      </p>
                      {/* Show pricing and capabilities from config file */}
                      {(() => {
                        const pricing = pricingService.getModelPricing(model.id);
                        console.log(`🔍 Pricing check for ${model.id}:`, pricing); // Debug log
                        if (pricing) {
                          return (
                            <div className="space-y-2 mt-1">
                              {/* Pricing Info */}
                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                <div className="flex items-center gap-1">
                                  <DollarSign className="h-3 w-3" />
                                  <span>In: {pricingService.formatPrice(pricing.input_price_per_million)}/M</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <DollarSign className="h-3 w-3" />
                                  <span>Out: {pricingService.formatPrice(pricing.output_price_per_million)}/M</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Zap className="h-3 w-3" />
                                  <span>{pricing.speed_tokens_per_second} tok/s</span>
                                </div>
                              </div>

                              {/* Capabilities */}
                              {pricing.capabilities && (
                                <div className="flex items-center gap-3 text-xs">
                                  <div className={`flex items-center gap-1 ${pricing.capabilities.tool_use ? 'text-green-600' : 'text-gray-400'}`}>
                                    <Wrench className="h-3 w-3" />
                                    <span>Tools</span>
                                  </div>
                                  <div className={`flex items-center gap-1 ${pricing.capabilities.parallel_tool_use ? 'text-green-600' : 'text-gray-400'}`}>
                                    <Layers className="h-3 w-3" />
                                    <span>Parallel</span>
                                  </div>
                                  <div className={`flex items-center gap-1 ${pricing.capabilities.json_mode ? 'text-green-600' : 'text-gray-400'}`}>
                                    <FileJson className="h-3 w-3" />
                                    <span>JSON</span>
                                  </div>
                                </div>
                              )}
                            </div>
                          );
                        } else {
                          console.log(`❌ No pricing found for ${model.id}`); // Debug log
                        }
                        return null;
                      })()}
                      {/* Fallback to OpenRouter pricing if no config pricing */}
                      {!pricingService.getModelPricing(model.id) && isOpenRouterModel(model) && model.pricing && (
                        <p className="text-xs text-muted-foreground">
                          Pricing: ${model.pricing.prompt}/${model.pricing.completion}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleTestModel(model.id)}
                        disabled={state.loading}
                        title="Test model"
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                      {isEnabled && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSetDefault(model.id)}
                          disabled={isDefault}
                        >
                          Set Default
                        </Button>
                      )}
                      <Button
                        variant={isEnabled ? "destructive" : "default"}
                        size="sm"
                        onClick={() => handleModelToggle(model.id)}
                      >
                        {isEnabled ? 'Disable' : 'Enable'}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {filteredModels.length === 0 && !state.loading && (
            <div className="text-center py-8 text-muted-foreground">
              No models found
            </div>
          )}

          {/* Bulk Actions */}
          <div className="flex items-center gap-2 pt-4 border-t">
            <Button
              variant="destructive"
              onClick={clearAllModels}
              disabled={state.loading || state.enabledModels.length === 0}
              className="flex items-center gap-2"
            >
              <PowerOff className="h-4 w-4" />
              Disable All Models ({state.enabledModels.length})
            </Button>
            {state.enabledModels.length > 0 && (
              <span className="text-sm text-muted-foreground">
                This will disable all {state.enabledModels.length} enabled model{state.enabledModels.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 