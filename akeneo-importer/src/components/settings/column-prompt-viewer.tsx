'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { 
  RefreshCw,
  Eye,
  FileSpreadsheet,
  Database,
  AlertCircle,
  CheckCircle,
  Loader2,
  Search,
  Filter
} from 'lucide-react';

interface ConfigurationPrompt {
  columnName: string;
  prompt: string | null;
  source: 'excel' | 'google-sheets';
  type: 'Navisionvorlage' | 'Akeneo';
  required: boolean;
  hasPrompt: boolean;
}

export function ColumnPromptViewer() {
  const [prompts, setPrompts] = useState<ConfigurationPrompt[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'with-prompts' | 'without-prompts'>('all');
  const [configurationSource, setConfigurationSource] = useState<'excel' | 'google-sheets' | null>(null);
  const [lastRefresh, setLastRefresh] = useState<string | null>(null);

  useEffect(() => {
    loadConfigurationPrompts();
  }, []);

  const loadConfigurationPrompts = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/unified-config?showBoth=true');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      if (data.success) {
        const configPrompts: ConfigurationPrompt[] = data.data.columns.map((col: any) => ({
          columnName: col.column_name,
          prompt: col.prompt,
          source: data.data.source || 'excel',
          type: col.type,
          required: col.required,
          hasPrompt: !!col.prompt
        }));
        
        setPrompts(configPrompts);
        setConfigurationSource(data.data.source);
        setLastRefresh(data.data.timestamp);
      } else {
        throw new Error(data.message || 'Failed to load configuration prompts');
      }
    } catch (error) {
      console.error('Error loading configuration prompts:', error);
      toast.error('Failed to load configuration prompts', {
        description: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshConfiguration = async () => {
    setRefreshing(true);
    try {
      // First refresh the backend cache
      const refreshResponse = await fetch('/api/unified-config', {
        method: 'POST'
      });

      if (!refreshResponse.ok) {
        throw new Error('Failed to refresh configuration cache');
      }

      // Then reload the data
      await loadConfigurationPrompts();
      
      toast.success('Configuration refreshed successfully', {
        description: 'Prompts have been reloaded from the source file'
      });
    } catch (error) {
      console.error('Error refreshing configuration:', error);
      toast.error('Failed to refresh configuration', {
        description: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setRefreshing(false);
    }
  };

  const filteredPrompts = prompts.filter(prompt => {
    const matchesSearch = prompt.columnName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (prompt.prompt && prompt.prompt.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'with-prompts' && prompt.hasPrompt) ||
                         (filterType === 'without-prompts' && !prompt.hasPrompt);
    
    return matchesSearch && matchesFilter;
  });

  const promptStats = {
    total: prompts.length,
    withPrompts: prompts.filter(p => p.hasPrompt).length,
    withoutPrompts: prompts.filter(p => !p.hasPrompt).length,
    navision: prompts.filter(p => p.type === 'Navisionvorlage').length,
    akeneo: prompts.filter(p => p.type === 'Akeneo').length
  };

  const PromptViewDialog = ({ prompt }: { prompt: ConfigurationPrompt }) => (
    <DialogContent className="max-w-4xl">
      <DialogHeader>
        <DialogTitle className="flex items-center space-x-2">
          <Eye className="h-5 w-5" />
          <span>{prompt.columnName} - Configuration Prompt</span>
        </DialogTitle>
        <DialogDescription>
          Prompt defined in {configurationSource === 'excel' ? 'Excel configuration' : 'Google Sheets'}
        </DialogDescription>
      </DialogHeader>
      
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-600">Column Name:</label>
            <div className="font-mono bg-gray-50 p-2 rounded border">{prompt.columnName}</div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-600">Type:</label>
            <div className="font-mono bg-gray-50 p-2 rounded border">{prompt.type}</div>
          </div>
        </div>
        
        <div>
          <label className="text-sm font-medium text-gray-600 mb-2 block">
            Configured Prompt:
          </label>
          {prompt.prompt ? (
            <Textarea
              value={prompt.prompt}
              readOnly
              className="min-h-[200px] resize-y font-mono text-sm bg-gray-50"
            />
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No Prompt Configured</AlertTitle>
              <AlertDescription>
                This column does not have a prompt defined in the configuration file.
              </AlertDescription>
            </Alert>
          )}
        </div>
        
        <Alert>
          <FileSpreadsheet className="h-4 w-4" />
          <AlertTitle>Configuration Source</AlertTitle>
          <AlertDescription>
            To modify this prompt, edit the "{prompt.columnName}" row in the "Prompt" column of your {configurationSource === 'excel' ? 'Excel file' : 'Google Sheets'}, then click "Refresh Configuration" to reload.
          </AlertDescription>
        </Alert>
      </div>
    </DialogContent>
  );

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading Configuration Prompts</span>
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Column Prompts from Configuration</span>
            <Badge variant={configurationSource === 'excel' ? 'default' : 'secondary'}>
              {configurationSource === 'excel' ? 'Excel' : 'Google Sheets'}
            </Badge>
          </div>
          
          <Button 
            onClick={refreshConfiguration}
            disabled={refreshing}
            size="sm"
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-1" />
            )}
            Refresh Configuration
          </Button>
        </CardTitle>
        <CardDescription>
          View prompts defined in your configuration file. To modify prompts, edit the source file and refresh.
          {lastRefresh && (
            <span className="block text-xs text-gray-500 mt-1">
              Last refreshed: {new Date(lastRefresh).toLocaleString()}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{promptStats.total}</div>
            <div className="text-sm text-blue-700">Total Columns</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{promptStats.withPrompts}</div>
            <div className="text-sm text-green-700">With Prompts</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-600">{promptStats.withoutPrompts}</div>
            <div className="text-sm text-gray-700">Without Prompts</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{promptStats.navision}</div>
            <div className="text-sm text-purple-700">Navision</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{promptStats.akeneo}</div>
            <div className="text-sm text-orange-700">Akeneo</div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex space-x-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search columns or prompts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
            <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" style={{ marginTop: '12px', marginLeft: '12px', position: 'absolute' }} />
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="px-3 py-2 border rounded-md bg-white"
          >
            <option value="all">All Columns</option>
            <option value="with-prompts">With Prompts</option>
            <option value="without-prompts">Without Prompts</option>
          </select>
        </div>

        {/* Prompts List */}
        {filteredPrompts.length === 0 ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No Matching Columns</AlertTitle>
            <AlertDescription>
              {searchTerm ? 'No columns match your search criteria.' : 'No columns found in configuration.'}
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-3">
            {filteredPrompts.map((prompt) => (
              <Card key={prompt.columnName} className="border-2 border-gray-200 hover:border-blue-300 transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <h3 className="font-semibold text-lg">{prompt.columnName}</h3>
                      <Badge variant={prompt.type === 'Navisionvorlage' ? 'default' : 'secondary'}>
                        {prompt.type}
                      </Badge>
                      {prompt.required && (
                        <Badge variant="destructive" className="text-xs">Required</Badge>
                      )}
                      {prompt.hasPrompt ? (
                        <Badge variant="default" className="flex items-center space-x-1 bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3" />
                          <span>Has Prompt</span>
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="flex items-center space-x-1">
                          <AlertCircle className="h-3 w-3" />
                          <span>No Prompt</span>
                        </Badge>
                      )}
                    </div>
                    
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </DialogTrigger>
                      <PromptViewDialog prompt={prompt} />
                    </Dialog>
                  </div>
                  
                  {prompt.prompt && (
                    <div className="mt-3 text-sm text-gray-600 font-mono bg-gray-50 p-3 rounded border">
                      {prompt.prompt.length > 150 ? `${prompt.prompt.substring(0, 150)}...` : prompt.prompt}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 