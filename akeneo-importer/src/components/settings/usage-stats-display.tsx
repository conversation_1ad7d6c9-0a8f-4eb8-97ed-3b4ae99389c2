'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  DollarSign, 
  Zap, 
  Clock, 
  AlertCircle, 
  CheckCircle,
  ExternalLink,
  Info
} from 'lucide-react';
import { GroqUsageStats, OpenRouterUsageStats, LLMProvider } from '@/types';

interface UsageStatsDisplayProps {
  provider: LLMProvider;
}

export function UsageStatsDisplay({ provider }: UsageStatsDisplayProps) {
  const [groqStats, setGroqStats] = useState<GroqUsageStats | null>(null);
  const [openRouterStats, setOpenRouterStats] = useState<OpenRouterUsageStats | null>(null);
  const [loading, setLoading] = useState(false);

  const loadUsageStats = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/models/usage-stats?provider=${provider}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (provider === 'groq') {
        setGroqStats(data);
      } else if (provider === 'openrouter') {
        setOpenRouterStats(data);
      }
    } catch (error) {
      console.error('Error loading usage stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsageStats();
  }, [provider]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const renderGroqStats = () => {
    if (!groqStats) return null;

    if (!groqStats.success) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {groqStats.error || 'Failed to load Groq usage statistics'}
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <div className="space-y-4">
        {groqStats.message && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{groqStats.message}</span>
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => window.open('https://console.groq.com/settings/limits', '_blank')}
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                View Dashboard
              </Button>
            </AlertDescription>
          </Alert>
        )}


      </div>
    );
  };

  const renderOpenRouterStats = () => {
    if (!openRouterStats) return null;

    if (!openRouterStats.success) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {openRouterStats.error || 'Failed to load OpenRouter usage statistics'}
          </AlertDescription>
        </Alert>
      );
    }

    const credits = openRouterStats.credits;
    const keyInfo = openRouterStats.keyInfo;
    const remainingPercentage = credits ? (credits.remaining / credits.totalCredits) * 100 : 0;
    const usagePercentage = credits ? (credits.totalUsage / credits.totalCredits) * 100 : 0;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Credit Balance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {credits && (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Remaining:</span>
                      <span className="text-lg font-bold text-green-600">
                        {formatCurrency(credits.remaining)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ width: `${remainingPercentage}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="space-y-1 text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>Total Credits:</span>
                      <span>{formatCurrency(credits.totalCredits)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Used:</span>
                      <span>{formatCurrency(credits.totalUsage)}</span>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <Info className="h-4 w-4" />
                API Key Info
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {keyInfo && (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Key:</span>
                    <Badge variant="outline" className="text-xs">
                      {keyInfo.label}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Tier:</span>
                    <Badge variant={keyInfo.isFreeTier ? "secondary" : "default"}>
                      {keyInfo.isFreeTier ? 'Free' : 'Paid'}
                    </Badge>
                  </div>
                  
                  {keyInfo.limit && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Limit:</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(keyInfo.limit)}
                      </span>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://openrouter.ai/activity', '_blank')}
          >
            <ExternalLink className="h-3 w-3 mr-1" />
            View Activity
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://openrouter.ai/credits', '_blank')}
          >
            <DollarSign className="h-3 w-3 mr-1" />
            Add Credits
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base flex items-center gap-2">
              {provider === 'groq' ? (
                <>
                  <Zap className="h-4 w-4" />
                  Groq Usage Statistics
                </>
              ) : (
                <>
                  <DollarSign className="h-4 w-4" />
                  OpenRouter Usage & Balance
                </>
              )}
            </CardTitle>
            <CardDescription>
              {provider === 'groq' 
                ? 'Rate limits and service status'
                : 'Credit balance and usage information'
              }
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadUsageStats}
            disabled={loading}
          >
            <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Loading statistics...</span>
          </div>
        ) : (
          provider === 'groq' ? renderGroqStats() : renderOpenRouterStats()
        )}
      </CardContent>
    </Card>
  );
} 