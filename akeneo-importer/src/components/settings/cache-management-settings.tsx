'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { toast } from 'sonner';
import { 
  Database, 
  Trash2, 
  RefreshCw, 
  Activity, 
  Clock, 
  HardDrive,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Download 
} from 'lucide-react';
import { CacheHistoryTable } from './cache-history-table';

interface CacheStats {
  redis_info: {
    used_memory: number;
    used_memory_human: string;
    keyspace_hits: number;
    keyspace_misses: number;
    connected_clients: number;
    uptime_in_seconds: number;
  };
  cache_keys: {
    total: number;
    patterns: Record<string, number>;
  };
  service_info: {
    status: string;
    version?: string;
    uptime?: number;
  };
}

export function CacheManagementSettings() {
  const [stats, setStats] = useState<CacheStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [clearLoading, setClearLoading] = useState(false);
  const [clearPattern, setClearPattern] = useState('');
  const [serviceConnected, setServiceConnected] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [logDownloadRows, setLogDownloadRows] = useState<number>(100);
  const [downloadLoading, setDownloadLoading] = useState(false);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/cache/stats');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.stats);
        setServiceConnected(true);
        setLastRefresh(new Date());
      } else {
        console.error('Failed to fetch cache stats:', result.error);
        setServiceConnected(false);
        toast.error('Failed to fetch cache statistics');
      }
    } catch (error) {
      console.error('Error fetching cache stats:', error);
      setServiceConnected(false);
      toast.error('Cache service is not available');
    } finally {
      setLoading(false);
    }
  };

  const clearCache = async (pattern?: string) => {
    try {
      setClearLoading(true);
      const response = await fetch('/api/cache/clear', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pattern }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(`Cache cleared successfully. ${result.cleared_keys} keys removed.`);
        await fetchStats(); // Refresh stats
      } else {
        toast.error(`Failed to clear cache: ${result.error}`);
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
      toast.error('Failed to clear cache');
    } finally {
      setClearLoading(false);
    }
  };

  const downloadLogs = async () => {
    try {
      setDownloadLoading(true);
      
      const response = await fetch(`/api/debug/llm-logs/download?limit=${logDownloadRows}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to download logs');
      }

      // Get the filename from the response headers
      const contentDisposition = response.headers.get('Content-Disposition');
      const filenameMatch = contentDisposition?.match(/filename="([^"]+)"/);
      const filename = filenameMatch ? filenameMatch[1] : `llm-logs-${logDownloadRows}.json`;

      // Create a blob and download it
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Downloaded ${logDownloadRows} log entries successfully`);
    } catch (error) {
      console.error('Error downloading logs:', error);
      toast.error(`Failed to download logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setDownloadLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const getCacheHitRate = () => {
    if (!stats?.redis_info) return 0;
    const hits = stats.redis_info.keyspace_hits || 0;
    const misses = stats.redis_info.keyspace_misses || 0;
    const total = hits + misses;
    return total > 0 ? Math.round((hits / total) * 100) : 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size={32} />
        <span className="ml-2">Loading cache statistics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Service Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Cache Service Status
            {serviceConnected ? (
              <Badge variant="default" className="ml-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                Connected
              </Badge>
            ) : (
              <Badge variant="destructive" className="ml-2">
                <XCircle className="h-3 w-3 mr-1" />
                Disconnected
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                Last updated: {lastRefresh ? lastRefresh.toLocaleString() : 'Never'}
              </p>
              {!serviceConnected && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Cache service is not available. LLM requests will work but won't be cached.
                  </AlertDescription>
                </Alert>
              )}
            </div>
            <Button onClick={fetchStats} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {stats && serviceConnected && (
        <>
          {/* Overview Section */}
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Overview</h2>
              <p className="text-muted-foreground">Cache usage statistics and key distribution</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <HardDrive className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium">Memory Usage</p>
                      <p className="text-lg font-bold">{stats.redis_info?.used_memory_human || '0B'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Database className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="text-sm font-medium">Total Cache Keys</p>
                      <p className="text-lg font-bold">{stats.cache_keys?.total || 0}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-yellow-500" />
                    <div>
                      <p className="text-sm font-medium">Cache Hit Rate</p>
                      <p className="text-lg font-bold">{getCacheHitRate()}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-purple-500" />
                    <div>
                      <p className="text-sm font-medium">Uptime</p>
                      <p className="text-lg font-bold">{formatUptime(stats.redis_info?.uptime_in_seconds || 0)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Cache Key Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stats.cache_keys?.patterns && Object.entries(stats.cache_keys.patterns).length > 0 ? (
                    Object.entries(stats.cache_keys.patterns).map(([pattern, count]) => (
                      <div key={pattern} className="flex justify-between items-center">
                        <span className="text-sm font-mono">{pattern}</span>
                        <Badge variant="outline">{count} keys</Badge>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">No cache keys found</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Section */}
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Performance</h2>
              <p className="text-muted-foreground">Cache performance metrics and detailed Redis information</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Cache Performance</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Cache Hits:</span>
                    <span className="font-bold text-green-600">{(stats.redis_info?.keyspace_hits || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cache Misses:</span>
                    <span className="font-bold text-red-600">{(stats.redis_info?.keyspace_misses || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Hit Rate:</span>
                    <span className="font-bold">{getCacheHitRate()}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${getCacheHitRate()}%` }}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Redis Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Connected Clients:</span>
                    <span className="font-bold">{stats.redis_info?.connected_clients || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage:</span>
                    <span className="font-bold">{formatBytes(stats.redis_info?.used_memory || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Uptime:</span>
                    <span className="font-bold">{formatUptime(stats.redis_info?.uptime_in_seconds || 0)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Cache History Table */}
            <CacheHistoryTable limit={25} autoRefresh={true} />
          </div>

          {/* Management Section */}
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Management</h2>
              <p className="text-muted-foreground">Clear cache data and manage storage</p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Cache Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Clear All Cache</h4>
                    <p className="text-sm text-muted-foreground">
                      Remove all cached LLM responses. This will force fresh API calls for all future requests.
                    </p>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Clear All
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Clear All Cache</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <p>Are you sure you want to clear all cached data? This action cannot be undone.</p>
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" onClick={() => {}}>Cancel</Button>
                          <Button 
                            variant="destructive" 
                            onClick={() => clearCache()}
                            disabled={clearLoading}
                          >
                            {clearLoading ? <LoadingSpinner size={16} /> : 'Clear All Cache'}
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                <div className="border-t pt-4">
                  <div className="space-y-2">
                    <Label htmlFor="pattern">Clear Cache by Pattern</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="pattern"
                        placeholder="Enter pattern (e.g., *gpt-4o*)"
                        value={clearPattern}
                        onChange={(e) => setClearPattern(e.target.value)}
                      />
                      <Button 
                        onClick={() => clearCache(clearPattern)}
                        disabled={!clearPattern || clearLoading}
                        variant="outline"
                      >
                        {clearLoading ? <LoadingSpinner size={16} /> : 'Clear Pattern'}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Use wildcards (*) to match patterns. Example: *gpt-4o* will clear all GPT-4o related cache.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Log Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Download LLM Logs</h4>
                    <p className="text-sm text-muted-foreground">
                      Download the last X rows of LLM logs as a JSON file for analysis or debugging.
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logRows">Number of rows to download</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="logRows"
                      type="number"
                      placeholder="Enter number of rows"
                      value={logDownloadRows}
                      onChange={(e) => setLogDownloadRows(Math.max(1, parseInt(e.target.value) || 100))}
                      min="1"
                      max="10000"
                      className="w-48"
                    />
                    <Button 
                      onClick={downloadLogs}
                      disabled={downloadLoading || !logDownloadRows}
                      variant="outline"
                    >
                      {downloadLoading ? (
                        <LoadingSpinner size={16} />
                      ) : (
                        <Download className="h-4 w-4 mr-2" />
                      )}
                      Download Logs
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Downloads the most recent LLM log entries including requests, responses, timing, and metadata.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
} 