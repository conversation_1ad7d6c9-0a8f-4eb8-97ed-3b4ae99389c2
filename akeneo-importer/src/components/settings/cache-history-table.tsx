'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { toast } from 'sonner';
import { 
  RefreshCw, 
  Eye, 
  Clock, 
  Database,
  FileText,
  Zap 
} from 'lucide-react';

interface CacheHistoryEntry {
  cacheKey: string;
  timestamp: string;
  request: any;
  response: any;
  model: string;
  size: number;
}

interface CacheHistoryTableProps {
  limit?: number;
  autoRefresh?: boolean;
}

export function CacheHistoryTable({ limit = 25, autoRefresh = true }: CacheHistoryTableProps) {
  const [history, setHistory] = useState<CacheHistoryEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [selectedEntry, setSelectedEntry] = useState<CacheHistoryEntry | null>(null);
  const [populatingHistory, setPopulatingHistory] = useState(false);

  const fetchHistory = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/cache/history?limit=${limit}`);
      const result = await response.json();
      
      if (result.success) {
        setHistory(result.history || []);
        setLastRefresh(new Date());
      } else {
        console.error('Failed to fetch cache history:', result.error);
        toast.error('Failed to fetch cache history');
      }
    } catch (error) {
      console.error('Error fetching cache history:', error);
      toast.error('Cache service is not available');
    } finally {
      setLoading(false);
    }
  };

  const populateInitialHistory = async () => {
    try {
      setPopulatingHistory(true);
      const response = await fetch('/api/cache/populate-history', {
        method: 'POST',
      });
      const result = await response.json();
      
      if (result.success) {
        toast.success(`Populated ${result.populatedCount} cache history entries`);
        await fetchHistory(); // Refresh the table
      } else {
        console.error('Failed to populate cache history:', result.error);
        toast.error('Failed to populate cache history');
      }
    } catch (error) {
      console.error('Error populating cache history:', error);
      toast.error('Failed to populate initial cache history');
    } finally {
      setPopulatingHistory(false);
    }
  };

  useEffect(() => {
    fetchHistory();
    
    if (autoRefresh) {
      // Auto-refresh every 30 seconds
      const interval = setInterval(fetchHistory, 30000);
      return () => clearInterval(interval);
    }
  }, [limit, autoRefresh]);

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const truncateKey = (key: string, length: number = 12) => {
    return key.length > length ? `${key.substring(0, length)}...` : key;
  };

  const getModelBadgeColor = (model: string) => {
    if (model.includes('gpt-4')) return 'default';
    if (model.includes('gpt-3.5')) return 'secondary';
    return 'outline';
  };

  if (loading && history.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size={32} />
        <span className="ml-2">Loading cache history...</span>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Cache History
          </CardTitle>
          <div className="flex items-center gap-2">
            {lastRefresh && (
              <span className="text-xs text-muted-foreground">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchHistory} variant="outline" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <div className="text-center py-8">
            <Database className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No cache history available</p>
            <p className="text-sm text-muted-foreground mt-1 mb-4">
              Cache entries will appear here after LLM requests are processed
            </p>
            <Button 
              onClick={populateInitialHistory} 
              disabled={populatingHistory}
              variant="outline"
            >
              {populatingHistory ? (
                <>
                  <LoadingSpinner size={16} />
                  <span className="ml-2">Populating...</span>
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Populate from Existing Cache
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Showing {history.length} recent cache entries
              </p>
            </div>
            
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Cache Key</TableHead>
                    <TableHead>Model</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.map((entry, index) => (
                    <TableRow key={`${entry.cacheKey}-${index}`}>
                      <TableCell>
                        <div className="font-mono text-sm">
                          {truncateKey(entry.cacheKey)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getModelBadgeColor(entry.model)}>
                          {entry.model}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <FileText className="h-3 w-3" />
                          {formatSize(entry.size)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatTimestamp(entry.timestamp)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setSelectedEntry(entry)}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Cache Entry Details</DialogTitle>
                            </DialogHeader>
                            {selectedEntry && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">Cache Key:</label>
                                    <div className="font-mono text-xs bg-muted p-2 rounded mt-1 break-all">
                                      {selectedEntry.cacheKey}
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Model:</label>
                                    <div className="mt-1">
                                      <Badge variant={getModelBadgeColor(selectedEntry.model)}>
                                        {selectedEntry.model}
                                      </Badge>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Size:</label>
                                    <div className="text-sm mt-1">{formatSize(selectedEntry.size)}</div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Timestamp:</label>
                                    <div className="text-sm mt-1">{formatTimestamp(selectedEntry.timestamp)}</div>
                                  </div>
                                </div>
                                
                                {selectedEntry.request && (
                                  <div>
                                    <label className="text-sm font-medium mb-2 block">Request:</label>
                                    <pre className="bg-muted p-3 rounded text-xs overflow-x-auto max-h-48">
                                      {JSON.stringify(selectedEntry.request, null, 2)}
                                    </pre>
                                  </div>
                                )}
                                
                                <div>
                                  <label className="text-sm font-medium mb-2 block">Response:</label>
                                  <pre className="bg-muted p-3 rounded text-xs overflow-x-auto max-h-64">
                                    {JSON.stringify(selectedEntry.response, null, 2)}
                                  </pre>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 