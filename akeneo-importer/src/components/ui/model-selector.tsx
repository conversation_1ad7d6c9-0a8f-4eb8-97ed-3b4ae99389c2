"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RefreshCw, Bot, DollarSign, Zap, Wrench, Layers, FileJson } from "lucide-react";
import { toast } from "sonner";
import pricingService from "@/lib/pricing/pricing-service";

interface Model {
  id: string;
  name: string;
  description?: string;
  context_length?: number;
  context_window?: number;
  provider: 'openrouter' | 'groq';
}

interface ModelSelectorProps {
  selectedModel: string | null;
  onModelChange: (modelId: string | null) => void;
  disabled?: boolean;
  label?: string;
  description?: string;
  showPricing?: boolean;
  className?: string;
}

export function ModelSelector({
  selectedModel,
  onModelChange,
  disabled = false,
  label = "LLM Model",
  description = "Select the language model to use for transformation",
  showPricing = true,
  className = ""
}: ModelSelectorProps) {
  const [models, setModels] = useState<Model[]>([]);
  const [enabledModels, setEnabledModels] = useState<string[]>([]);
  const [defaultModel, setDefaultModel] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [currentProvider, setCurrentProvider] = useState<'openrouter' | 'groq'>('groq');
  
  // Add ref to track if models have been loaded to prevent duplicate calls
  const modelsLoadedRef = useRef<boolean>(false);
  const isLoadingRef = useRef<boolean>(false);

  // Memoized loadModels function to prevent unnecessary re-calls during bulk operations
  const loadModels = useCallback(async () => {
    // Prevent duplicate calls if already loading or already loaded
    if (isLoadingRef.current || (modelsLoadedRef.current && models.length > 0)) {
      console.log('⏭️ Skipping model loading - already loaded or in progress');
      return;
    }

    isLoadingRef.current = true;
    setLoading(true);
    
    try {
      console.log('🔄 Loading models for ModelSelector...');
      
      // Load current provider first
      let provider = currentProvider;
      const providerResponse = await fetch('/api/models/provider');
      if (providerResponse.ok) {
        const providerData = await providerResponse.json();
        if (providerData.success) {
          provider = providerData.provider;
          setCurrentProvider(provider);
        }
      }

      // Load enabled models using the correct provider
      const enabledResponse = await fetch(`/api/models/enabled?provider=${provider}`);
      if (enabledResponse.ok) {
        const enabledData = await enabledResponse.json();
        if (enabledData.success) {
          const enabledModelIds = enabledData.enabled_models.map((m: any) => m.id);
          setEnabledModels(enabledModelIds);
          setDefaultModel(enabledData.default_model);
          
          console.log(`📋 Enabled models for ${provider}:`, enabledModelIds.length);
          
          // Auto-select default model if no model is selected
          if (!selectedModel && enabledData.default_model) {
            onModelChange(enabledData.default_model);
          }
        }
      }

      // Load available models using the correct provider
      const availableResponse = await fetch(`/api/models/available?provider=${provider}`);
      if (availableResponse.ok) {
        const availableData = await availableResponse.json();
        if (availableData.success) {
          setModels(availableData.models);
          console.log(`📋 Available models for ${provider}:`, availableData.models.length);
          modelsLoadedRef.current = true;
        }
      }
    } catch (error) {
      console.error('Error loading models:', error);
      toast.error('Failed to load available models');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, []);

  // Only load models once when component mounts
  useEffect(() => {
    loadModels();
  }, []); // Empty dependency array - only run once on mount

  const refreshModels = async () => {
    // Prevent multiple refresh calls
    if (isLoadingRef.current) {
      console.log('⏭️ Skipping model refresh - already in progress');
      return;
    }

    isLoadingRef.current = true;
    setLoading(true);
    
    try {
      console.log('🔄 Refreshing models manually...');
      const response = await fetch(`/api/models/available?refresh=true&provider=${currentProvider}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setModels(data.models);
          modelsLoadedRef.current = true;
          toast.success(`Refreshed ${data.models.length} models`);
        }
      }
    } catch (error) {
      console.error('Error refreshing models:', error);
      toast.error('Failed to refresh models');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  };

  // Filter to only show enabled models
  const availableModels = models.filter(model => enabledModels.includes(model.id));
  
  // Debug logging to help troubleshoot
  console.log(`🔍 ModelSelector Debug:`, {
    totalModels: models.length,
    enabledModels: enabledModels,
    availableModels: availableModels.length,
    filteredModelIds: availableModels.map(m => m.id)
  });

  const getModelDisplayInfo = (model: Model) => {
    const pricing = pricingService.getModelPricing(model.id);
    const contextLength = model.context_length || model.context_window;
    
    return {
      name: model.name,
      pricing,
      contextLength: contextLength ? `${contextLength.toLocaleString()} tokens` : null,
      isDefault: model.id === defaultModel
    };
  };

  if (availableModels.length === 0 && !loading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Bot className="h-4 w-4" />
            {label}
          </CardTitle>
          {description && (
            <CardDescription className="text-xs">
              {description}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-center py-3 text-sm text-muted-foreground">
            <p>No enabled models available</p>
            <p className="text-xs mt-1">
              {models.length > 0 
                ? `${models.length} models loaded, but none are enabled. Please enable models in Settings → AI Models.`
                : 'No models loaded. Please check your provider configuration in Settings → AI Models.'
              }
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshModels}
              className="mt-2"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            {label}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshModels}
            disabled={loading || disabled}
            className="h-5 w-5 p-0"
          >
            <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
        {description && (
          <CardDescription className="text-xs">
            {description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="pt-0">
        <Select
          value={selectedModel || ""}
          onValueChange={(value) => onModelChange(value || null)}
          disabled={disabled || loading}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={loading ? "Loading models..." : "Select a model"} />
          </SelectTrigger>
          <SelectContent>
            {availableModels.map((model) => {
              const displayInfo = getModelDisplayInfo(model);
              
              return (
                <SelectItem key={model.id} value={model.id}>
                  <div className="flex items-center gap-2 w-full">
                    <span className="font-medium">{displayInfo.name}</span>
                    {displayInfo.isDefault && (
                      <Badge variant="default" className="text-xs">Default</Badge>
                    )}
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
        
        {selectedModel && showPricing && (() => {
          const selectedModelInfo = availableModels.find(m => m.id === selectedModel);
          const pricing = selectedModelInfo ? pricingService.getModelPricing(selectedModel) : null;
          
          if (pricing) {
            return (
              <div className="mt-2 p-2 bg-muted/50 rounded-sm">
                <div className="text-xs text-muted-foreground">
                  <div className="font-medium mb-1">Cost Estimate</div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>Input: {pricingService.formatPrice(pricing.input_price_per_million)}/M tokens</div>
                    <div>Output: {pricingService.formatPrice(pricing.output_price_per_million)}/M tokens</div>
                  </div>
                  <div className="flex items-center gap-4 mt-1 text-xs">
                    <div className="flex items-center gap-1">
                      <Zap className="h-3 w-3" />
                      <span>{pricing.speed_tokens_per_second} tok/s</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span>Context: {selectedModelInfo?.context_length || selectedModelInfo?.context_window ? `${(selectedModelInfo.context_length || selectedModelInfo.context_window)?.toLocaleString()} tokens` : 'Unknown'}</span>
                    </div>
                  </div>
                  {pricing.capabilities && (
                    <div className="flex items-center gap-2 mt-1">
                      <div className={`flex items-center gap-1 ${pricing.capabilities.tool_use ? 'text-green-600' : 'text-gray-400'}`}>
                        <Wrench className="h-3 w-3" />
                        <span>Tools</span>
                      </div>
                      <div className={`flex items-center gap-1 ${pricing.capabilities.parallel_tool_use ? 'text-green-600' : 'text-gray-400'}`}>
                        <Layers className="h-3 w-3" />
                        <span>Parallel</span>
                      </div>
                      <div className={`flex items-center gap-1 ${pricing.capabilities.json_mode ? 'text-green-600' : 'text-gray-400'}`}>
                        <FileJson className="h-3 w-3" />
                        <span>JSON</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          }
          return null;
        })()}
      </CardContent>
    </Card>
  );
} 