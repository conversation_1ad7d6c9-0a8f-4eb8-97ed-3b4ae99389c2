'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  RefreshCw, 
  ExternalLink, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  FileSpreadsheet,
  Database,
  Settings
} from 'lucide-react';
import { useGoogleSheets } from '@/hooks/use-google-sheets';

export function GoogleSheetsManager() {
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const {
    authenticated,
    loading,
    error,
    mappingDefinitions,
    worksheetNames,
    validationData,
    authenticate,
    logout,
    refreshCache,
    checkAuthStatus
  } = useGoogleSheets();

  const handleAuthenticate = async () => {
    setIsAuthenticating(true);
    try {
      const success = await authenticate();
      // Handle success/failure in the UI state
    } catch (error) {
      console.error('Authentication error:', error);
    } finally {
      setIsAuthenticating(false);
    }
  };

  const handleLogout = async () => {
    const success = await logout();
    // Handle success/failure in the UI state
  };

  const handleRefreshCache = async () => {
    setIsRefreshing(true);
    try {
      const result = await refreshCache();
      // Handle success/failure in the UI state
    } catch (error) {
      console.error('Refresh error:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusIcon = () => {
    if (loading) return <RefreshCw className="h-4 w-4 animate-spin" />;
    if (authenticated) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusText = () => {
    if (loading) return "Checking connection...";
    if (authenticated) return "Connected";
    return "Not connected";
  };

  const getStatusColor = () => {
    if (loading) return "secondary";
    if (authenticated) return "default";
    return "destructive";
  };

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Google Sheets Integration
          </CardTitle>
          <CardDescription>
            Manage your Google Sheets connection and configuration data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="font-medium">Status:</span>
              <Badge variant={getStatusColor() as any}>
                {getStatusText()}
              </Badge>
            </div>
            
            <div className="flex gap-2">
              {authenticated ? (
                <>
                  <Button
                    onClick={handleRefreshCache}
                    disabled={isRefreshing}
                    variant="outline"
                    size="sm"
                  >
                    {isRefreshing ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <RefreshCw className="h-4 w-4 mr-2" />
                    )}
                    Refresh Cache
                  </Button>
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    size="sm"
                  >
                    Disconnect
                  </Button>
                </>
              ) : (
                <Button
                  onClick={handleAuthenticate}
                  disabled={isAuthenticating}
                  size="sm"
                >
                  {isAuthenticating ? (
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <ExternalLink className="h-4 w-4 mr-2" />
                  )}
                  Connect to Google Sheets
                </Button>
              )}
            </div>
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {authenticated && (
            <div className="text-sm text-muted-foreground">
              <p>Connected to Google Sheets. Configuration data will be loaded from your spreadsheet.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Data Summary */}
      {authenticated && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Configuration Data
            </CardTitle>
            <CardDescription>
              Summary of loaded configuration data from Google Sheets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {mappingDefinitions.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  Mapping Definitions
                </div>
              </div>
              
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {worksheetNames.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  Available Worksheets
                </div>
              </div>
              
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {Object.keys(validationData).length}
                </div>
                <div className="text-sm text-muted-foreground">
                  Validation Data Sets
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mapping Definitions Details */}
      {authenticated && mappingDefinitions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Mapping Definitions
            </CardTitle>
            <CardDescription>
              Column mapping definitions loaded from Google Sheets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {mappingDefinitions.map((def, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <div className="font-medium">{def.display_name}</div>
                    <div className="text-sm text-muted-foreground">
                      {def.column_name} • {def.data_type}
                      {def.required && <Badge variant="secondary" className="ml-2">Required</Badge>}
                    </div>
                  </div>
                  {def.validation_config && (
                    <Badge variant="outline">
                      {def.validation_config.worksheet_name}:{def.validation_config.column_name}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Worksheet Names */}
      {authenticated && worksheetNames.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Available Worksheets</CardTitle>
            <CardDescription>
              Worksheets available in your Google Sheets configuration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {worksheetNames.map((name, index) => (
                <Badge key={index} variant="secondary">
                  {name}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}