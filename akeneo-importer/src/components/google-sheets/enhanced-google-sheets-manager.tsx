'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertCircle, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileSpreadsheet, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Settings,
  Database,
  TrendingUp,
  Clock,
  Zap
} from 'lucide-react';

interface ConfigurationMetadata {
  lastRefresh: string;
  version: string;
  worksheetCount: number;
  mappingDefinitionsCount: number;
  validationTablesCount: number;
  spreadsheetId: string;
  checksum: string;
}

interface ConfigurationStats {
  cacheStatus: 'hit' | 'miss' | 'expired';
  cacheAge: number;
  cacheSize: number;
  validationTablesCount: number;
  lastRefresh: string | null;
  nextRefreshDue: string | null;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalColumns: number;
    requiredColumns: number;
    validationTables: number;
    missingValidationTables: string[];
  };
}

export function EnhancedGoogleSheetsManager() {
  const [loading, setLoading] = useState(false);
  const [metadata, setMetadata] = useState<ConfigurationMetadata | null>(null);
  const [stats, setStats] = useState<ConfigurationStats | null>(null);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState<boolean | null>(null);
  const [lastChangeCheck, setLastChangeCheck] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    loadConfigurationData();
  }, []);

  const loadConfigurationData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load configuration stats
      const statsResponse = await fetch('/api/google-sheets/config/redis?stats_only=true');
      const statsData = await statsResponse.json();
      
      if (statsData.success) {
        setStats(statsData.data.stats);
      }

      // Load validation data
      const validationResponse = await fetch('/api/google-sheets/config/redis', {
        method: 'PUT'
      });
      const validationData = await validationResponse.json();
      
      if (validationData.success) {
        setValidation(validationData.data);
      }

      // Check for changes
      await checkForChanges();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load configuration data');
    } finally {
      setLoading(false);
    }
  };

  const checkForChanges = async () => {
    try {
      const response = await fetch('/api/google-sheets/config/changes');
      const data = await response.json();
      
      if (data.success) {
        setHasChanges(data.data.hasChanges);
        setLastChangeCheck(data.data.lastCheck);
      }
    } catch (err) {
      console.error('Failed to check for changes:', err);
    }
  };

  const refreshConfiguration = async (force: boolean = false) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/google-sheets/refresh-cache', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ useRedis: true, force })
      });

      const data = await response.json();

      if (data.success) {
        setMetadata(data.metadata);
        setValidation(data.validation);
        setStats(data.stats);
        setHasChanges(false);
        
        // Show success message or toast here
        console.log('✅ Configuration refreshed successfully');
      } else {
        throw new Error(data.message || 'Refresh failed');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh configuration');
    } finally {
      setLoading(false);
    }
  };

  const invalidateCache = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/google-sheets/config/redis', {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        // Reload data after cache invalidation
        await loadConfigurationData();
        console.log('🗑️ Cache invalidated successfully');
      } else {
        throw new Error(data.error || 'Cache invalidation failed');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to invalidate cache');
    } finally {
      setLoading(false);
    }
  };

  const getCacheStatusColor = (status: string) => {
    switch (status) {
      case 'hit': return 'bg-green-500';
      case 'expired': return 'bg-yellow-500';
      case 'miss': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getValidationStatusIcon = (isValid: boolean) => {
    return isValid ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Enhanced Google Sheets Configuration Manager
          <Badge variant="outline" className="ml-2">Redis-Backed</Badge>
        </CardTitle>
        <CardDescription>
          Server-side configuration management with Redis caching, validation, and change detection
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="validation">Validation</TabsTrigger>
            <TabsTrigger value="actions">Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Cache Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Cache Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <div className={`h-2 w-2 rounded-full ${getCacheStatusColor(stats?.cacheStatus || 'miss')}`} />
                    <span className="capitalize">{stats?.cacheStatus || 'Unknown'}</span>
                  </div>
                  {stats?.cacheAge && (
                    <p className="text-sm text-muted-foreground mt-1">
                      Age: {Math.floor(stats.cacheAge / 60)}m {stats.cacheAge % 60}s
                    </p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                  {validation ? (
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        {getValidationStatusIcon(validation.isValid)}
                        <span className="text-sm">
                          {validation.summary.totalColumns} columns
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {validation.summary.requiredColumns} required, {validation.summary.validationTables} validation tables
                      </p>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Loading...</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Changes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    {hasChanges === null ? (
                      <Clock className="h-4 w-4 text-gray-500" />
                    ) : hasChanges ? (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    <span className="text-sm">
                      {hasChanges === null 
                        ? 'Checking...' 
                        : hasChanges 
                          ? 'Changes detected' 
                          : 'Up to date'
                      }
                    </span>
                  </div>
                  {lastChangeCheck && (
                    <p className="text-sm text-muted-foreground mt-1">
                      Last check: {new Date(lastChangeCheck).toLocaleTimeString()}
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Last Refresh Info */}
            {stats?.lastRefresh && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Last Refresh</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm">
                        {new Date(stats.lastRefresh).toLocaleString()}
                      </p>
                      {stats.nextRefreshDue && (
                        <p className="text-sm text-muted-foreground">
                          Next refresh due: {new Date(stats.nextRefreshDue).toLocaleTimeString()}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {stats.validationTablesCount} validation tables
                      </p>
                      <p className="text-sm text-muted-foreground">
                        ~{Math.round(stats.cacheSize / 1024)} KB cached
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stats ? (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Cache Performance</p>
                        <div className="mt-2">
                          <div className="flex justify-between text-sm">
                            <span>Cache Efficiency</span>
                            <span>{stats.cacheStatus === 'hit' ? '100%' : '0%'}</span>
                          </div>
                          <Progress 
                            value={stats.cacheStatus === 'hit' ? 100 : 0} 
                            className="mt-1"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium">Memory Usage</p>
                        <div className="mt-2">
                          <div className="flex justify-between text-sm">
                            <span>Cache Size</span>
                            <span>{Math.round(stats.cacheSize / 1024)} KB</span>
                          </div>
                          <Progress 
                            value={Math.min((stats.cacheSize / (1024 * 100)) * 100, 100)} 
                            className="mt-1"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-2xl font-bold">{stats.cacheAge}s</p>
                        <p className="text-sm text-muted-foreground">Cache Age</p>
                      </div>
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-2xl font-bold">{stats.validationTablesCount}</p>
                        <p className="text-sm text-muted-foreground">Validation Tables</p>
                      </div>
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-2xl font-bold">
                          {Math.round(stats.cacheSize / 1024)}KB
                        </p>
                        <p className="text-sm text-muted-foreground">Memory Used</p>
                      </div>
                    </div>
                  </>
                ) : (
                  <p className="text-muted-foreground">No performance data available</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="validation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {validation && getValidationStatusIcon(validation.isValid)}
                  Configuration Validation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {validation ? (
                  <>
                    {/* Validation Summary */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-3 bg-muted rounded-lg">
                        <p className="text-2xl font-bold">{validation.summary.totalColumns}</p>
                        <p className="text-sm text-muted-foreground">Total Columns</p>
                      </div>
                      <div className="text-center p-3 bg-muted rounded-lg">
                        <p className="text-2xl font-bold">{validation.summary.requiredColumns}</p>
                        <p className="text-sm text-muted-foreground">Required</p>
                      </div>
                      <div className="text-center p-3 bg-muted rounded-lg">
                        <p className="text-2xl font-bold">{validation.summary.validationTables}</p>
                        <p className="text-sm text-muted-foreground">Validation Tables</p>
                      </div>
                      <div className="text-center p-3 bg-muted rounded-lg">
                        <p className="text-2xl font-bold text-red-500">
                          {validation.summary.missingValidationTables.length}
                        </p>
                        <p className="text-sm text-muted-foreground">Missing Tables</p>
                      </div>
                    </div>

                    {/* Errors */}
                    {validation.errors.length > 0 && (
                      <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertTitle>Configuration Errors ({validation.errors.length})</AlertTitle>
                        <AlertDescription className="mt-2">
                          <ul className="list-disc pl-4 space-y-1">
                            {validation.errors.map((error, index) => (
                              <li key={index} className="text-sm">{error}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Warnings */}
                    {validation.warnings.length > 0 && (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>Configuration Warnings ({validation.warnings.length})</AlertTitle>
                        <AlertDescription className="mt-2">
                          <ul className="list-disc pl-4 space-y-1">
                            {validation.warnings.map((warning, index) => (
                              <li key={index} className="text-sm">{warning}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Success State */}
                    {validation.isValid && (
                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertTitle>Configuration Valid</AlertTitle>
                        <AlertDescription>
                          All configuration checks passed successfully.
                        </AlertDescription>
                      </Alert>
                    )}
                  </>
                ) : (
                  <p className="text-muted-foreground">Loading validation data...</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="actions" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Refresh Configuration</CardTitle>
                  <CardDescription>
                    Update configuration from Google Sheets and store in Redis cache
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    onClick={() => refreshConfiguration(false)}
                    disabled={loading}
                    className="w-full"
                  >
                    <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                    Smart Refresh
                  </Button>
                  <Button 
                    onClick={() => refreshConfiguration(true)}
                    disabled={loading}
                    variant="outline"
                    className="w-full"
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    Force Refresh
                  </Button>
                  <p className="text-sm text-muted-foreground">
                    Smart refresh checks cache age, force refresh ignores cache.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Cache Management</CardTitle>
                  <CardDescription>
                    Manage Redis cache and check for configuration changes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    onClick={checkForChanges}
                    disabled={loading}
                    variant="outline"
                    className="w-full"
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Check for Changes
                  </Button>
                  <Button 
                    onClick={invalidateCache}
                    disabled={loading}
                    variant="destructive"
                    className="w-full"
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Invalidate Cache
                  </Button>
                  <p className="text-sm text-muted-foreground">
                    Use invalidate cache to force fresh data on next access.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 