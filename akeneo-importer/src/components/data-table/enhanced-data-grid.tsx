"use client";

import { useRef, useState, useEffect, useMemo, useCallback } from "react";
import { AgGridReact } from 'ag-grid-react';
import { 
  ColDef, 
  GridApi, 
  GridReadyEvent, 
  CellValueChangedEvent,
  ModuleRegistry,
  AllCommunityModule
} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

// Register AG Grid modules
ModuleRegistry.registerModules([AllCommunityModule]);
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { 
  Zap, 
  MapPin, 
  Type, 
  EyeOff, 
  Loader2,
  Settings,
  Wand2,
  Save,
  RefreshCw,
  Upload,
  FileSpreadsheet
} from "lucide-react";

// Configuration-driven interfaces based on Google Sheets structure
interface ConfigurationRow {
  Column_name: string;
  Type: "Navisionvorlage" | "Akeneo";
  Required: boolean;
  Default_Mapping: "Deactivated" | "String" | "1-to-1" | "AI Transform" | null;
  /** @deprecated Use Custom_Mapping_Prompt_Template instead */
  Default_Mapping_Content: string | null;
  /** Custom prompt template that overrides the default template for this column */
  Custom_Mapping_Prompt_Template?: string | null;
  Prompt: string | null;
  Output_Validation_Column: string | null;
}

interface LookupTable {
  Code: string;
  Beschreibung: string;
  [key: string]: any;
}

interface ColumnTransformationMode {
  type: '1-to-1' | 'ai-transform' | 'string-overwrite' | 'deactivated';
  sourceColumn?: string;
  aiPrompt?: string;
  stringValue?: string;
  isProcessing?: boolean;
}

interface GridConfig {
  targetColumns: ConfigurationRow[];
  sourceData: any[][];
  lookupTables: Record<string, LookupTable[]>;
  defaultMappings: Record<string, ColumnTransformationMode>;
}

interface DirectDataGridProps {
  jobId?: string;
  onUploadComplete?: (gridConfig: GridConfig) => void;
  initialGridConfig?: GridConfig;
  sourceColumns?: string[];
}

export function DirectDataGrid({ 
  jobId, 
  onUploadComplete, 
  initialGridConfig,
  sourceColumns = []
}: DirectDataGridProps) {
  const gridRef = useRef<AgGridReact>(null);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [gridConfig, setGridConfig] = useState<GridConfig | null>(initialGridConfig || null);
  const [transformationModes, setTransformationModes] = useState<Record<string, ColumnTransformationMode>>({});
  const [selectedColumn, setSelectedColumn] = useState<string>("");
  const [showColumnConfig, setShowColumnConfig] = useState(false);
  const [rowData, setRowData] = useState<any[]>([]);

  // Load configuration from Google Sheets on mount
  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await fetch('/api/google-sheets/config/main');
      if (!response.ok) throw new Error('Failed to load configuration');
      
      const responseText = await response.text();
      if (!responseText || responseText === 'undefined') {
        throw new Error('Empty or invalid response from configuration endpoint');
      }
      
      const config = JSON.parse(responseText);
      
      // Ensure config has the expected structure
      if (!config || !config.configuration || !Array.isArray(config.configuration)) {
        throw new Error('Invalid configuration format received');
      }
      
      // Filter columns where Required = true (as per documentation)
      const requiredColumns = config.configuration.filter((col: ConfigurationRow) => col.Required === true);
      
      // Set up default transformation modes based on configuration
      const defaultModes: Record<string, ColumnTransformationMode> = {};
      requiredColumns.forEach((col: ConfigurationRow) => {
        const defaultType = col.Default_Mapping?.toLowerCase().replace(' ', '-') as ColumnTransformationMode['type'] || 'deactivated';
        defaultModes[col.Column_name] = {
          type: defaultType,
          stringValue: col.Custom_Mapping_Prompt_Template || col.Default_Mapping_Content || '',
          aiPrompt: col.Prompt || '',
          sourceColumn: sourceColumns[0] || '', // Default to first source column
        };
      });

      setGridConfig({
        targetColumns: requiredColumns,
        sourceData: [],
        lookupTables: config.lookupTables || {},
        defaultMappings: defaultModes
      });
      
      setTransformationModes(defaultModes);
      
    } catch (error) {
      console.error('Failed to load configuration:', error);
      toast.error('Failed to load configuration from Google Sheets');
    } finally {
      setLoading(false);
    }
  };

  // File upload handler - Direct to Grid (NO WIZARD)
  const handleFileUpload = async (file: File, worksheetName?: string): Promise<void> => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (worksheetName) formData.append('worksheet_name', worksheetName);
      
      // Direct upload with immediate grid display
      const response = await fetch('/api/import/upload-and-grid', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) throw new Error('Upload failed');
      
      const result = await response.json();
      
      // Update grid configuration with source data
      if (gridConfig) {
        const updatedConfig = {
          ...gridConfig,
          sourceData: result.sourceData || [],
        };
        setGridConfig(updatedConfig);
        onUploadComplete?.(updatedConfig);
        
        // Transform and set row data
        const transformedData = await transformLocalData(0, result.sourceData?.length || 0);
        setRowData(transformedData);
      }
      
      toast.success('File uploaded successfully! Grid ready for transformation.');
      
    } catch (error) {
      console.error('Upload failed:', error);
      toast.error('Failed to upload file');
    } finally {
      setUploading(false);
    }
  };

  // Column definitions for AG Grid - Configuration-driven
  const columnDefs = useMemo((): ColDef[] => {
    if (!gridConfig) return [];
    
    return gridConfig.targetColumns
      .filter(col => transformationModes[col.Column_name]?.type !== 'deactivated')
      .map((col): ColDef => {
        const mode = transformationModes[col.Column_name];
        const lookupData = col.Output_Validation_Column ? 
          gridConfig.lookupTables[col.Output_Validation_Column] : null;
        
        return {
          headerName: col.Column_name,
          field: col.Column_name,
          sortable: true,
          filter: true,
          resizable: true,
          editable: mode?.type !== 'deactivated',
          minWidth: 150,
          maxWidth: 300,
          
          // Cell styling based on transformation mode
          cellStyle: (params) => {
            const currentMode = transformationModes[params.colDef.field!];
            if (!currentMode) return null;
            
            switch (currentMode.type) {
              case '1-to-1':
                return { backgroundColor: '#f0f9ff', borderLeft: '4px solid #3b82f6' };
              case 'ai-transform':
                return { backgroundColor: '#fef3c7', borderLeft: '4px solid #f59e0b' };
              case 'string-overwrite':
                return { backgroundColor: '#f3e8ff', borderLeft: '4px solid #8b5cf6' };
              default:
                return null;
            }
          },
          
          // Dropdown editor for validation columns
          cellEditor: lookupData ? 'agSelectCellEditor' : 'agTextCellEditor',
          cellEditorParams: lookupData ? {
            values: lookupData.map(item => item.Code)
          } : undefined,
          
          // Custom cell renderer for AI processing status
          cellRenderer: (params: any) => {
            const mode = transformationModes[params.colDef.field!];
            if (mode?.type === 'ai-transform' && mode.isProcessing) {
              return '<div class="flex items-center"><div class="animate-spin h-4 w-4 border-2 border-orange-500 border-t-transparent rounded-full mr-2"></div>Processing...</div>';
            }
            return params.value;
          }
        };
      });
  }, [gridConfig, transformationModes]);

  // Transform local data based on column modes
  const transformLocalData = async (startRow: number, endRow: number): Promise<any[]> => {
    if (!gridConfig) return [];
    
    const sourceRows = gridConfig.sourceData.slice(startRow, endRow);
    const transformedRows = [];
    
    for (const sourceRow of sourceRows) {
      const transformedRow: any = {};
      
      for (const [columnName, mode] of Object.entries(transformationModes)) {
        if (mode.type === 'deactivated') continue;
        
        switch (mode.type) {
          case '1-to-1':
            // Map from source column
            const sourceIndex = sourceColumns.findIndex(col => col === mode.sourceColumn);
            transformedRow[columnName] = sourceIndex >= 0 ? sourceRow[sourceIndex] : '';
            break;
            
          case 'string-overwrite':
            transformedRow[columnName] = mode.stringValue || '';
            break;
            
          case 'ai-transform':
            if (mode.isProcessing) {
              transformedRow[columnName] = '[AI Processing...]';
            } else {
              // Use source data as placeholder until AI processing
              const aiSourceIndex = sourceColumns.findIndex(col => col === mode.sourceColumn);
              transformedRow[columnName] = aiSourceIndex >= 0 ? sourceRow[aiSourceIndex] : '[Needs Processing]';
            }
            break;
            
          default:
            transformedRow[columnName] = '';
        }
      }
      
      transformedRows.push(transformedRow);
    }
    
    return transformedRows;
  };

  // Grid ready event handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api);
  }, []);

  // Update column transformation mode
  const updateColumnMode = useCallback((columnName: string, mode: ColumnTransformationMode) => {
    setTransformationModes(prev => ({
      ...prev,
      [columnName]: mode
    }));
    
    // Refresh grid data with new transformation
    if (gridConfig) {
      transformLocalData(0, gridConfig.sourceData.length).then(setRowData);
    }
  }, [gridConfig]);

  // Apply AI transformation to column
  const applyAITransformation = useCallback(async (columnName: string) => {
    const mode = transformationModes[columnName];
    if (!mode || mode.type !== 'ai-transform') return;

    // Set processing state
    updateColumnMode(columnName, { ...mode, isProcessing: true });

    try {
      const response = await fetch('/api/import/ai-transform', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_id: jobId,
          column_name: columnName,
          ai_prompt: mode.aiPrompt,
          source_column: mode.sourceColumn,
          transformation_mode: 'ai-transform'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'AI transformation failed');
      }
      
      toast.success(`AI transformation applied to ${columnName}`);
      
      // Refresh grid data
      if (gridConfig) {
        const transformedData = await transformLocalData(0, gridConfig.sourceData.length);
        setRowData(transformedData);
      }
      
    } catch (error) {
      console.error('AI transformation failed:', error);
      toast.error('Failed to apply AI transformation');
    } finally {
      // Clear processing state
      updateColumnMode(columnName, { ...mode, isProcessing: false });
    }
  }, [transformationModes, updateColumnMode, jobId, gridConfig]);

  // Bulk apply transformation to entire column
  const bulkApplyTransformation = useCallback(async (columnName: string) => {
    const mode = transformationModes[columnName];
    if (!mode) return;

    updateColumnMode(columnName, { ...mode, isProcessing: true });

    try {
      // Dynamic concurrency based on row count: 10 workers for multiple rows, 1 for single row
      const totalRows = gridConfig?.sourceData?.length || rowData?.length || 0;
      const dynamicConcurrency = totalRows > 1 ? 10 : 1;
      
      const response = await fetch('/api/transform/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_id: jobId,
          column_name: columnName,
          transformation_mode: (() => {
            switch (mode.type) {
              case '1-to-1': return '1to1';
              case 'ai-transform': return 'ai_transform';
              case 'string-overwrite': return 'string_overwrite';
              case 'deactivated': return 'deactivated';
              default: return 'deactivated';
            }
          })(),
          source_column: mode.sourceColumn,
          string_value: mode.stringValue,
          ai_prompt: mode.aiPrompt,
          batch_size: 10, // Reasonable batch size
          concurrency: dynamicConcurrency // Dynamic: 10 workers for multiple rows, 1 for single row
        }),
      });

      if (!response.ok) throw new Error('Bulk transformation failed');
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(`Bulk transformation applied to ${columnName} with ${result.worker_stats?.totalWorkers || 1} workers (${totalRows} rows processed)`);
      } else {
        throw new Error(result.message || 'Bulk transformation failed');
      }
      
      // Refresh grid data
      if (gridConfig) {
        const transformedData = await transformLocalData(0, gridConfig.sourceData.length);
        setRowData(transformedData);
      }

    } catch (error) {
      console.error('Bulk transformation failed:', error);
      toast.error('Failed to apply bulk transformation');
    } finally {
      updateColumnMode(columnName, { ...mode, isProcessing: false });
    }
  }, [transformationModes, updateColumnMode, jobId, gridConfig, rowData]);

  // Cell value changed handler
  const onCellValueChanged = useCallback((event: CellValueChangedEvent) => {
    const { colDef, newValue, rowIndex } = event;
    
    if (colDef.field && jobId) {
      fetch('/api/import/update-cell', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_id: jobId,
          row_index: rowIndex,
          column_name: colDef.field,
          value: newValue,
        }),
      }).catch(error => {
        console.error('Failed to update cell:', error);
        toast.error('Failed to update cell value');
      });
    }
  }, [jobId]);

  // File upload drag and drop area
  const renderFileUpload = () => (
    <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
      <CardContent className="p-8">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            {uploading ? (
              <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
            ) : (
              <Upload className="h-12 w-12 text-gray-400" />
            )}
          </div>
          <div>
            <h3 className="text-lg font-medium">
              {uploading ? 'Processing File...' : 'Upload File for Direct Grid View'}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {uploading ? 
                'File is being processed and grid is being prepared...' :
                'CSV, Excel files supported. Grid will appear immediately after upload.'
              }
            </p>
          </div>
          {!uploading && (
            <input
              type="file"
              accept=".csv,.xlsx,.xls"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload(file);
              }}
              className="hidden"
              id="file-upload"
            />
          )}
          {!uploading && (
            <label
              htmlFor="file-upload"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Choose File
            </label>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Column configuration panel
  const renderColumnConfiguration = () => {
    if (!selectedColumn || !transformationModes[selectedColumn]) return null;

    const mode = transformationModes[selectedColumn];
    const columnConfig = gridConfig?.targetColumns.find(col => col.Column_name === selectedColumn);

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Configure: {selectedColumn}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Transformation Mode</Label>
            <Select
              value={mode.type}
              onValueChange={(value: ColumnTransformationMode['type']) => {
                updateColumnMode(selectedColumn, {
                  ...mode,
                  type: value,
                  // Reset type-specific values
                  ...(value === 'string-overwrite' && { stringValue: columnConfig?.Default_Mapping_Content || '' }),
                  ...(value === 'ai-transform' && { aiPrompt: columnConfig?.Prompt || '' })
                });
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1-to-1">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4" />
                    <span>1-to-1 Source Mapping</span>
                  </div>
                </SelectItem>
                <SelectItem value="ai-transform">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4" />
                    <span>AI Transformation</span>
                  </div>
                </SelectItem>
                <SelectItem value="string-overwrite">
                  <div className="flex items-center space-x-2">
                    <Type className="h-4 w-4" />
                    <span>String Overwrite</span>
                  </div>
                </SelectItem>
                <SelectItem value="deactivated">
                  <div className="flex items-center space-x-2">
                    <EyeOff className="h-4 w-4" />
                    <span>Deactivated</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {mode.type === '1-to-1' && (
            <div>
              <Label>Source Column</Label>
              <Select
                value={mode.sourceColumn || ''}
                onValueChange={(value) => updateColumnMode(selectedColumn, { ...mode, sourceColumn: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select source column" />
                </SelectTrigger>
                <SelectContent>
                  {sourceColumns.map(col => (
                    <SelectItem key={col} value={col}>{col}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {mode.type === 'string-overwrite' && (
            <div>
              <Label>String Value</Label>
              <Input
                value={mode.stringValue || ''}
                onChange={(e) => updateColumnMode(selectedColumn, { ...mode, stringValue: e.target.value })}
                placeholder="Enter static string value"
              />
            </div>
          )}

          {mode.type === 'ai-transform' && (
            <div className="space-y-3">
              <div>
                <Label>Source Column</Label>
                <Select
                  value={mode.sourceColumn || ''}
                  onValueChange={(value) => updateColumnMode(selectedColumn, { ...mode, sourceColumn: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select source column" />
                  </SelectTrigger>
                  <SelectContent>
                    {sourceColumns.map(col => (
                      <SelectItem key={col} value={col}>{col}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>AI Transformation Prompt</Label>
                <Textarea
                  value={mode.aiPrompt || ''}
                  onChange={(e) => updateColumnMode(selectedColumn, { ...mode, aiPrompt: e.target.value })}
                  placeholder={columnConfig?.Prompt || "Describe how you want to transform the data..."}
                  className="min-h-[100px]"
                />
              </div>
              
              <div className="flex space-x-2">
                <Button
                  onClick={() => applyAITransformation(selectedColumn)}
                  disabled={mode.isProcessing || !mode.aiPrompt}
                  size="sm"
                >
                  {mode.isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Test Transform
                    </>
                  )}
                </Button>
                
                <Button
                  onClick={() => bulkApplyTransformation(selectedColumn)}
                  disabled={mode.isProcessing || !mode.aiPrompt}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Bulk Apply
                </Button>
              </div>
            </div>
          )}

          <Button
            onClick={() => {
              toast.success(`Configuration saved for ${selectedColumn}`);
            }}
            className="w-full"
            variant="outline"
          >
            <Save className="h-4 w-4 mr-2" />
            Apply Changes
          </Button>
        </CardContent>
      </Card>
    );
  };

  // Show file upload if no data loaded
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>Loading configuration...</span>
      </div>
    );
  }

  if (!gridConfig) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Configuration Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Failed to load configuration from Google Sheets. Please check your configuration.
            </p>
            <Button onClick={loadConfiguration}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Loading Configuration
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show upload area if no source data
  if (!jobId && gridConfig.sourceData.length === 0) {
    return (
      <div className="space-y-4">
        {renderFileUpload()}
        <Card>
          <CardHeader>
            <CardTitle>Configuration Ready</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              Configuration loaded with {gridConfig.targetColumns.length} target columns. 
              Upload a file to begin data preview.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Column status summary
  const getColumnStatusSummary = () => {
    const counts = {
      'one-to-one': Object.values(transformationModes).filter(m => m.type === '1-to-1').length,
      'ai-transform': Object.values(transformationModes).filter(m => m.type === 'ai-transform').length,
      'string-overwrite': Object.values(transformationModes).filter(m => m.type === 'string-overwrite').length,
      'deactivated': Object.values(transformationModes).filter(m => m.type === 'deactivated').length
    };
    return counts;
  };

  const statusSummary = getColumnStatusSummary();

  return (
    <div className="space-y-4">
      {/* Column Status Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Column Transformation Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <Badge variant="outline" className="bg-blue-50">
                <MapPin className="h-3 w-3 mr-1" />
                {statusSummary['one-to-one']} 1-to-1
              </Badge>
            </div>
            <div className="text-center">
              <Badge variant="outline" className="bg-yellow-50">
                <Zap className="h-3 w-3 mr-1" />
                {statusSummary['ai-transform']} AI Transform
              </Badge>
            </div>
            <div className="text-center">
              <Badge variant="outline" className="bg-purple-50">
                <Type className="h-3 w-3 mr-1" />
                {statusSummary['string-overwrite']} String
              </Badge>
            </div>
            <div className="text-center">
              <Badge variant="outline" className="bg-gray-50">
                <EyeOff className="h-3 w-3 mr-1" />
                {statusSummary['deactivated']} Deactivated
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Column Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Column Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-4">
            <div className="flex-1">
              <Label>Select Column to Configure</Label>
              <Select value={selectedColumn} onValueChange={setSelectedColumn}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a column to configure" />
                </SelectTrigger>
                <SelectContent>
                  {gridConfig.targetColumns.map(col => {
                    const mode = transformationModes[col.Column_name];
                    return (
                      <SelectItem key={col.Column_name} value={col.Column_name}>
                        <div className="flex items-center space-x-2">
                          {mode?.type === '1-to-1' && <MapPin className="h-4 w-4 text-blue-500" />}
                          {mode?.type === 'ai-transform' && <Zap className="h-4 w-4 text-yellow-500" />}
                          {mode?.type === 'string-overwrite' && <Type className="h-4 w-4 text-purple-500" />}
                          {mode?.type === 'deactivated' && <EyeOff className="h-4 w-4 text-gray-500" />}
                          <span>{col.Column_name}</span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <Button
              onClick={() => setShowColumnConfig(!showColumnConfig)}
              variant="outline"
              disabled={!selectedColumn}
            >
              <Settings className="h-4 w-4 mr-2" />
              Configure
            </Button>
          </div>
          
          {showColumnConfig && renderColumnConfiguration()}
        </CardContent>
      </Card>

      {/* High-Performance Data Grid */}
      <Card>
        <CardHeader>
          <CardTitle>Data Grid - Configuration-Driven View</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="ag-theme-alpine w-full" style={{ height: '600px' }}>
            <AgGridReact
              ref={gridRef}
              rowData={rowData}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              onCellValueChanged={onCellValueChanged}
              
              // High-performance settings
              animateRows={false}
              
              // Default column settings
              defaultColDef={{
                sortable: true,
                filter: true,
                resizable: true,
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 