"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { 
  Plus, 
  Search, 
  Filter, 
  RefreshCw,
  FileSpreadsheet,
  Grid3X3,
  List
} from "lucide-react";
import { JobCard } from "./job-card";
import { JobMetadata, JobFilter, jobService } from "@/lib/job-service";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useImportWizardStore } from "@/stores/import-wizard-store";

interface JobDashboardProps {
  initialJobs?: JobMetadata[];
}

export function JobDashboard({ initialJobs }: JobDashboardProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  
  // State
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [filter, setFilter] = useState<JobFilter>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [newJobName, setNewJobName] = useState('');

  // Queries
  const {
    data: jobsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['jobs', currentPage, statusFilter, searchTerm],
    queryFn: async () => {
      const result = await jobService.listJobs(
        {
          status: statusFilter === 'all' ? undefined : statusFilter,
          search: searchTerm || undefined
        },
        currentPage,
        20
      );
      
      // Debug logging
      console.log('🎯 Job dashboard received data:', result);
      console.log('🔍 Jobs with names:', result.jobs.map((job: JobMetadata) => ({ 
        id: job.id, 
        name: job.name, 
        status: job.status 
      })));
      
      return result;
    },
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
    initialData: initialJobs ? { 
      jobs: initialJobs, 
      total: initialJobs.length, 
      page: 1, 
      totalPages: 1 
    } : undefined,
    retry: (failureCount, error) => {
      // Don't retry JSON parsing errors more than once
      if (error instanceof Error && error.message.includes('JSON')) {
        return failureCount < 1;
      }
      return failureCount < 3;
    }
  });

  // Mutations
  const createJobMutation = useMutation({
    mutationFn: (data: { name: string; source_type: 'file_upload' | 'google_sheets' }) =>
      jobService.createJob(data),
    onSuccess: (newJob) => {
      console.log('Job created successfully:', newJob);
      queryClient.invalidateQueries({ queryKey: ['jobs'] });
      queryClient.refetchQueries({ queryKey: ['jobs'] });
      setShowCreateDialog(false);
      setNewJobName('');
      toast.success('Job created successfully');
      // Clear the wizard store before navigating to ensure clean state
      const { reset } = useImportWizardStore.getState();
      reset();
      // Use the correct job ID from the response
      router.push(`/import-wizard?job_id=${newJob.id}`);
    },
    onError: (error) => {
      console.error('Failed to create job:', error);
      toast.error('Failed to create job');
    }
  });

  // Handlers
  const handleJobAction = async (action: string, jobId: string, data?: any) => {
    try {
      switch (action) {
        case 'rename':
          await jobService.renameJob(jobId, data.name);
          queryClient.invalidateQueries({ queryKey: ['jobs'] });
          queryClient.refetchQueries({ queryKey: ['jobs'] });
          break;
        case 'duplicate':
          await jobService.duplicateJob(jobId);
          queryClient.invalidateQueries({ queryKey: ['jobs'] });
          queryClient.refetchQueries({ queryKey: ['jobs'] });
          toast.success('Job duplicated successfully');
          break;
        case 'delete':
          try {
            console.log(`Dashboard: Attempting to delete job ${jobId}`);
            
            // Optimistic update - remove the job from the cache immediately
            queryClient.setQueryData(['jobs', currentPage, statusFilter, searchTerm], (oldData: any) => {
              if (!oldData) return oldData;
              return {
                ...oldData,
                jobs: oldData.jobs.filter((job: any) => job.id !== jobId),
                total: oldData.total - 1
              };
            });
            
            await jobService.deleteJob(jobId);
            console.log(`Dashboard: Successfully called delete for job ${jobId}`);
            
            // Invalidate and refetch to ensure consistency
            queryClient.invalidateQueries({ queryKey: ['jobs'] });
            
            toast.success('Job deleted successfully');
          } catch (error) {
            console.error('Delete job error:', error);
            
            // Revert optimistic update on error
            queryClient.invalidateQueries({ queryKey: ['jobs'] });
            
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
              toast.error('Job not found - it may have already been deleted');
            } else {
              toast.error('Failed to delete job: ' + errorMessage);
            }
          }
          break;
        case 'archive':
          await jobService.archiveJob(jobId);
          queryClient.invalidateQueries({ queryKey: ['jobs'] });
          queryClient.refetchQueries({ queryKey: ['jobs'] });
          toast.success('Job archived successfully');
          break;
        case 'export':
          if (data.format === 'excel') {
            const blob = await jobService.exportJobToExcel(jobId);
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `job-${jobId}.xlsx`;
            a.click();
            URL.revokeObjectURL(url);
          } else if (data.format === 'csv') {
            const blob = await jobService.exportJobToCSV(jobId);
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `job-${jobId}.csv`;
            a.click();
            URL.revokeObjectURL(url);
          }
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} job:`, error);
      throw error;
    }
  };

  const handleJobOpen = (jobId: string) => {
    router.push(`/import-wizard?job_id=${jobId}`);
  };

  const handleCreateJob = async () => {
    if (!newJobName.trim()) return;
    
    createJobMutation.mutate({
      name: newJobName.trim(),
      source_type: 'file_upload'
    });
  };

  const statusOptions = [
    { label: 'All Status', value: 'all' },
    { label: 'Draft', value: 'draft' },
    { label: 'Data Uploaded', value: 'data-uploaded' },
    { label: 'Processing', value: 'processing' },
    { label: 'Completed', value: 'completed' },
    { label: 'Error', value: 'error' }
  ];

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            Failed to load jobs. Please try again.
            <Button variant="outline" onClick={() => refetch()} className="ml-4">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Import Jobs</h1>
          <p className="text-muted-foreground">
            Manage your data import jobs and track their progress
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create New Job
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search jobs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Jobs Grid/List */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : !jobsData || jobsData.jobs.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <FileSpreadsheet className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No jobs found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Try adjusting your filters or search terms'
                  : 'Create your first import job to get started'
                }
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create New Job
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Jobs Display */}
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
              : 'grid-cols-1'
          }`}>
            {jobsData.jobs.map((job: JobMetadata) => (
              <JobCard
                key={job.id}
                job={job}
                onJobAction={handleJobAction}
                onJobOpen={handleJobOpen}
              />
            ))}
          </div>

          {/* Pagination */}
          {jobsData.totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Page {currentPage} of {jobsData.totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, jobsData.totalPages))}
                disabled={currentPage === jobsData.totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}

      {/* Create Job Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Import Job</DialogTitle>
            <DialogDescription>
              Enter a name for your new import job. You'll be able to upload data after creation.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="job-name">Job Name</Label>
              <Input
                id="job-name"
                value={newJobName}
                onChange={(e) => setNewJobName(e.target.value)}
                placeholder="e.g., Product Import Q4 2024"
                disabled={createJobMutation.isPending}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowCreateDialog(false)}
              disabled={createJobMutation.isPending}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleCreateJob}
              disabled={createJobMutation.isPending || !newJobName.trim()}
            >
              {createJobMutation.isPending ? 'Creating...' : 'Create Job'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 