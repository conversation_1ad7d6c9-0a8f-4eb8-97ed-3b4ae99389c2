"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>oot<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  MoreHorizontal, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  FileText, 
  Download,
  Edit,
  Copy,
  Archive,
  Trash2,
  Play
} from "lucide-react";
import { JobMetadata } from "@/lib/job-service";
import { toast } from "sonner";

interface JobCardProps {
  job: JobMetadata;
  onJobAction: (action: string, jobId: string, data?: any) => void;
  onJobOpen: (jobId: string) => void;
}

export function JobCard({ job, onJobAction, onJobOpen }: JobCardProps) {
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [newName, setNewName] = useState(job.name);
  const [isLoading, setIsLoading] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'data-uploaded':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'processing':
        return Clock;
      case 'error':
        return AlertCircle;
      case 'data-uploaded':
        return FileText;
      case 'draft':
        return Edit;
      default:
        return Clock;
    }
  };

  const handleRename = async () => {
    if (newName.trim() === job.name) {
      setShowRenameDialog(false);
      return;
    }

    setIsLoading(true);
    try {
      await onJobAction('rename', job.id, { name: newName.trim() });
      setShowRenameDialog(false);
      toast.success('Job renamed successfully');
    } catch (error) {
      toast.error('Failed to rename job');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      await onJobAction('delete', job.id);
      setShowDeleteDialog(false);
      toast.success('Job deleted successfully');
    } catch (error) {
      toast.error('Failed to delete job');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async (format: 'excel' | 'csv') => {
    try {
      await onJobAction('export', job.id, { format });
      toast.success(`Job exported to ${format.toUpperCase()}`);
    } catch (error) {
      toast.error(`Failed to export job to ${format.toUpperCase()}`);
    }
  };

  const StatusIcon = getStatusIcon(job.status);
  const progressPercentage = job.progress.total_cells > 0 
    ? (job.progress.processed_cells / job.progress.total_cells) * 100 
    : 0;

  return (
    <>
      <Card className="hover:shadow-md transition-shadow cursor-pointer">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg truncate" title={job.name}>
              {job.name}
            </CardTitle>
            <CardDescription className="flex items-center gap-2">
              <StatusIcon className="h-4 w-4" />
              {job.original_filename}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getStatusColor(job.status)}>
              {job.status.replace('-', ' ')}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onJobOpen(job.id)}>
                  <Play className="mr-2 h-4 w-4" />
                  Open
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setShowRenameDialog(true)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onJobAction('duplicate', job.id)}>
                  <Copy className="mr-2 h-4 w-4" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleExport('excel')}>
                  <Download className="mr-2 h-4 w-4" />
                  Export Excel
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('csv')}>
                  <Download className="mr-2 h-4 w-4" />
                  Export CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onJobAction('archive', job.id)}>
                  <Archive className="mr-2 h-4 w-4" />
                  Archive
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Rows:</span>
              <span className="ml-2 font-medium">{job.row_count.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Columns:</span>
              <span className="ml-2 font-medium">{job.column_count}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Processed:</span>
              <span className="ml-2 font-medium">{job.processed_rows.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Source:</span>
              <span className="ml-2 font-medium capitalize">
                {job.source_type.replace('_', ' ')}
              </span>
            </div>
          </div>

          {job.status === 'processing' && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Progress:</span>
                <span className="font-medium">
                  {job.progress.processed_cells}/{job.progress.total_cells} cells
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
              
              {job.progress.estimated_completion && (
                <p className="text-xs text-muted-foreground">
                  ETA: {new Date(job.progress.estimated_completion).toLocaleString()}
                </p>
              )}
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            <div>Created: {new Date(job.created_at).toLocaleDateString()}</div>
            <div>Updated: {new Date(job.updated_at).toLocaleDateString()}</div>
          </div>
        </CardContent>

        <CardFooter className="pt-0">
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={() => onJobOpen(job.id)}
          >
            <Play className="mr-2 h-4 w-4" />
            Open Job
          </Button>
        </CardFooter>
      </Card>

      {/* Rename Dialog */}
      <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Job</DialogTitle>
            <DialogDescription>
              Enter a new name for this import job.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="job-name">Job Name</Label>
              <Input
                id="job-name"
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
                placeholder="Enter job name..."
                disabled={isLoading}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowRenameDialog(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleRename}
              disabled={isLoading || !newName.trim()}
            >
              {isLoading ? 'Renaming...' : 'Rename'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Job</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{job.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteDialog(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 