"use client";

import { useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { 
  Settings, 
  Eye, 
  EyeOff, 
  Move, 
  Maximize2, 
  Plus,
  Trash2,
  Save,
  RotateCcw,
  Filter,
  ArrowUp,
  ArrowDown,
  GripVertical
} from "lucide-react";

interface ColumnConfig {
  id: string;
  field: string;
  headerName: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  visible: boolean;
  width: number;
  minWidth: number;
  maxWidth: number;
  resizable: boolean;
  sortable: boolean;
  filterable: boolean;
  editable: boolean;
  pinned?: 'left' | 'right' | null;
  order: number;
}

interface ColumnGroup {
  id: string;
  name: string;
  columns: string[];
  collapsed: boolean;
}

interface ColumnManagerProps {
  columns: ColumnConfig[];
  groups?: ColumnGroup[];
  onColumnsChange: (columns: ColumnConfig[]) => void;
  onGroupsChange?: (groups: ColumnGroup[]) => void;
  onSaveConfiguration?: () => Promise<void>;
  onResetConfiguration?: () => Promise<void>;
}

export function ColumnManager({
  columns,
  groups = [],
  onColumnsChange,
  onGroupsChange,
  onSaveConfiguration,
  onResetConfiguration
}: ColumnManagerProps) {
  const [selectedColumn, setSelectedColumn] = useState<string | null>(null);
  const [editingColumn, setEditingColumn] = useState<ColumnConfig | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [saving, setSaving] = useState(false);

  // Filter columns based on search and type
  const filteredColumns = columns.filter(column => {
    const matchesSearch = column.headerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         column.field.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === "all" || 
                       (filterType === "visible" && column.visible) ||
                       (filterType === "hidden" && !column.visible) ||
                       (filterType === column.type);
    return matchesSearch && matchesType;
  });

  // Toggle column visibility
  const toggleColumnVisibility = useCallback((columnId: string) => {
    const updatedColumns = columns.map(col =>
      col.id === columnId ? { ...col, visible: !col.visible } : col
    );
    onColumnsChange(updatedColumns);
    toast.success(`Column ${updatedColumns.find(c => c.id === columnId)?.visible ? 'shown' : 'hidden'}`);
  }, [columns, onColumnsChange]);

  // Update column width
  const updateColumnWidth = useCallback((columnId: string, width: number) => {
    const updatedColumns = columns.map(col =>
      col.id === columnId ? { ...col, width: Math.max(col.minWidth, Math.min(col.maxWidth, width)) } : col
    );
    onColumnsChange(updatedColumns);
  }, [columns, onColumnsChange]);

  // Move column order
  const moveColumn = useCallback((columnId: string, direction: 'up' | 'down') => {
    const columnIndex = columns.findIndex(col => col.id === columnId);
    if (columnIndex === -1) return;

    const newIndex = direction === 'up' ? columnIndex - 1 : columnIndex + 1;
    if (newIndex < 0 || newIndex >= columns.length) return;

    const updatedColumns = [...columns];
    [updatedColumns[columnIndex], updatedColumns[newIndex]] = [updatedColumns[newIndex], updatedColumns[columnIndex]];
    
    // Update order indices
    updatedColumns.forEach((col, index) => {
      col.order = index;
    });

    onColumnsChange(updatedColumns);
    toast.success(`Column moved ${direction}`);
  }, [columns, onColumnsChange]);

  // Pin/unpin column
  const toggleColumnPin = useCallback((columnId: string, pin: 'left' | 'right' | null) => {
    const updatedColumns = columns.map(col =>
      col.id === columnId ? { ...col, pinned: col.pinned === pin ? null : pin } : col
    );
    onColumnsChange(updatedColumns);
    
    const column = updatedColumns.find(c => c.id === columnId);
    const pinStatus = column?.pinned ? `pinned ${column.pinned}` : 'unpinned';
    toast.success(`Column ${pinStatus}`);
  }, [columns, onColumnsChange]);

  // Save column configuration
  const handleSaveConfiguration = useCallback(async () => {
    if (!onSaveConfiguration) return;
    
    setSaving(true);
    try {
      await onSaveConfiguration();
      toast.success('Column configuration saved');
    } catch (error) {
      console.error('Failed to save configuration:', error);
      toast.error('Failed to save configuration');
    } finally {
      setSaving(false);
    }
  }, [onSaveConfiguration]);

  // Reset to default configuration
  const handleResetConfiguration = useCallback(async () => {
    if (!onResetConfiguration) return;
    
    try {
      await onResetConfiguration();
      toast.success('Configuration reset to defaults');
    } catch (error) {
      console.error('Failed to reset configuration:', error);
      toast.error('Failed to reset configuration');
    }
  }, [onResetConfiguration]);

  // Edit column dialog
  const handleEditColumn = useCallback((column: ColumnConfig) => {
    setEditingColumn({ ...column });
  }, []);

  // Save edited column
  const handleSaveEditedColumn = useCallback(() => {
    if (!editingColumn) return;

    const updatedColumns = columns.map(col =>
      col.id === editingColumn.id ? editingColumn : col
    );
    onColumnsChange(updatedColumns);
    setEditingColumn(null);
    toast.success('Column updated');
  }, [editingColumn, columns, onColumnsChange]);

  // Get column type badge color
  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'text': return 'bg-blue-100 text-blue-800';
      case 'number': return 'bg-green-100 text-green-800';
      case 'date': return 'bg-purple-100 text-purple-800';
      case 'boolean': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      {/* Header Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span>Column Management</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                onClick={handleResetConfiguration} 
                variant="outline" 
                size="sm"
                disabled={!onResetConfiguration}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button 
                onClick={handleSaveConfiguration} 
                size="sm"
                disabled={saving || !onSaveConfiguration}
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search Columns</Label>
              <Input
                id="search"
                placeholder="Search by name or field..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="filter">Filter by Type</Label>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="visible">Visible Only</SelectItem>
                  <SelectItem value="hidden">Hidden Only</SelectItem>
                  <SelectItem value="text">Text</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="boolean">Boolean</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <div className="text-sm text-gray-500">
                Showing {filteredColumns.length} of {columns.length} columns
                <br />
                {columns.filter(c => c.visible).length} visible
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Column List */}
      <Card>
        <CardHeader>
          <CardTitle>Column Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {filteredColumns.map((column, index) => (
              <div
                key={column.id}
                className={`p-4 border rounded-lg transition-colors ${
                  selectedColumn === column.id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex flex-col space-y-1">
                      <Button
                        onClick={() => moveColumn(column.id, 'up')}
                        variant="ghost"
                        size="sm"
                        disabled={index === 0}
                        className="h-6 w-6 p-0"
                      >
                        <ArrowUp className="h-3 w-3" />
                      </Button>
                      <Button
                        onClick={() => moveColumn(column.id, 'down')}
                        variant="ghost"
                        size="sm"
                        disabled={index === filteredColumns.length - 1}
                        className="h-6 w-6 p-0"
                      >
                        <ArrowDown className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <GripVertical className="h-4 w-4 text-gray-400" />
                    
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{column.headerName}</span>
                        <Badge className={getTypeBadgeColor(column.type)}>
                          {column.type}
                        </Badge>
                        {column.pinned && (
                          <Badge variant="outline">
                            Pinned {column.pinned}
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        Field: {column.field} | Width: {column.width}px
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      onClick={() => toggleColumnVisibility(column.id)}
                      variant="ghost"
                      size="sm"
                      className={column.visible ? 'text-green-600' : 'text-gray-400'}
                    >
                      {column.visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </Button>
                    
                    <Select
                      value={column.pinned || 'none'}
                      onValueChange={(value) => toggleColumnPin(column.id, value === 'none' ? null : value as 'left' | 'right')}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                      </SelectContent>
                    </Select>

                    <div className="flex items-center space-x-1">
                      <Input
                        type="number"
                        value={column.width}
                        onChange={(e) => updateColumnWidth(column.id, parseInt(e.target.value) || column.width)}
                        className="w-20"
                        min={column.minWidth}
                        max={column.maxWidth}
                      />
                      <span className="text-xs text-gray-500">px</span>
                    </div>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          onClick={() => handleEditColumn(column)}
                          variant="ghost"
                          size="sm"
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Edit Column: {column.headerName}</DialogTitle>
                        </DialogHeader>
                        {editingColumn && (
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="headerName">Header Name</Label>
                              <Input
                                id="headerName"
                                value={editingColumn.headerName}
                                onChange={(e) => setEditingColumn({ ...editingColumn, headerName: e.target.value })}
                              />
                            </div>
                            
                            <div>
                              <Label htmlFor="field">Field</Label>
                              <Input
                                id="field"
                                value={editingColumn.field}
                                onChange={(e) => setEditingColumn({ ...editingColumn, field: e.target.value })}
                              />
                            </div>

                            <div>
                              <Label htmlFor="type">Type</Label>
                              <Select
                                value={editingColumn.type}
                                onValueChange={(value: any) => setEditingColumn({ ...editingColumn, type: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="text">Text</SelectItem>
                                  <SelectItem value="number">Number</SelectItem>
                                  <SelectItem value="date">Date</SelectItem>
                                  <SelectItem value="boolean">Boolean</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="grid grid-cols-3 gap-4">
                              <div>
                                <Label htmlFor="width">Width</Label>
                                <Input
                                  id="width"
                                  type="number"
                                  value={editingColumn.width}
                                  onChange={(e) => setEditingColumn({ 
                                    ...editingColumn, 
                                    width: parseInt(e.target.value) || editingColumn.width 
                                  })}
                                />
                              </div>
                              <div>
                                <Label htmlFor="minWidth">Min Width</Label>
                                <Input
                                  id="minWidth"
                                  type="number"
                                  value={editingColumn.minWidth}
                                  onChange={(e) => setEditingColumn({ 
                                    ...editingColumn, 
                                    minWidth: parseInt(e.target.value) || editingColumn.minWidth 
                                  })}
                                />
                              </div>
                              <div>
                                <Label htmlFor="maxWidth">Max Width</Label>
                                <Input
                                  id="maxWidth"
                                  type="number"
                                  value={editingColumn.maxWidth}
                                  onChange={(e) => setEditingColumn({ 
                                    ...editingColumn, 
                                    maxWidth: parseInt(e.target.value) || editingColumn.maxWidth 
                                  })}
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id="resizable"
                                  checked={editingColumn.resizable}
                                  onChange={(e) => setEditingColumn({ ...editingColumn, resizable: e.target.checked })}
                                />
                                <Label htmlFor="resizable">Resizable</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id="sortable"
                                  checked={editingColumn.sortable}
                                  onChange={(e) => setEditingColumn({ ...editingColumn, sortable: e.target.checked })}
                                />
                                <Label htmlFor="sortable">Sortable</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id="filterable"
                                  checked={editingColumn.filterable}
                                  onChange={(e) => setEditingColumn({ ...editingColumn, filterable: e.target.checked })}
                                />
                                <Label htmlFor="filterable">Filterable</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id="editable"
                                  checked={editingColumn.editable}
                                  onChange={(e) => setEditingColumn({ ...editingColumn, editable: e.target.checked })}
                                />
                                <Label htmlFor="editable">Editable</Label>
                              </div>
                            </div>

                            <div className="flex justify-end space-x-2">
                              <Button variant="outline" onClick={() => setEditingColumn(null)}>
                                Cancel
                              </Button>
                              <Button onClick={handleSaveEditedColumn}>
                                Save Changes
                              </Button>
                            </div>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              onClick={() => {
                const updatedColumns = columns.map(col => ({ ...col, visible: true }));
                onColumnsChange(updatedColumns);
                toast.success('All columns shown');
              }}
              variant="outline"
              className="w-full"
            >
              <Eye className="h-4 w-4 mr-2" />
              Show All
            </Button>
            
            <Button
              onClick={() => {
                const updatedColumns = columns.map(col => ({ ...col, visible: false }));
                onColumnsChange(updatedColumns);
                toast.success('All columns hidden');
              }}
              variant="outline"
              className="w-full"
            >
              <EyeOff className="h-4 w-4 mr-2" />
              Hide All
            </Button>
            
            <Button
              onClick={() => {
                const updatedColumns = columns.map(col => ({ ...col, width: 150 }));
                onColumnsChange(updatedColumns);
                toast.success('All column widths reset');
              }}
              variant="outline"
              className="w-full"
            >
                              <Maximize2 className="h-4 w-4 mr-2" />
              Reset Widths
            </Button>
            
            <Button
              onClick={() => {
                const updatedColumns = columns.map(col => ({ ...col, pinned: null }));
                onColumnsChange(updatedColumns);
                toast.success('All columns unpinned');
              }}
              variant="outline"
              className="w-full"
            >
              <Move className="h-4 w-4 mr-2" />
              Unpin All
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}