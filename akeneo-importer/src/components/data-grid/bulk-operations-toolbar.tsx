"use client";

import { useState, useCallback } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { 
  Play, 
  Square, 
  RefreshCw, 
  Download,
  Upload,
  Zap,
  Filter,
  CheckSquare,
  X,
  Loader2,
  BarChart3,
  Alert<PERSON>riangle,
  Settings
} from "lucide-react";

interface BulkOperation {
  id: string;
  type: 'transform' | 'validate' | 'export' | 'import';
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  totalRows: number;
  processedRows: number;
  failedRows: number;
  startTime?: Date;
  endTime?: Date;
  result?: any;
  error?: string;
}

interface BulkTransformConfig {
  operation: 'ai-transform' | 'string-replace' | 'format' | 'validate';
  columns: string[];
  aiPrompt?: string;
  findValue?: string;
  replaceValue?: string;
  format?: string;
  validationRules?: any;
}

interface BulkOperationsToolbarProps {
  jobId?: string;
  selectedRows: any[];
  selectedColumns: string[];
  availableColumns: { field: string; headerName: string; type: string }[];
  onOperationComplete?: (operation: BulkOperation) => void;
  onProgressUpdate?: (operationId: string, progress: number) => void;
}

export function BulkOperationsToolbar({
  jobId,
  selectedRows,
  selectedColumns,
  availableColumns,
  onOperationComplete,
  onProgressUpdate
}: BulkOperationsToolbarProps) {
  const [operations, setOperations] = useState<BulkOperation[]>([]);
  const [currentOperation, setCurrentOperation] = useState<BulkOperation | null>(null);
  const [transformConfig, setTransformConfig] = useState<BulkTransformConfig>({
    operation: 'ai-transform',
    columns: [],
    aiPrompt: ''
  });
  const [showOperationDialog, setShowOperationDialog] = useState(false);

  // Start bulk operation
  const startBulkOperation = useCallback(async (config: BulkTransformConfig) => {
    const operationId = `op_${Date.now()}`;
    
    const newOperation: BulkOperation = {
      id: operationId,
      type: 'transform',
      name: `Bulk ${config.operation}`,
      description: `${config.operation} on ${config.columns.length} columns`,
      status: 'running',
      progress: 0,
      totalRows: selectedRows.length,
      processedRows: 0,
      failedRows: 0,
      startTime: new Date()
    };

    setOperations(prev => [...prev, newOperation]);
    setCurrentOperation(newOperation);

    try {
      const response = await fetch('/api/grid/bulk-operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId,
          operationId,
          type: config.operation,
          config: {
            columns: config.columns,
            rowIds: selectedRows.map(row => row.id),
            aiPrompt: config.aiPrompt,
            findValue: config.findValue,
            replaceValue: config.replaceValue,
            format: config.format,
            validationRules: config.validationRules
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to start bulk operation');
      }

      // Start progress monitoring
      monitorOperationProgress(operationId);
      
      toast.success(`Bulk operation started: ${newOperation.name}`);
      setShowOperationDialog(false);

    } catch (error) {
      console.error('Failed to start bulk operation:', error);
      
      // Update operation status to failed
      setOperations(prev => prev.map(op =>
        op.id === operationId
          ? { ...op, status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
          : op
      ));
      
      toast.error('Failed to start bulk operation');
    }
  }, [selectedRows, jobId]);

  // Monitor operation progress
  const monitorOperationProgress = useCallback(async (operationId: string) => {
    const checkProgress = async () => {
      try {
        const response = await fetch(`/api/grid/bulk-operations/${operationId}/status`);
        if (!response.ok) return;

        const status = await response.json();
        
        setOperations(prev => prev.map(op =>
          op.id === operationId
            ? {
                ...op,
                status: status.status,
                progress: status.progress,
                processedRows: status.processedRows,
                failedRows: status.failedRows,
                endTime: status.status === 'completed' || status.status === 'failed' ? new Date() : undefined,
                result: status.result,
                error: status.error
              }
            : op
        ));

        onProgressUpdate?.(operationId, status.progress);

        // Continue monitoring if still running
        if (status.status === 'running') {
          setTimeout(checkProgress, 1000);
        } else {
          // Operation completed
          const finalOperation = operations.find(op => op.id === operationId);
          if (finalOperation) {
            onOperationComplete?.({ ...finalOperation, ...status });
          }
          
          if (currentOperation?.id === operationId) {
            setCurrentOperation(null);
          }
        }

      } catch (error) {
        console.error('Failed to check operation progress:', error);
      }
    };

    checkProgress();
  }, [operations, currentOperation, onOperationComplete, onProgressUpdate]);

  // Cancel operation
  const cancelOperation = useCallback(async (operationId: string) => {
    try {
      const response = await fetch(`/api/grid/bulk-operations/${operationId}/cancel`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Failed to cancel operation');
      }

      setOperations(prev => prev.map(op =>
        op.id === operationId
          ? { ...op, status: 'cancelled', endTime: new Date() }
          : op
      ));

      if (currentOperation?.id === operationId) {
        setCurrentOperation(null);
      }

      toast.success('Operation cancelled');

    } catch (error) {
      console.error('Failed to cancel operation:', error);
      toast.error('Failed to cancel operation');
    }
  }, [currentOperation]);

  // Clear completed operations
  const clearCompletedOperations = useCallback(() => {
    setOperations(prev => prev.filter(op => 
      op.status === 'running' || op.status === 'pending'
    ));
    toast.success('Cleared completed operations');
  }, []);

  // Export operation results
  const exportResults = useCallback(async (operationId: string) => {
    try {
      const response = await fetch(`/api/grid/bulk-operations/${operationId}/export`);
      if (!response.ok) {
        throw new Error('Failed to export results');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `bulk_operation_${operationId}_results.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Results exported successfully');

    } catch (error) {
      console.error('Failed to export results:', error);
      toast.error('Failed to export results');
    }
  }, []);

  // Get operation status badge
  const getStatusBadge = (status: BulkOperation['status']) => {
    switch (status) {
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800">Running</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    }
  };

  // Format duration
  const formatDuration = (startTime?: Date, endTime?: Date) => {
    if (!startTime) return '—';
    const end = endTime || new Date();
    const duration = Math.floor((end.getTime() - startTime.getTime()) / 1000);
    
    if (duration < 60) return `${duration}s`;
    if (duration < 3600) return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
  };

  const runningOperations = operations.filter(op => op.status === 'running');
  const completedOperations = operations.filter(op => op.status === 'completed' || op.status === 'failed');

  return (
    <div className="space-y-4">
      {/* Selection Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CheckSquare className="h-4 w-4" />
              <span>Bulk Operations</span>
            </div>
            <Button
              onClick={clearCompletedOperations}
              variant="outline"
              size="sm"
              disabled={completedOperations.length === 0}
            >
              Clear Completed
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {selectedRows.length.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Selected Rows</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {selectedColumns.length}
              </div>
              <div className="text-xs text-gray-500">Selected Columns</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {runningOperations.length}
              </div>
              <div className="text-xs text-gray-500">Running</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {completedOperations.length}
              </div>
              <div className="text-xs text-gray-500">Completed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Dialog open={showOperationDialog} onOpenChange={setShowOperationDialog}>
              <DialogTrigger asChild>
                <Button 
                  className="w-full"
                  disabled={selectedRows.length === 0 || selectedColumns.length === 0}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  AI Transform
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Bulk AI Transformation</DialogTitle>
                  <DialogDescription>
                    Configure AI transformation for {selectedRows.length} rows across {selectedColumns.length} columns
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div>
                    <Label>Operation Type</Label>
                    <Select
                      value={transformConfig.operation}
                      onValueChange={(value: any) => setTransformConfig({ ...transformConfig, operation: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ai-transform">AI Transform</SelectItem>
                        <SelectItem value="string-replace">String Replace</SelectItem>
                        <SelectItem value="format">Format Data</SelectItem>
                        <SelectItem value="validate">Validate Data</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Target Columns</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedColumns.map(columnId => {
                        const column = availableColumns.find(c => c.field === columnId);
                        return (
                          <Badge key={columnId} variant="outline">
                            {column?.headerName || columnId}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>

                  {transformConfig.operation === 'ai-transform' && (
                    <div>
                      <Label>AI Transformation Prompt</Label>
                      <Textarea
                        value={transformConfig.aiPrompt}
                        onChange={(e) => setTransformConfig({ ...transformConfig, aiPrompt: e.target.value })}
                        placeholder="Describe how you want to transform the data..."
                        className="min-h-[100px]"
                      />
                    </div>
                  )}

                  {transformConfig.operation === 'string-replace' && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Find</Label>
                        <Input
                          value={transformConfig.findValue || ''}
                          onChange={(e) => setTransformConfig({ ...transformConfig, findValue: e.target.value })}
                          placeholder="Text to find..."
                        />
                      </div>
                      <div>
                        <Label>Replace With</Label>
                        <Input
                          value={transformConfig.replaceValue || ''}
                          onChange={(e) => setTransformConfig({ ...transformConfig, replaceValue: e.target.value })}
                          placeholder="Replacement text..."
                        />
                      </div>
                    </div>
                  )}

                  {transformConfig.operation === 'format' && (
                    <div>
                      <Label>Format Pattern</Label>
                      <Input
                        value={transformConfig.format || ''}
                        onChange={(e) => setTransformConfig({ ...transformConfig, format: e.target.value })}
                        placeholder="e.g., {value:uppercase}, {value:date:YYYY-MM-DD}"
                      />
                    </div>
                  )}

                  <div className="flex justify-end space-x-2">
                    <Button 
                      variant="outline" 
                      onClick={() => setShowOperationDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={() => startBulkOperation(transformConfig)}
                      disabled={
                        (transformConfig.operation === 'ai-transform' && !transformConfig.aiPrompt) ||
                        (transformConfig.operation === 'string-replace' && (!transformConfig.findValue || !transformConfig.replaceValue)) ||
                        (transformConfig.operation === 'format' && !transformConfig.format)
                      }
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Operation
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Button 
              variant="outline" 
              className="w-full"
              disabled={selectedRows.length === 0}
            >
              <Filter className="h-4 w-4 mr-2" />
              Validate Data
            </Button>

            <Button 
              variant="outline" 
              className="w-full"
              disabled={selectedRows.length === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              Export Selection
            </Button>

            <Button 
              variant="outline" 
              className="w-full"
              disabled={selectedRows.length === 0}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Operation */}
      {currentOperation && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Current Operation: {currentOperation.name}</span>
              </div>
              <Button
                onClick={() => cancelOperation(currentOperation.id)}
                variant="outline"
                size="sm"
              >
                <Square className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Progress value={currentOperation.progress} className="w-full" />
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Progress:</span>
                  <div className="font-medium">{currentOperation.progress.toFixed(1)}%</div>
                </div>
                <div>
                  <span className="text-gray-500">Processed:</span>
                  <div className="font-medium">
                    {currentOperation.processedRows.toLocaleString()} / {currentOperation.totalRows.toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Failed:</span>
                  <div className="font-medium text-red-600">{currentOperation.failedRows.toLocaleString()}</div>
                </div>
                <div>
                  <span className="text-gray-500">Duration:</span>
                  <div className="font-medium">{formatDuration(currentOperation.startTime)}</div>
                </div>
              </div>

              <div className="text-sm text-gray-600">
                {currentOperation.description}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Operation History */}
      {operations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4" />
              <span>Operation History</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {operations.map(operation => (
                <div
                  key={operation.id}
                  className="p-4 border rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{operation.name}</span>
                          {getStatusBadge(operation.status)}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                          {operation.description} • {formatDuration(operation.startTime, operation.endTime)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {operation.status === 'completed' && (
                        <Button
                          onClick={() => exportResults(operation.id)}
                          variant="ghost"
                          size="sm"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                      
                      {operation.status === 'running' && (
                        <Button
                          onClick={() => cancelOperation(operation.id)}
                          variant="ghost"
                          size="sm"
                        >
                          <Square className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {operation.status === 'running' && (
                    <div className="mt-3">
                      <Progress value={operation.progress} className="w-full h-2" />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{operation.processedRows} / {operation.totalRows} processed</span>
                        <span>{operation.progress.toFixed(1)}%</span>
                      </div>
                    </div>
                  )}

                  {operation.status === 'failed' && operation.error && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-4 w-4" />
                        <span>Error: {operation.error}</span>
                      </div>
                    </div>
                  )}

                  {operation.status === 'completed' && (
                    <div className="mt-2 grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Processed:</span>
                        <div className="font-medium text-green-600">{operation.processedRows.toLocaleString()}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Failed:</span>
                        <div className="font-medium text-red-600">{operation.failedRows.toLocaleString()}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Success Rate:</span>
                        <div className="font-medium">
                          {((operation.processedRows - operation.failedRows) / operation.processedRows * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}