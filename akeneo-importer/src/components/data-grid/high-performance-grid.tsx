// @ts-nocheck
"use client";

import { useRef, useState, useEffect, useMemo, useCallback } from "react";
import { AgGridReact } from 'ag-grid-react';
import { 
  ColDef, 
  GridApi, 
  GridReadyEvent, 
  CellValueChangedEvent,
  IServerSideGetRowsRequest,
  IServerSideGetRowsParams
} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { 
  Activity, 
  RefreshCw, 
  Settings,
  Loader2,
  Filter,
  Eye,
  EyeOff
} from "lucide-react";

interface GridColumn {
  field: string;
  headerName: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  editable: boolean;
  visible: boolean;
  width?: number;
  filter?: string;
  sort?: 'asc' | 'desc';
}

interface GridPerformanceMetrics {
  totalRows: number;
  loadedRows: number;
  renderTime: number;
  memoryUsage: number;
  cacheHitRate: number;
}

interface HighPerformanceGridProps {
  jobId?: string;
  dataEndpoint: string;
  columns: GridColumn[];
  onColumnVisibilityChange?: (columnId: string, visible: boolean) => void;
  onColumnResize?: (columnId: string, width: number) => void;
  onColumnReorder?: (columnOrder: string[]) => void;
  onCellUpdate?: (rowId: string, field: string, value: any) => void;
  enableRealTimeUpdates?: boolean;
  maxCacheSize?: number;
}

export function HighPerformanceGrid({
  jobId,
  dataEndpoint,
  columns,
  onColumnVisibilityChange,
  onColumnResize,
  onColumnReorder,
  onCellUpdate,
  enableRealTimeUpdates = true,
  maxCacheSize = 10000
}: HighPerformanceGridProps) {
  const gridRef = useRef<AgGridReact>(null);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [loading, setLoading] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<Set<string>>(
    new Set(columns.filter(col => col.visible).map(col => col.field))
  );
  const [performance, setPerformance] = useState<GridPerformanceMetrics>({
    totalRows: 0,
    loadedRows: 0,
    renderTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0
  });

  // Performance monitoring
  const updatePerformanceMetrics = useCallback(() => {
    if (!gridApi) return;
    
    const startTime = performance.now();
    
    // Calculate actual performance metrics
    const metrics: GridPerformanceMetrics = {
      totalRows: gridApi.getInfiniteRowCount() || 0,
      loadedRows: gridApi.getDisplayedRowCount(),
      renderTime: performance.now() - startTime,
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
      cacheHitRate: 0 // No cache hit rate available without proper cache implementation
    };
    
    setPerformance(metrics);
  }, [gridApi]);

  // Server-side data source
  const serverSideDatasource = useMemo(() => ({
    getRows: async (params: IServerSideGetRowsParams) => {
      const startTime = performance.now();
      setLoading(true);
      
      try {
        const request: IServerSideGetRowsRequest = params.request;
        
        // Build query parameters
        const queryParams = new URLSearchParams({
          startRow: request.startRow?.toString() || '0',
          endRow: request.endRow?.toString() || '100',
          sortModel: JSON.stringify(request.sortModel || []),
          filterModel: JSON.stringify(request.filterModel || {}),
          groupKeys: JSON.stringify(request.groupKeys || []),
          pivotCols: JSON.stringify(request.pivotCols || []),
          pivotMode: request.pivotMode?.toString() || 'false',
          groupCols: JSON.stringify(request.groupCols || []),
          valueCols: JSON.stringify(request.valueCols || []),
          rowGroupCols: JSON.stringify(request.rowGroupCols || [])
        });

        if (jobId) {
          queryParams.append('jobId', jobId);
        }

        const response = await fetch(`${dataEndpoint}?${queryParams}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }

        const result = await response.json();
        
        // Update performance metrics
        const endTime = performance.now();
        setPerformance(prev => ({
          ...prev,
          renderTime: endTime - startTime,
          loadedRows: result.data?.length || 0,
          totalRows: result.totalRows || 0
        }));

        params.success({
          rowData: result.data || [],
          rowCount: result.totalRows || 0
        });

      } catch (error) {
        console.error('Failed to load grid data:', error);
        toast.error('Failed to load data');
        params.fail();
      } finally {
        setLoading(false);
      }
    }
  }), [dataEndpoint, jobId]);

  // AG Grid column definitions with virtualization
  const columnDefs = useMemo((): ColDef[] => {
    return columns
      .filter(col => visibleColumns.has(col.field))
      .map((col): ColDef => ({
        headerName: col.headerName,
        field: col.field,
        sortable: true,
        filter: true,
        resizable: true,
        editable: col.editable,
        width: col.width || 150,
        minWidth: 100,
        maxWidth: 500,
        
        // Column-specific settings
        cellDataType: col.type === 'number' ? 'number' : 
                     col.type === 'date' ? 'date' : 
                     col.type === 'boolean' ? 'boolean' : 'text',
        
        // Performance optimizations
        enableCellChangeFlash: enableRealTimeUpdates,
        suppressMenu: false,
        
        // Custom cell renderer for performance
        cellRenderer: (params: any) => {
          if (params.value === null || params.value === undefined) {
            return '<span style="color: #ccc;">—</span>';
          }
          return params.value;
        },
        
        // Filter settings
        filterParams: {
          debounceMs: 200, // Debounce filter changes
          maxNumConditions: 2
        }
      }));
  }, [columns, visibleColumns, enableRealTimeUpdates]);

  // Grid ready event handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api);
    
    // Set server-side row model
    params.api.setGridOption('rowModelType', 'serverSide');
    params.api.setGridOption('serverSideDatasource', serverSideDatasource);
    
    // Performance monitoring
    updatePerformanceMetrics();
  }, [serverSideDatasource, updatePerformanceMetrics]);

  // Cell value changed handler
  const onCellValueChanged = useCallback(async (event: CellValueChangedEvent) => {
    const { colDef, newValue, data, rowIndex } = event;
    
    if (!colDef.field || !data) return;
    
    try {
      // Optimistic update
      const response = await fetch('/api/grid/cell-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId,
          rowId: data.id,
          field: colDef.field,
          value: newValue,
          rowIndex
        }),
      });

      if (!response.ok) {
        throw new Error('Cell update failed');
      }

      onCellUpdate?.(data.id, colDef.field, newValue);
      
      // Flash the cell to indicate successful update
      if (enableRealTimeUpdates) {
        event.api.flashCells({ 
          rowNodes: [event.node!], 
          columns: [colDef.field] 
        });
      }

    } catch (error) {
      console.error('Failed to update cell:', error);
      toast.error('Failed to update cell value');
      
      // Revert the change
      event.node?.setDataValue(colDef.field!, event.oldValue);
    }
  }, [jobId, onCellUpdate, enableRealTimeUpdates]);

  // Column visibility toggle
  const toggleColumnVisibility = useCallback((columnId: string) => {
    setVisibleColumns(prev => {
      const newSet = new Set(prev);
      if (newSet.has(columnId)) {
        newSet.delete(columnId);
      } else {
        newSet.add(columnId);
      }
      
      onColumnVisibilityChange?.(columnId, newSet.has(columnId));
      return newSet;
    });
  }, [onColumnVisibilityChange]);

  // Refresh grid data
  const refreshData = useCallback(() => {
    if (gridApi) {
      gridApi.refreshServerSide({ purge: true });
      updatePerformanceMetrics();
      toast.success('Grid data refreshed');
    }
  }, [gridApi, updatePerformanceMetrics]);

  // Format memory usage
  const formatMemoryUsage = (bytes: number): string => {
    if (bytes === 0) return '0 MB';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  // Performance monitoring effect
  useEffect(() => {
    if (!gridApi) return;
    
    const interval = setInterval(updatePerformanceMetrics, 5000);
    return () => clearInterval(interval);
  }, [gridApi, updatePerformanceMetrics]);

  return (
    <div className="space-y-4">
      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Grid Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {performance.totalRows.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Total Rows</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {performance.loadedRows.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Loaded</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {performance.renderTime.toFixed(0)}ms
              </div>
              <div className="text-xs text-gray-500">Render Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatMemoryUsage(performance.memoryUsage)}
              </div>
              <div className="text-xs text-gray-500">Memory</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-teal-600">
                {(performance.cacheHitRate * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Cache Hit</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Column Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span>Column Management</span>
            </div>
            <Button onClick={refreshData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {columns.map(column => (
              <Badge
                key={column.field}
                variant={visibleColumns.has(column.field) ? "default" : "outline"}
                className="cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => toggleColumnVisibility(column.field)}
              >
                {visibleColumns.has(column.field) ? (
                  <Eye className="h-3 w-3 mr-1" />
                ) : (
                  <EyeOff className="h-3 w-3 mr-1" />
                )}
                {column.headerName}
              </Badge>
            ))}
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Showing {visibleColumns.size} of {columns.length} columns
          </div>
        </CardContent>
      </Card>

      {/* High-Performance Data Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>High-Performance Data Grid</span>
            {loading && (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Loading data...</span>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="ag-theme-alpine w-full" style={{ height: '600px' }}>
            <AgGridReact
              ref={gridRef}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              onCellValueChanged={onCellValueChanged}
              
              // Server-side row model
              rowModelType="serverSide"
              serverSideStoreType={ServerSideStoreType.Partial}
              cacheBlockSize={100}
              maxBlocksInCache={10}
              maxConcurrentDatasourceRequests={2}
              blockLoadDebounceMillis={200}
              
              // Performance optimizations
              animateRows={false}
              suppressRowClickSelection={true}
              suppressCellFocus={false}
              enableRangeSelection={true}
              
              // Column virtualization
              suppressColumnVirtualisation={false}
              
              // Default column settings
              defaultColDef={{
                sortable: true,
                filter: true,
                resizable: true,
                suppressMenu: false,
                floatingFilter: true
              }}
              
              // Grid options for performance
              suppressContextMenu={false}
              enableCellTextSelection={true}
              ensureDomOrder={false}
              suppressRowTransform={true}
              
              // Callback for column events
              onColumnResized={(event) => {
                if (event.finished && event.column) {
                  onColumnResize?.(event.column.getColId(), event.column.getActualWidth());
                }
              }}
              
              onColumnMoved={(event) => {
                if (event.finished && gridApi) {
                  const columnOrder = gridApi.getColumns()?.map(col => col.getColId()) || [];
                  onColumnReorder?.(columnOrder);
                }
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}