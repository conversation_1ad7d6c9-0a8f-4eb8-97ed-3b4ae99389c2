"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertTriangle, ExternalLink, RefreshCw } from "lucide-react";
import { useGoogleSheets } from "@/hooks/use-google-sheets";

interface GoogleSheetsAuthDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAuthSuccess?: () => void;
}

export function GoogleSheetsAuthDialog({
  open,
  onOpenChange,
  onAuthSuccess
}: GoogleSheetsAuthDialogProps) {
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const { authenticate, checkAuthStatus } = useGoogleSheets();

  const handleAuthenticate = async () => {
    setIsAuthenticating(true);
    try {
      const success = await authenticate();
      if (success) {
        onAuthSuccess?.();
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Authentication failed:', error);
    } finally {
      setIsAuthenticating(false);
    }
  };

  const handleCheckStatus = async () => {
    setIsChecking(true);
    try {
      await checkAuthStatus();
      // Wait a moment for state to update
      setTimeout(() => {
        onAuthSuccess?.();
        onOpenChange(false);
      }, 500);
    } catch (error) {
      console.error('Status check failed:', error);
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Google Sheets Authorization Required
          </DialogTitle>
          <DialogDescription className="text-left">
            The application requires access to Google Sheets to load column configurations.
            Please authenticate to continue with the import process.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">What we need access to:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Read column mapping definitions</li>
              <li>• Access validation data worksheets</li>
              <li>• Load configuration settings</li>
            </ul>
          </div>
          
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <p className="text-xs text-gray-600">
              <strong>Note:</strong> We only request read-only access to your Google Sheets. 
              Your data remains secure and we cannot modify your spreadsheets.
            </p>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button 
            variant="outline" 
            onClick={handleCheckStatus}
            disabled={isChecking || isAuthenticating}
            className="w-full sm:w-auto"
          >
            {isChecking ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Again
              </>
            )}
          </Button>
          <Button 
            onClick={handleAuthenticate}
            disabled={isAuthenticating || isChecking}
            className="w-full sm:w-auto"
          >
            {isAuthenticating ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Authenticating...
              </>
            ) : (
              <>
                <ExternalLink className="h-4 w-4 mr-2" />
                Authenticate with Google
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 