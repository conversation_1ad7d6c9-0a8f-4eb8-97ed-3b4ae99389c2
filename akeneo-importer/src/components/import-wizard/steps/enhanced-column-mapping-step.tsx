"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Setting<PERSON>, 
  <PERSON>, 
  Zap,
  CheckCircle
} from "lucide-react";
import { useImportWizardStore } from "@/stores/import-wizard-store";
import { TargetColumnSelectionStep } from "./target-column-selection-step";
import { AiTransformMappingStep } from "./ai-transform-mapping-step";

const COLUMN_MAPPING_SUB_STEPS = [
  {
    id: 1,
    title: "Select Target Columns",
    description: "Choose which columns to create in your target system",
    icon: Settings,
  },
  {
    id: 2,
    title: "AI One-to-One Mapping",
    description: "Use AI to find exact matches between source and target columns",
    icon: Brain,
  },
  {
    id: 3,
    title: "AI Transform Mapping",
    description: "Apply AI transformation to remaining unmapped columns",
    icon: Zap,
  },
];

export function EnhancedColumnMappingStep() {
  const [currentSubStep, setCurrentSubStep] = useState(1);

  const getSubStepStatus = (subStepId: number) => {
    if (subStepId < currentSubStep) return "completed";
    if (subStepId === currentSubStep) return "current";
    return "upcoming";
  };

  const renderSubStepContent = () => {
    switch (currentSubStep) {
      case 1:
        return <TargetColumnSelectionStep />;
      case 2:
        return (
          <Card className="p-8 text-center">
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <Brain className="h-16 w-16 mx-auto text-gray-400" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    One-to-One Column Mapping
                  </h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    This feature will allow you to manually map source columns directly to target columns without AI transformation.
                  </p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg max-w-sm mx-auto">
                  <p className="text-sm text-blue-800 font-medium">
                    🚧 Coming Soon
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    This functionality is currently under development and will be available in a future update.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 3:
        return <AiTransformMappingStep />;
      default:
        return <TargetColumnSelectionStep />;
    }
  };

  const getSubStepIcon = (subStepId: number, status: string) => {
    if (status === "completed") {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    }
    const SubStepIcon = COLUMN_MAPPING_SUB_STEPS[subStepId - 1]?.icon;
    return SubStepIcon ? (
      <SubStepIcon className={`h-5 w-5 ${status === "current" ? "text-blue-600" : "text-gray-400"}`} />
    ) : null;
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Simplified Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {COLUMN_MAPPING_SUB_STEPS[currentSubStep - 1]?.title}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {COLUMN_MAPPING_SUB_STEPS[currentSubStep - 1]?.description}
          </p>
        </div>
        
        {/* Compact Sub-step Indicators */}
        <div className="flex items-center space-x-4">
          {COLUMN_MAPPING_SUB_STEPS.map((subStep) => {
            const status = getSubStepStatus(subStep.id);
            return (
              <div
                key={subStep.id}
                className="flex items-center space-x-2"
              >
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    status === "completed"
                      ? "bg-green-100 border-green-600"
                      : status === "current"
                      ? "bg-blue-100 border-blue-600"
                      : "bg-gray-100 border-gray-300"
                  }`}
                >
                  {getSubStepIcon(subStep.id, status)}
                </div>
                <div className="text-sm">
                  <Badge
                    variant={
                      status === "completed"
                        ? "default"
                        : status === "current"
                        ? "secondary"
                        : "outline"
                    }
                    className="text-xs"
                  >
                    {subStep.id}
                  </Badge>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Sub-step Content */}
      {renderSubStepContent()}
    </div>
  );
} 