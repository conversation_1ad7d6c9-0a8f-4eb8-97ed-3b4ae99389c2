"use client";

import { use<PERSON><PERSON>back, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { 
  CloudUpload, 
  FileSpreadsheet, 
  X, 
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Loader2
} from "lucide-react";
import { useImportWizardStore } from "@/stores/import-wizard-store";
import { useMutation } from "@tanstack/react-query";
import { SheetSelectionDialog } from "./sheet-selection-dialog";
import { DataPreviewCard } from "./data-preview-card";

interface FileUploadResponse {
  success: boolean;
  job_id: string;
  row_count: number;
  column_count: number;
  source_columns: string[];
  preview_data?: any[];
  error?: string;
}

interface SheetDetectionResponse {
  success: boolean;
  filename: string;
  sheet_names: string[];
  sheets_preview: Record<string, any>;
  total_sheets: number;
}

export function FileUploadStep() {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [googleSheetUrl, setGoogleSheetUrl] = useState("");
  const [googleSheetName, setGoogleSheetName] = useState("");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [sheetSelectionOpen, setSheetSelectionOpen] = useState(false);
  const [detectedSheets, setDetectedSheets] = useState<SheetDetectionResponse | null>(null);

  const { 
    jobId, 
    setJobId, 
    setSourceColumns, 
    setCurrentStep,
    setError,
    availableSheets,
    selectedSheet,
    previewData,
    setAvailableSheets,
    setSelectedSheet,
    setPreviewData,
    sourceColumns,
    markStepCompleted
  } = useImportWizardStore();

  // Create job mutation
  const createJobMutation = useMutation({
    mutationFn: async (jobName: string) => {
      console.log('File upload step: Creating job with name:', jobName);
      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          name: jobName,
          source_type: 'file_upload'
        }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create job: ${errorText}`);
      }
      const result = await response.json();
      console.log('File upload step: Job creation response:', result);
      return result;
    },
    onSuccess: (data) => {
      console.log('File upload step: Job created successfully:', data);
      const jobId = data.success ? data.job_id : data.id;
      const job = data.success ? data.job : data;
      
      setJobId(jobId);
      
      if (job.name) {
        useImportWizardStore.getState().setJobName(job.name);
        console.log('File upload step: Set job name to:', job.name);
      }
      
      toast.success('Job created successfully');
    },
    onError: (error) => {
      console.error('Job creation failed:', error);
      toast.error('Failed to create import job');
      setError('Failed to create import job');
    },
  });

  // Sheet detection mutation
  const detectSheetsMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/api/import/detect-sheets', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) throw new Error('Sheet detection failed');
      return response.json() as Promise<SheetDetectionResponse>;
    },
    onSuccess: (data) => {
      console.log('Sheets detected:', data);
      setDetectedSheets(data);
      setAvailableSheets(data.sheet_names);
      
      // Auto-select first sheet if only one sheet
      if (data.sheet_names.length === 1) {
        setSelectedSheet(data.sheet_names[0]);
      } else {
        setSheetSelectionOpen(true);
      }
    },
    onError: (error) => {
      console.error('Sheet detection failed:', error);
      toast.error('Failed to detect Excel sheets');
      setError('Failed to detect Excel sheets');
    },
  });

  // File upload mutation
  const uploadMutation = useMutation({
    mutationFn: async ({ file, worksheet }: { file?: File; worksheet?: string }) => {
      const formData = new FormData();
      
      if (file) {
        formData.append('file', file);
      }
      
      if (worksheet) {
        formData.append('worksheet_name', worksheet);
      }
      
      formData.append('job_id', jobId!);
      
      const response = await fetch('/api/import/upload', {
        method: 'POST',
        body: formData,
      });
      if (!response.ok) throw new Error('Upload failed');
      return response.json() as Promise<FileUploadResponse>;
    },
    onSuccess: (data) => {
      console.log('Upload successful:', data);
      setSourceColumns(data.source_columns);
      setPreviewData(data.preview_data || []);
      toast.success(`File uploaded successfully! ${data.row_count} rows, ${data.column_count} columns`);
      // Mark step as completed
      markStepCompleted(1);
      // Don't automatically move to next step - let user decide when to continue
    },
    onError: (error) => {
      console.error('Upload failed:', error);
      toast.error('Failed to upload file');
      setError('Failed to upload file');
    },
  });

  // Check if we already have uploaded data when component mounts
  useEffect(() => {
    if (jobId && sourceColumns.length > 0 && previewData.length > 0) {
      // Data already exists - show preview
      console.log('File upload step: Existing data found');
    }
  }, [jobId, sourceColumns, previewData]);

  const isExcelFile = (file: File) => {
    return file.type.includes('sheet') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    setUploadedFile(file);

    // Create job first if we don't have one
    let currentJobId = jobId;
    if (!currentJobId) {
      try {
        const jobName = `Import_${file.name}_${new Date().toISOString().slice(0, 19)}`;
        const jobResult = await createJobMutation.mutateAsync(jobName);
        console.log('File upload: Job created:', jobResult);
        
        currentJobId = jobResult.success ? jobResult.job_id : jobResult.id;
        
        const job = jobResult.success ? jobResult.job : jobResult;
        if (job.name && !useImportWizardStore.getState().jobName) {
          useImportWizardStore.getState().setJobName(job.name);
        }
      } catch (error) {
        console.error('Failed to create job:', error);
        toast.error('Failed to create job');
        return;
      }
    }

    if (!currentJobId) {
      toast.error('Failed to create job');
      return;
    }

    // Check if it's an Excel file
    if (isExcelFile(file)) {
      // Detect sheets first
      detectSheetsMutation.mutate(file);
    } else {
      // For CSV files, upload directly
      uploadMutation.mutate({ file });
    }
  }, [jobId, createJobMutation, detectSheetsMutation, uploadMutation]);

  const handleSheetSelectionConfirm = async () => {
    if (!uploadedFile || !selectedSheet) return;
    
    setSheetSelectionOpen(false);
    
    // Upload with selected sheet
    uploadMutation.mutate({ 
      file: uploadedFile, 
      worksheet: selectedSheet 
    });
  };

  const handleGoogleSheetsUpload = async () => {
    if (!googleSheetUrl.trim()) {
      toast.error('Please enter a Google Sheets URL');
      return;
    }

    // Create job first if we don't have one
    let currentJobId = jobId;
    if (!currentJobId) {
      try {
        const jobName = `GoogleSheet_${googleSheetName || 'Import'}_${new Date().toISOString().slice(0, 19)}`;
        const jobResult = await createJobMutation.mutateAsync(jobName);
        console.log('Google Sheets: Job created:', jobResult);
        
        currentJobId = jobResult.success ? jobResult.job_id : jobResult.id;
        
        const job = jobResult.success ? jobResult.job : jobResult;
        if (job.name && !useImportWizardStore.getState().jobName) {
          useImportWizardStore.getState().setJobName(job.name);
        }
      } catch (error) {
        console.error('Failed to create job:', error);
        toast.error('Failed to create job');
        return;
      }
    }

    if (!currentJobId) {
      toast.error('Failed to create job');
      return;
    }

    const formData = new FormData();
    formData.append('job_id', currentJobId);
    formData.append('google_sheet_url', googleSheetUrl);
    if (googleSheetName) {
      formData.append('google_sheet_name', googleSheetName);
    }

    uploadMutation.mutate({ file: undefined });
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxFiles: 1,
    disabled: uploadMutation.isPending || detectSheetsMutation.isPending
  });

  const removeFile = () => {
    setUploadedFile(null);
    setDetectedSheets(null);
    setAvailableSheets([]);
    setSelectedSheet(null);
    setPreviewData([]);
  };

  const isUploading = uploadMutation.isPending || createJobMutation.isPending || detectSheetsMutation.isPending;
  const hasUploadedData = sourceColumns.length > 0 && previewData.length > 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CloudUpload className="h-5 w-5" />
            <span>Upload Your Data</span>
          </CardTitle>
          <CardDescription>
            Choose your data source to begin the import process. Supported formats: CSV, Excel (.xlsx, .xls), and Google Sheets.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="file" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="file">File Upload</TabsTrigger>
              <TabsTrigger value="sheets">Google Sheets</TabsTrigger>
            </TabsList>
            
            <TabsContent value="file" className="space-y-4">
              {/* File Upload Area */}
              <div 
                {...getRootProps()} 
                className={`
                  border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                  ${isDragActive 
                    ? 'border-blue-400 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                  }
                  ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <input {...getInputProps()} />
                
                {isUploading ? (
                  <div className="space-y-4">
                    <Loader2 className="h-12 w-12 animate-spin text-blue-500 mx-auto" />
                    <div>
                      <p className="text-lg font-medium">Processing...</p>
                      <p className="text-sm text-gray-500">
                        {createJobMutation.isPending ? 'Creating job...' : 
                         detectSheetsMutation.isPending ? 'Detecting sheets...' :
                         'Uploading file...'}
                      </p>
                    </div>
                  </div>
                ) : hasUploadedData ? (
                  <div className="space-y-4">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
                    <div>
                      <p className="text-lg font-medium text-green-600">Data Uploaded Successfully</p>
                      <p className="text-sm text-gray-500">
                        {sourceColumns.length} columns, {previewData.length} preview rows
                      </p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={removeFile}
                        className="mt-2"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Upload Different File
                      </Button>
                    </div>
                  </div>
                ) : uploadedFile ? (
                  <div className="space-y-4">
                    <FileSpreadsheet className="h-12 w-12 text-blue-500 mx-auto" />
                    <div>
                      <p className="text-lg font-medium text-blue-600">File Ready</p>
                      <p className="text-sm text-gray-500">{uploadedFile.name}</p>
                      {isExcelFile(uploadedFile) && (
                        <p className="text-xs text-amber-600">
                          {availableSheets.length > 1 ? 'Waiting for sheet selection...' : 'Processing Excel file...'}
                        </p>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={removeFile}
                        className="mt-2"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Remove
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <CloudUpload className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-lg font-medium">
                        {isDragActive ? 'Drop your file here' : 'Drag & drop your file here'}
                      </p>
                      <p className="text-sm text-gray-500">
                        or click to browse your computer
                      </p>
                      <p className="text-xs text-gray-400 mt-2">
                        Supports CSV, Excel (.xlsx, .xls) files
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="sheets" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="sheet-url">Google Sheets URL</Label>
                  <Input
                    id="sheet-url"
                    type="url"
                    placeholder="https://docs.google.com/spreadsheets/d/..."
                    value={googleSheetUrl}
                    onChange={(e) => setGoogleSheetUrl(e.target.value)}
                    disabled={isUploading}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Make sure the sheet is publicly accessible or you have the necessary permissions
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="sheet-name">Sheet Name (Optional)</Label>
                  <Input
                    id="sheet-name"
                    placeholder="Sheet1"
                    value={googleSheetName}
                    onChange={(e) => setGoogleSheetName(e.target.value)}
                    disabled={isUploading}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Leave empty to use the first sheet
                  </p>
                </div>

                <Button 
                  onClick={handleGoogleSheetsUpload}
                  disabled={!googleSheetUrl.trim() || isUploading}
                  className="w-full"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Connect to Google Sheets
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Data Preview */}
      {hasUploadedData && (
        <DataPreviewCard
          data={previewData}
          sourceColumns={sourceColumns}
          filename={uploadedFile?.name}
          selectedSheet={selectedSheet || undefined}
          totalRows={previewData.length}
        />
      )}

      {/* Continue Button */}
      {hasUploadedData && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium text-green-600">Data Upload Complete!</h3>
                <p className="text-sm text-gray-600">
                  Review your data above and click continue when ready to proceed with column mapping.
                </p>
              </div>
              <Button 
                onClick={() => setCurrentStep(2)}
                size="lg"
                className="min-w-48"
              >
                Continue to Column Mapping
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status Card */}
      {jobId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>Import Job Created</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm">
                <strong>Job ID:</strong> <code className="bg-gray-100 px-2 py-1 rounded">{jobId}</code>
              </p>
              <p className="text-sm text-gray-600">
                Your import job has been created and is ready for data upload.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Tips</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-xs text-gray-600">
            • Ensure your data has column headers in the first row
          </p>
          <p className="text-xs text-gray-600">
            • For Excel files with multiple sheets, you'll be asked to select which sheet to import
          </p>
          <p className="text-xs text-gray-600">
            • For Google Sheets, make sure the sheet is shared with view permissions
          </p>
          <p className="text-xs text-gray-600">
            • Large files may take a few moments to process
          </p>
        </CardContent>
      </Card>

      {/* Sheet Selection Dialog */}
      {detectedSheets && (
        <SheetSelectionDialog
          open={sheetSelectionOpen}
          onOpenChange={setSheetSelectionOpen}
          filename={detectedSheets.filename}
          sheetNames={detectedSheets.sheet_names}
          sheetsPreview={detectedSheets.sheets_preview}
          selectedSheet={selectedSheet}
          onSheetSelect={setSelectedSheet}
          onConfirm={handleSheetSelectionConfirm}
          isLoading={uploadMutation.isPending}
        />
      )}
    </div>
  );
} 