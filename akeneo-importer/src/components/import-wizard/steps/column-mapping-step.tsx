"use client";

import { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { 
  Brain, 
  Zap,
  Loader2, 
  CheckCircle,
  Trash2,
  Settings,
  MessageSquare,
  Shield,
  AlertCircle,
  FileSpreadsheet,
  Cloud,
  CloudOff,
  Database
} from "lucide-react";
import { useImportWizardStore } from "@/stores/import-wizard-store";
import { useUnifiedConfig } from "@/hooks/use-unified-config";
import { GoogleSheetsAuthDialog } from "../google-sheets-auth-dialog";

interface TargetColumn {
  column_name: string;
  display_name: string;
  type: "Navisionvorlage" | "Akeneo";
  required: boolean;
  default_mapping: string | null;
  default_mapping_content: string | null;
  prompt: string | null;
  output_validation_column: string | null;
  validation_options?: string[];
}

interface ColumnMapping {
  targetColumn: string;
  sourceColumn: string | null;
  mappingType: "one-to-one" | "ai-transform" | "static" | "deactivated";
  staticValue?: string;
  prompt?: string;
  validationOptions?: string[];
}

type ColumnSystemType = "erp" | "product-information-system";

export function ColumnMappingStep() {
  const [allTargetColumns, setAllTargetColumns] = useState<TargetColumn[]>([]);
  const [columnMappings, setColumnMappings] = useState<ColumnMapping[]>([]);
  const [loadingMappings, setLoadingMappings] = useState(false);
  const [loadingAiTransform, setLoadingAiTransform] = useState(false);
  const [configurationSource, setConfigurationSource] = useState<"google-sheets" | "excel" | "unified" | null>(null);
  const [sourceConnectionStatus, setSourceConnectionStatus] = useState<"checking" | "connected" | "failed">("checking");
  const [columnSystemType, setColumnSystemType] = useState<ColumnSystemType>("erp");
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  
  const { sourceColumns } = useImportWizardStore();
  const unifiedConfig = useUnifiedConfig();

  // Filter columns based on selected system type - memoized to prevent recreation
  const getFilteredColumns = useCallback((columns: TargetColumn[]): TargetColumn[] => {
    if (columnSystemType === "erp") {
      // For ERP, show only Navisionvorlage columns (ERP system columns)
      return columns.filter(col => col.type === "Navisionvorlage");
    } else {
      // For product information system, show only Akeneo columns
      return columns.filter(col => col.type === "Akeneo");
    }
  }, [columnSystemType]);

  const targetColumns = useMemo(() => getFilteredColumns(allTargetColumns), [allTargetColumns, getFilteredColumns]);

  const loadTargetColumns = useCallback(async () => {
    setLoadingMappings(true);
    setSourceConnectionStatus("checking");
    
    try {
      console.log("🔍 Loading from unified configuration...");
      
      // Check if unified config is already loaded
      if (!unifiedConfig.isLoading && unifiedConfig.columns.length > 0) {
        console.log("✅ Using cached unified configuration");
        setConfigurationSource("unified");
        setSourceConnectionStatus("connected");
        
        // Convert unified config columns to target columns
        const unifiedTargetColumns: TargetColumn[] = unifiedConfig.columns.map(col => ({
          column_name: col.column_name,
          display_name: col.column_name,
          type: col.type,
          required: col.required,
          default_mapping: col.default_mapping || null,
          default_mapping_content: col.default_mapping_content || null,
          prompt: col.prompt || null,
          output_validation_column: col.output_validation_column || null
        }));
        
        setAllTargetColumns(unifiedTargetColumns);
        toast.success("Configuration loaded from unified config");
        return;
      }
      
      // If not loaded, trigger a refresh
      console.log("🔄 Refreshing unified configuration...");
      await unifiedConfig.refreshConfig();
      
      if (unifiedConfig.columns.length > 0) {
        console.log("✅ Unified configuration refreshed successfully");
        setConfigurationSource("unified");
        setSourceConnectionStatus("connected");
        
        // Convert unified config columns to target columns
        const unifiedTargetColumns: TargetColumn[] = unifiedConfig.columns.map(col => ({
          column_name: col.column_name,
          display_name: col.column_name,
          type: col.type,
          required: col.required,
          default_mapping: col.default_mapping || null,
          default_mapping_content: col.default_mapping_content || null,
          prompt: col.prompt || null,
          output_validation_column: col.output_validation_column || null
        }));
        
        setAllTargetColumns(unifiedTargetColumns);
        toast.success("Configuration loaded from unified config");
        return;
      }
      
      // Configuration failed to load
      setSourceConnectionStatus("failed");
      toast.error("Failed to load unified configuration. Please check settings.");
      
    } catch (error) {
      console.error("❌ Unified configuration loading failed:", error);
      setSourceConnectionStatus("failed");
      toast.error("Failed to load configuration");
    } finally {
      setLoadingMappings(false);
    }
  }, []);

  // Handle auth success
  const handleAuthSuccess = useCallback(async () => {
    console.log('🔑 Authentication successful - reloading configuration...');
    setShowAuthDialog(false);
    setSourceConnectionStatus("checking");
    await unifiedConfig.refreshConfig();
  }, [unifiedConfig]);

  // Load target columns with fallback mechanism
  useEffect(() => {
    let mounted = true;
    
    const loadConfig = async () => {
      // Check if we've hit max retries and have auth error
      if (unifiedConfig.authError && unifiedConfig.retryCount >= unifiedConfig.maxRetries) {
        if (!mounted) return;
        console.warn('Max retries reached with auth error - showing auth dialog');
        setSourceConnectionStatus("failed");
        setShowAuthDialog(true);
        setLoadingMappings(false);
        return;
      }
      
      // If unified config already has data, use it immediately
      if (!unifiedConfig.isLoading && unifiedConfig.columns.length > 0) {
        if (!mounted) return;
        
        console.log("✅ Using cached unified configuration immediately");
        setConfigurationSource("unified");
        setSourceConnectionStatus("connected");
        
        const unifiedTargetColumns: TargetColumn[] = unifiedConfig.columns.map(col => ({
          column_name: col.column_name,
          display_name: col.column_name,
          type: col.type,
          required: col.required,
          default_mapping: col.default_mapping || null,
          default_mapping_content: col.default_mapping_content || null,
          prompt: col.prompt || null,
          output_validation_column: col.output_validation_column || null
        }));
        
        setAllTargetColumns(unifiedTargetColumns);
        setLoadingMappings(false);
      } else if (!unifiedConfig.isLoading && unifiedConfig.columns.length === 0) {
        // Only load if we don't have data and we're not already loading
        if (!mounted) return;
        console.log("🔄 Loading configuration from API...");
        await loadTargetColumns();
      }
    };
    
    loadConfig();
    
    return () => {
      mounted = false;
    };
  }, [unifiedConfig.isLoading, unifiedConfig.columns.length, unifiedConfig.authError, unifiedConfig.retryCount, unifiedConfig.maxRetries, loadTargetColumns]);

  // Initialize column mappings function - memoized to prevent recreation
  const initializeColumnMappings = useCallback((columns: TargetColumn[]) => {
    const initialMappings: ColumnMapping[] = columns.map(col => ({
      targetColumn: col.column_name,
      sourceColumn: null,
      mappingType: "deactivated",
      prompt: col.prompt || undefined,
      validationOptions: col.validation_options
    }));
    
    setColumnMappings(initialMappings);
  }, []);

  // Initialize column mappings when target columns or system type changes
  useEffect(() => {
    if (targetColumns.length > 0) {
      // Only initialize if we don't have mappings or if the count changed
      if (columnMappings.length === 0 || columnMappings.length !== targetColumns.length) {
        initializeColumnMappings(targetColumns);
      }
    }
  }, [targetColumns.length, columnSystemType, initializeColumnMappings, columnMappings.length]);

  const handleColumnSystemChange = useCallback((newSystemType: ColumnSystemType) => {
    setColumnSystemType(newSystemType);
    toast.success(`Switched to ${newSystemType === "erp" ? "ERP System (Navisionvorlage)" : "Product Information System (Akeneo)"} columns`);
  }, []);

  const handleSourceColumnChange = useCallback((targetColumn: string, sourceColumn: string | null) => {
    setColumnMappings(prev => 
      prev.map(mapping => 
        mapping.targetColumn === targetColumn 
          ? { 
              ...mapping, 
              sourceColumn,
              mappingType: sourceColumn ? "one-to-one" : "deactivated"
            }
          : mapping
      )
    );
  }, []);

  const handleMappingTypeChange = useCallback((targetColumn: string, mappingType: string) => {
    setColumnMappings(prev => 
      prev.map(mapping => 
        mapping.targetColumn === targetColumn 
          ? { 
              ...mapping, 
              mappingType: mappingType as ColumnMapping["mappingType"],
              sourceColumn: mappingType === "deactivated" ? null : mapping.sourceColumn
            }
          : mapping
      )
    );
  }, []);

  const handleStaticValueChange = useCallback((targetColumn: string, staticValue: string) => {
    setColumnMappings(prev => 
      prev.map(mapping => 
        mapping.targetColumn === targetColumn 
          ? { ...mapping, staticValue }
          : mapping
      )
    );
  }, []);

  const suggestOneToOneMappings = async () => {
    setLoadingMappings(true);
    
    try {
      // Simple intelligent matching based on column names
      const updatedMappings = columnMappings.map(mapping => {
        const targetCol = mapping.targetColumn.toLowerCase();
        const bestMatch = sourceColumns.find(sourceCol => {
          const source = sourceCol.toLowerCase();
          return source === targetCol || 
                 source.includes(targetCol) || 
                 targetCol.includes(source);
        });
        
        return {
          ...mapping,
          sourceColumn: bestMatch || null,
          mappingType: bestMatch ? "one-to-one" as const : mapping.mappingType
        };
      });
      
      setColumnMappings(updatedMappings);
      toast.success("AI suggestions applied to column mappings");
      
    } catch (error) {
      console.error("Error suggesting mappings:", error);
      toast.error("Failed to suggest column mappings");
    } finally {
      setLoadingMappings(false);
    }
  };

  const applyAiTransformationMapping = async () => {
    setLoadingAiTransform(true);
    
    try {
      // Apply AI transformation mapping to all columns with prompts
      const updatedMappings = columnMappings.map((mapping: ColumnMapping) => {
        const targetCol = targetColumns.find(col => col.column_name === mapping.targetColumn);
        
        if (targetCol && targetCol.prompt && !mapping.sourceColumn) {
          return {
            ...mapping,
            mappingType: "ai-transform" as const,
            prompt: targetCol.prompt
          };
        }
        
        return mapping;
      });
      
      setColumnMappings(updatedMappings);
      toast.success("AI transformation mappings applied");
      
    } catch (error) {
      console.error("Error applying AI transformation:", error);
      toast.error("Failed to apply AI transformation mappings");
    } finally {
      setLoadingAiTransform(false);
    }
  };

  const clearAllMappings = () => {
    setColumnMappings(prev => 
      prev.map(mapping => ({
        ...mapping,
        sourceColumn: null,
        mappingType: "deactivated",
        staticValue: undefined
      }))
    );
    toast.success("All mappings cleared");
  };

  const groupedColumns = useMemo(() => {
    return targetColumns.reduce((acc, col) => {
      if (!acc[col.type]) acc[col.type] = [];
      acc[col.type].push(col);
      return acc;
    }, {} as Record<string, TargetColumn[]>);
  }, [targetColumns]);

  const getConnectionStatusIcon = () => {
    switch (sourceConnectionStatus) {
      case "checking":
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case "connected":
        return configurationSource === "google-sheets" 
          ? <Cloud className="h-4 w-4 text-green-500" />
          : <FileSpreadsheet className="h-4 w-4 text-green-500" />;
      case "failed":
        return <CloudOff className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getConnectionStatusText = () => {
    switch (sourceConnectionStatus) {
      case "checking":
        return "Checking configuration sources...";
      case "connected":
        if (configurationSource === "google-sheets") {
          return "Connected to Google Sheets";
        } else if (configurationSource === "excel") {
          return "Using Excel configuration";
        } else if (configurationSource === "unified") {
          return "Using unified configuration";
        } else {
          return "Configuration loaded";
        }
      case "failed":
        return "Configuration sources unavailable";
      default:
        return "Unknown status";
    }
  };

  if ((loadingMappings && sourceConnectionStatus === "checking") || unifiedConfig.isLoading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Loading Configuration
            </CardTitle>
            <CardDescription>
              Loading unified configuration...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (sourceConnectionStatus === "failed") {
    return (
      <>
        <GoogleSheetsAuthDialog
          open={showAuthDialog}
          onOpenChange={setShowAuthDialog}
          onAuthSuccess={handleAuthSuccess}
        />
        
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-5 w-5" />
                Configuration Error
              </CardTitle>
              <CardDescription>
                Failed to load unified configuration after {unifiedConfig.retryCount} attempts.
                {unifiedConfig.authError ? ' Google Sheets authentication is required.' : ' Please check the settings page to configure your data source.'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {unifiedConfig.authError ? (
                <Button onClick={() => setShowAuthDialog(true)} className="w-full">
                  <Settings className="h-4 w-4 mr-2" />
                  Authenticate with Google Sheets
                </Button>
              ) : (
                <Button onClick={loadTargetColumns} className="w-full">
                  <Loader2 className="h-4 w-4 mr-2" />
                  Retry Loading Configuration
                </Button>
              )}
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => window.open('/settings', '_blank')}
              >
                <Settings className="h-4 w-4 mr-2" />
                Open Settings to Configure Data Source
              </Button>
            </CardContent>
          </Card>
        </div>
      </>
    );
  }

  return (
    <>
      <GoogleSheetsAuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        onAuthSuccess={handleAuthSuccess}
      />
      
      <div className="space-y-6">
      {/* Column System Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Column System Selection
          </CardTitle>
          <CardDescription>
            Choose between ERP system columns (Navisionvorlage) or Product Information System columns (Akeneo)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Select 
              value={columnSystemType} 
              onValueChange={(value: ColumnSystemType) => handleColumnSystemChange(value)}
            >
              <SelectTrigger className="w-64">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="erp">
                  <div className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    ERP System Columns (Navisionvorlage)
                  </div>
                </SelectItem>
                <SelectItem value="product-information-system">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Product Information System Columns (Akeneo)
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <Badge variant="outline" className="flex items-center gap-1">
              {targetColumns.length} columns available
            </Badge>
            <Badge variant="secondary" className="text-xs">
              Showing {columnSystemType === "erp" ? "Navisionvorlage" : "Akeneo"} columns from {allTargetColumns.length} total
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        <Button 
          onClick={suggestOneToOneMappings}
          disabled={loadingMappings}
          className="flex items-center gap-2"
        >
          <Brain className="h-4 w-4" />
          {loadingMappings ? "Processing..." : "Suggest One-to-One Mappings with AI"}
        </Button>
        
        <Button 
          onClick={applyAiTransformationMapping}
          disabled={loadingAiTransform}
          variant="secondary"
          className="flex items-center gap-2 bg-purple-100 hover:bg-purple-200 text-purple-700"
        >
          <Zap className="h-4 w-4" />
          {loadingAiTransform ? "Processing..." : "Apply AI Transformation Mapping"}
        </Button>
        
        <Button 
          onClick={clearAllMappings}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Trash2 className="h-4 w-4" />
          Clear All
        </Button>
      </div>

      {/* Column Mappings by Type */}
      {Object.entries(groupedColumns).map(([type, columns]) => (
        <Card key={type}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              {type} Columns
              <Badge variant="outline">{columns.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {columns.map(column => {
                const mapping = columnMappings.find(m => m.targetColumn === column.column_name);
                return (
                  <ColumnMappingRow
                    key={column.column_name}
                    targetColumn={column}
                    mapping={mapping}
                    sourceColumns={sourceColumns}
                    onSourceColumnChange={handleSourceColumnChange}
                    onMappingTypeChange={handleMappingTypeChange}
                    onStaticValueChange={handleStaticValueChange}
                  />
                );
              })}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
    </>
  );
}

interface ColumnMappingRowProps {
  targetColumn: TargetColumn;
  mapping: ColumnMapping | undefined;
  sourceColumns: string[];
  onSourceColumnChange: (targetColumn: string, sourceColumn: string | null) => void;
  onMappingTypeChange: (targetColumn: string, mappingType: string) => void;
  onStaticValueChange: (targetColumn: string, staticValue: string) => void;
}

function ColumnMappingRow({ 
  targetColumn, 
  mapping, 
  sourceColumns, 
  onSourceColumnChange, 
  onMappingTypeChange,
  onStaticValueChange 
}: ColumnMappingRowProps) {
  const getMappingIcon = (type: string) => {
    switch (type) {
      case "one-to-one": return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "ai-transform": return <Brain className="h-4 w-4 text-purple-500" />;
      case "static": return <Settings className="h-4 w-4 text-blue-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="border rounded-lg">
      <div className="grid grid-cols-12 gap-4 items-center p-4">
        {/* Target Column Info */}
        <div className="col-span-3">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{targetColumn.display_name}</span>
            {targetColumn.required && <Badge variant="destructive" className="text-xs">Required</Badge>}
          </div>
          {targetColumn.prompt && (
            <div className="flex items-center gap-1 mt-1">
              <MessageSquare className="h-3 w-3 text-blue-500" />
              <span className="text-xs text-muted-foreground">Has AI Prompt</span>
            </div>
          )}
          {targetColumn.output_validation_column && (
            <div className="flex items-center gap-1 mt-1">
              <Shield className="h-3 w-3 text-green-500" />
              <span className="text-xs text-muted-foreground">Validation: {targetColumn.output_validation_column}</span>
            </div>
          )}
        </div>

      {/* Mapping Type */}
      <div className="col-span-2">
        <Select 
          value={mapping?.mappingType || "deactivated"}
          onValueChange={(value) => onMappingTypeChange(targetColumn.column_name, value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="deactivated">
              <div className="flex items-center gap-2">
                {getMappingIcon("deactivated")}
                Deactivated
              </div>
            </SelectItem>
            <SelectItem value="one-to-one">
              <div className="flex items-center gap-2">
                {getMappingIcon("one-to-one")}
                One-to-One
              </div>
            </SelectItem>
            <SelectItem value="ai-transform">
              <div className="flex items-center gap-2">
                {getMappingIcon("ai-transform")}
                AI Transform
              </div>
            </SelectItem>
            <SelectItem value="static">
              <div className="flex items-center gap-2">
                {getMappingIcon("static")}
                Static Value
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Source Column Selection */}
      <div className="col-span-3">
        {mapping?.mappingType === "one-to-one" && (
          <Select 
            value={mapping.sourceColumn || ""}
            onValueChange={(value) => onSourceColumnChange(targetColumn.column_name, value || null)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select source column" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">None</SelectItem>
              {sourceColumns.map(col => (
                <SelectItem key={col} value={col}>{col}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        
        {mapping?.mappingType === "static" && (
          <input
            type="text"
            placeholder="Enter static value"
            value={mapping.staticValue || ""}
            onChange={(e) => onStaticValueChange(targetColumn.column_name, e.target.value)}
            className="w-full px-3 py-2 border rounded-md text-sm"
          />
        )}
        
        {mapping?.mappingType === "ai-transform" && targetColumn.prompt && (
          <div className="flex items-center gap-2 p-2 bg-purple-50 rounded-md">
            <Brain className="h-4 w-4 text-purple-500" />
            <span className="text-sm text-purple-700">AI Transformation</span>
          </div>
        )}
      </div>

      {/* Status/Preview */}
      <div className="col-span-4">
        {mapping?.mappingType === "one-to-one" && mapping.sourceColumn && (
          <div className="text-sm text-muted-foreground">
            Maps from <span className="font-mono">{mapping.sourceColumn}</span>
          </div>
        )}
        
        {mapping?.mappingType === "ai-transform" && (
          <div className="text-sm text-purple-600">
            Uses AI with custom prompt
          </div>
        )}
        
        {mapping?.mappingType === "static" && mapping.staticValue && (
          <div className="text-sm text-blue-600">
            Fixed value: <span className="font-mono">{mapping.staticValue}</span>
          </div>
        )}
        
        {mapping?.mappingType === "deactivated" && (
          <div className="text-sm text-muted-foreground">
            Column not mapped
          </div>
        )}
      </div>
      </div>
      
      {/* Default Configuration Details */}
      {(targetColumn.default_mapping || targetColumn.default_mapping_content || targetColumn.prompt) && (
        <div className="px-4 pb-4 border-t bg-gray-50">
          <div className="text-xs font-medium text-gray-700 mb-2">Default Configuration</div>
          <div className="space-y-1">
            {targetColumn.default_mapping && (
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-600">Default Mapping:</span>
                <Badge variant="outline" className="text-xs">{targetColumn.default_mapping}</Badge>
              </div>
            )}
            {targetColumn.default_mapping_content && (
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-600">Custom Mapping Prompt Template:</span>
                <code className="text-xs bg-gray-100 px-2 py-1 rounded max-w-md truncate" title={targetColumn.default_mapping_content}>
                  {targetColumn.default_mapping_content.length > 50 ? `${targetColumn.default_mapping_content.substring(0, 50)}...` : targetColumn.default_mapping_content}
                </code>
              </div>
            )}
            {targetColumn.prompt && (
              <div className="mt-2">
                <span className="text-xs text-gray-600">AI Prompt:</span>
                <div className="text-xs text-gray-700 mt-1 p-2 bg-white border rounded max-w-2xl">
                  {targetColumn.prompt.length > 200 ? `${targetColumn.prompt.substring(0, 200)}...` : targetColumn.prompt}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 