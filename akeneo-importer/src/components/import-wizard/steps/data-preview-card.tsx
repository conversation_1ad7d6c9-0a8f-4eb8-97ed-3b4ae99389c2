"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, BarChart3, FileText, AlertCircle } from "lucide-react";
import { useState } from "react";

interface DataPreviewCardProps {
  data: any[];
  sourceColumns: string[];
  filename?: string;
  selectedSheet?: string;
  totalRows?: number;
  className?: string;
}

export function DataPreviewCard({
  data,
  sourceColumns,
  filename,
  selectedSheet,
  totalRows,
  className = ""
}: DataPreviewCardProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  
  if (!data || data.length === 0) {
    return null;
  }

  const previewRows = data.slice(0, 5); // Show first 5 rows
  const actualTotalRows = totalRows || data.length;
  const isWideTable = sourceColumns.length > 10;
  const isMassiveTable = sourceColumns.length > 25;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Data Preview</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Hide
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Show
              </>
            )}
          </Button>
        </CardTitle>
        <CardDescription>
          <div className="flex flex-wrap items-center gap-2 text-sm">
            {filename && (
              <span>
                <strong>File:</strong> {filename}
              </span>
            )}
            {selectedSheet && (
              <Badge variant="outline" className="text-xs">
                Sheet: {selectedSheet}
              </Badge>
            )}
            <span className="flex items-center">
              <BarChart3 className="h-3 w-3 mr-1" />
              {actualTotalRows.toLocaleString()} rows
            </span>
            <span className="flex items-center">
              {sourceColumns.length} columns
              {isMassiveTable && (
                <AlertCircle className="h-3 w-3 ml-1 text-orange-500" />
              )}
            </span>
            {isWideTable && (
              <Badge variant={isMassiveTable ? "destructive" : "secondary"} className="text-xs">
                {isMassiveTable ? "Very Wide Table" : "Wide Table"}
              </Badge>
            )}
          </div>
        </CardDescription>
      </CardHeader>
      
      {isExpanded && (
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>
                Showing first {Math.min(5, previewRows.length)} rows of {actualTotalRows.toLocaleString()} total rows
              </span>
              {isWideTable && (
                <span className="text-orange-600 text-xs">
                  Scroll horizontally to see all columns →
                </span>
              )}
            </div>
            
            <div className="border rounded-lg overflow-hidden">
              <div 
                className="overflow-auto" 
                style={{ 
                  maxHeight: isMassiveTable ? '300px' : '400px',
                  maxWidth: '100%'
                }}
              >
                <Table>
                  <TableHeader className="sticky top-0 z-10">
                    <TableRow>
                      <TableHead className="w-16 text-xs font-semibold bg-gray-50 border-r border-gray-200 sticky left-0 z-20">
                        #
                      </TableHead>
                      {sourceColumns.map((column, index) => (
                        <TableHead 
                          key={index} 
                          className="text-xs font-semibold bg-gray-50 border-r border-gray-200 last:border-r-0"
                          style={{ 
                            minWidth: isWideTable ? '120px' : '100px',
                            maxWidth: isMassiveTable ? '150px' : '200px'
                          }}
                        >
                          <div className="truncate" title={column}>
                            {column}
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {previewRows.map((row, rowIndex) => (
                      <TableRow key={rowIndex} className="hover:bg-gray-50">
                        <TableCell className="text-xs text-gray-500 font-mono bg-gray-50 border-r border-gray-200 sticky left-0 z-10">
                          {rowIndex + 1}
                        </TableCell>
                        {sourceColumns.map((column, colIndex) => (
                          <TableCell 
                            key={colIndex} 
                            className="text-xs border-r border-gray-100 last:border-r-0"
                            style={{ 
                              minWidth: isWideTable ? '120px' : '100px',
                              maxWidth: isMassiveTable ? '150px' : '200px'
                            }}
                          >
                            <div 
                              className="truncate" 
                              title={String(row[column] || '')}
                            >
                              {String(row[column] || '')}
                            </div>
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            
            <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
              <span>
                {actualTotalRows > 5 && 
                  `... and ${(actualTotalRows - 5).toLocaleString()} more rows`
                }
              </span>
              {isWideTable && (
                <span className="text-orange-600">
                  💡 Tip: Use keyboard arrows to navigate
                </span>
              )}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
} 