"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { 
  Wand2, 
  Loader2, 
  CheckCircle, 
  AlertTriangle,
  Eye,
  EyeOff,
  RefreshCw
} from "lucide-react";
import { useImportWizardStore } from "@/stores/import-wizard-store";

interface SingleColumnTransformDialogProps {
  isOpen: boolean;
  onClose: () => void;
  jobId: string;
  columnName: string;
  rowIndex: number;
  sourceRow: Record<string, any>;
  currentTransformValue?: string;
  promptTemplate?: string;
  jobNotes?: string;
  worksheetData?: any[];
  columnConfiguration?: any;
  onTransformComplete: (result: {
    column_name: string;
    row_index: number;
    result: string;
    reasoning?: string;
  }) => void;
}

export function SingleColumnTransformDialog({
  isOpen,
  onClose,
  jobId,
  columnName,
  rowIndex,
  sourceRow,
  currentTransformValue,
  promptTemplate,
  jobNotes,
  worksheetData,
  columnConfiguration,
  onTransformComplete
}: SingleColumnTransformDialogProps) {
  const [isTransforming, setIsTransforming] = useState(false);
  const [transformResult, setTransformResult] = useState<{
    result: string;
    reasoning?: string;
    structured_output?: boolean;
    fromCache?: boolean;
    cache_bypassed?: boolean;
  } | null>(null);
  const [showReasoning, setShowReasoning] = useState(true);
  const [showSourceData, setShowSourceData] = useState(false);
  const [showPromptDetails, setShowPromptDetails] = useState(false);
  const [renderedPrompt, setRenderedPrompt] = useState<string>('');

  // Get selected model from store
  const { selectedLlmModel } = useImportWizardStore();

  const handleTransform = async () => {
    if (!promptTemplate) {
      toast.error('No prompt template configured for this column');
      return;
    }

    setIsTransforming(true);
    setTransformResult(null);

    try {
      const response = await fetch('/api/import/single-column-transform', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          job_id: jobId,
          column_name: columnName,
          row_index: rowIndex,
          prompt_template: promptTemplate,
          use_structured_output: true,
          show_reasoning: showReasoning,
          // Include job context
          job_notes: jobNotes || '',
          worksheet_data: worksheetData || [],
          column_configuration: columnConfiguration,
          model_id: selectedLlmModel // Pass the selected model
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setTransformResult({
          result: result.answer || result.result,
          reasoning: result.reasoning,
          structured_output: result.structured_output,
          fromCache: result.fromCache,
          cache_bypassed: result.cache_bypassed
        });
        
        // Capture the rendered prompt for display
        if (result.debug_info?.enhanced_user_prompt) {
          setRenderedPrompt(result.debug_info.enhanced_user_prompt);
        }
        
        // Notify parent component
        onTransformComplete({
          column_name: columnName,
          row_index: rowIndex,
          result: result.answer || result.result,
          reasoning: result.reasoning
        });
        
        toast.success('Column transformed successfully!');
      } else {
        throw new Error(result.error || 'Transformation failed');
      }
    } catch (error) {
      console.error('Single column transform error:', error);
      toast.error(`Failed to transform column: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTransforming(false);
    }
  };

  const handleClose = () => {
    setTransformResult(null);
    setRenderedPrompt('');
    setShowPromptDetails(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Wand2 className="h-5 w-5" />
            <span>Transform Single Column</span>
          </DialogTitle>
          <DialogDescription>
            Transform column <strong>{columnName}</strong> for row <strong>{rowIndex + 1}</strong> using AI
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Column and Row Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Transform Context</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Column</Label>
                  <p className="text-sm bg-gray-50 p-2 rounded">{columnName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Row</Label>
                  <p className="text-sm bg-gray-50 p-2 rounded">{rowIndex + 1}</p>
                </div>
              </div>
              
              {currentTransformValue && (
                <div>
                  <Label className="text-sm font-medium">Current Value</Label>
                  <p className="text-sm bg-yellow-50 p-2 rounded border border-yellow-200">
                    {currentTransformValue}
                  </p>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSourceData(!showSourceData)}
                >
                  {showSourceData ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                  {showSourceData ? 'Hide' : 'Show'} Source Data
                </Button>
              </div>

              {showSourceData && (
                <div>
                  <Label className="text-sm font-medium">Source Row Data</Label>
                  <div className="bg-gray-50 p-3 rounded text-sm max-h-32 overflow-y-auto">
                    <pre className="break-words whitespace-pre-wrap overflow-wrap-anywhere">
                      {JSON.stringify(sourceRow, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Transform Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Transform Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <Button
                  onClick={handleTransform}
                  disabled={isTransforming || !promptTemplate}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isTransforming ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Transforming...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Transform Column
                    </>
                  )}
                </Button>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="show-reasoning"
                      checked={showReasoning}
                      onChange={(e) => setShowReasoning(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="show-reasoning" className="text-sm">
                      Show reasoning
                    </Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="show-prompt-details"
                      checked={showPromptDetails}
                      onChange={(e) => setShowPromptDetails(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="show-prompt-details" className="text-sm">
                      Show prompt details
                    </Label>
                  </div>
                </div>
              </div>

              {!promptTemplate && (
                <div className="flex items-center space-x-2 text-amber-600 bg-amber-50 p-3 rounded">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">
                    No prompt template configured for this column. Please configure AI Transform mapping first.
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Prompt Details */}
          {promptTemplate && showPromptDetails && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Prompt Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Original Prompt Template</Label>
                  <div className="bg-gray-50 p-3 rounded border max-h-32 overflow-y-auto">
                    <pre className="text-sm break-words whitespace-pre-wrap overflow-wrap-anywhere">
                      {promptTemplate}
                    </pre>
                  </div>
                </div>

                {renderedPrompt && (
                  <div>
                    <Label className="text-sm font-medium">Rendered Prompt (with placeholders filled)</Label>
                    <div className="bg-blue-50 p-3 rounded border border-blue-200 max-h-32 overflow-y-auto">
                      <pre className="text-sm break-words whitespace-pre-wrap overflow-wrap-anywhere">
                        {renderedPrompt}
                      </pre>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Transform Results */}
          {transformResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span>Transform Results</span>
                  <div className="flex space-x-2">
                    {transformResult.cache_bypassed && (
                      <Badge variant="outline" className="text-blue-600">
                        Cache Bypassed
                      </Badge>
                    )}
                    {transformResult.structured_output && (
                      <Badge variant="outline" className="text-purple-600">
                        Structured Output
                      </Badge>
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Transformed Value</Label>
                  <div className="bg-green-50 p-3 rounded border border-green-200">
                    <p className="text-sm font-mono break-words whitespace-pre-wrap overflow-wrap-anywhere">{transformResult.result}</p>
                  </div>
                </div>

                {transformResult.reasoning && showReasoning && (
                  <div>
                    <Label className="text-sm font-medium">AI Reasoning</Label>
                    <div className="bg-blue-50 p-3 rounded border border-blue-200 max-h-48 overflow-y-auto">
                      <p className="text-sm break-words whitespace-pre-wrap overflow-wrap-anywhere">{transformResult.reasoning}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
            {transformResult && (
              <Button
                onClick={handleTransform}
                disabled={isTransforming}
                variant="outline"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Transform Again
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 