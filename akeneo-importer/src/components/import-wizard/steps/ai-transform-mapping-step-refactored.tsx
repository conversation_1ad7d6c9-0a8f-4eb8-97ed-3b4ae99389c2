"use client";

import { useState, useEffect, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { 
  ChevronLeft,
  ChevronRight,
  Brain,
  Save,
  Loader2
} from "lucide-react";
import { useImportWizardStore, type MappingType } from "@/stores/import-wizard-store";
import { useAITransformColumn } from "@/hooks/use-ai-transform-column";
import { useLLMDefaults } from "@/hooks/use-llm-defaults";

// Import our new components
import { SourceDataPreview } from "./components/SourceDataPreview";
import { PromptTemplateEditor } from "./components/PromptTemplateEditor";
import { LLMTester } from "./components/LLMTester";
import { ColumnProgressTracker } from "./components/ColumnProgressTracker";
import { ColumnConfigurationPanel } from "./components/ColumnConfigurationPanel";

export function AiTransformMappingStepRefactored() {
  const {
    selectedTargetColumns,
    oneToOneMappings,
    columnMappingTypes,
    setAiTransformMappings,
    setCurrentStep,
    jobId,
    markStepCompleted,
    selectedLlmModel,
    setSelectedLlmModel
  } = useImportWizardStore();

  // Get columns that are set for AI transform mapping
  const aiTransformColumns = useMemo(() => {
    return selectedTargetColumns.filter(col => 
      columnMappingTypes[col] === 'ai-transform'
    );
  }, [selectedTargetColumns, columnMappingTypes]);

  // Use our custom hook for AI transform column logic
  const {
    // Column state
    configuredColumns,
    currentColumn,
    currentColumnIndex,
    totalColumns,
    progressPercentage,
    allColumnsConfigured,
    
    // Current column details
    columnConfiguration,
    currentPromptTemplate,
    currentColumnModel,
    
    // Testing state
    isTestingLLM,
    llmResponse,
    renderedPrompt,
    isPromptRendered,
    
    // Loading states
    loadingConfiguration,
    isSavingTemplate,
    
    // Actions
    setCurrentColumnIndex,
    setCurrentPromptTemplate,
    setCurrentColumnModel,
    saveCurrentTemplate,
    saveAllTemplates,
    clearAllMappings,
    testPromptWithLLM,
    renderPromptWithPlaceholders,
    goToNextColumn,
    goToPreviousColumn,
  } = useAITransformColumn(aiTransformColumns, jobId, selectedLlmModel);

  // Additional state for UI components that aren't in the hook yet
  const [sourceRows, setSourceRows] = useState<Record<string, unknown>[]>([]);
  const [currentRowIndex, setCurrentRowIndex] = useState(0);
  const [loadingSourceData, setLoadingSourceData] = useState(false);
  const [sourceDataError, setSourceDataError] = useState('');
  
  // Prompt editor state
  const [showSystemPrompt, setShowSystemPrompt] = useState(false);
  const [systemPrompt, setSystemPrompt] = useState('');
  const [loadingSystemPrompt, setLoadingSystemPrompt] = useState(false);
  
  // Job notes state
  const [jobNotes, setJobNotes] = useState('');
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [loadingNotes, setLoadingNotes] = useState(false);
  const [savingNotes, setSavingNotes] = useState(false);
  
  // LLM tester state - use hook for defaults
  const { defaults: llmDefaults } = useLLMDefaults();
  const [temperature, setTemperature] = useState(llmDefaults.temperature);
  const [useStructuredOutput, setUseStructuredOutput] = useState(true);
  
  // Update temperature when defaults load
  useEffect(() => {
    if (!llmDefaults.loading && llmDefaults.temperature) {
      setTemperature(llmDefaults.temperature);
    }
  }, [llmDefaults.loading, llmDefaults.temperature]);
  const [showReasoning, setShowReasoning] = useState(false);
  const [showRawResponse, setShowRawResponse] = useState(false);
  
  // Additional data state
  const [worksheetData, setWorksheetData] = useState<any[]>([]);
  const [availableWorksheets, setAvailableWorksheets] = useState<string[]>([]);
  const [loadingWorksheet, setLoadingWorksheet] = useState(false);
  const [additionalDataError, setAdditionalDataError] = useState('');

  // Load source data when job ID changes
  useEffect(() => {
    const loadSourceData = async () => {
      if (!jobId) {
        setSourceDataError('No job ID available');
        return;
      }
      
      console.log('🔍 AI Transform Mapping - Loading source data for job ID:', jobId);
      setLoadingSourceData(true);
      setSourceDataError('');
      
      try {
        const url = `/api/import/grid-data?job_id=${jobId}&page=1&page_size=10`;
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (!result.success) {
          setSourceDataError(result.error || 'API returned success: false');
          toast.error('Data Loading Error', {
            description: result.error || 'Unknown error occurred'
          });
          return;
        }
        
        if (!result.data || !result.data.rows || result.data.rows.length === 0) {
          setSourceDataError('No data rows available in the response');
          toast.error('Empty Data Response', {
            description: 'The data source appears to be empty. Please re-upload your file.'
          });
          return;
        }

        setSourceRows(result.data.rows);
        toast.success('Source data loaded successfully', {
          description: `${result.data.rows.length} rows loaded for transformation`
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setSourceDataError(`Failed to load source data: ${errorMessage}`);
        console.error('🔍 AI Transform Mapping - Error:', error);
        toast.error('Failed to load source data', {
          description: 'Please check your connection and try again, or return to step 1 to re-upload.'
        });
      } finally {
        setLoadingSourceData(false);
      }
    };

    loadSourceData();
  }, [jobId]);

  // Load available worksheets
  useEffect(() => {
    const loadAvailableWorksheets = async () => {
      try {
        const response = await fetch('/api/google-sheets/worksheets');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setAvailableWorksheets(result.data || []);
          }
        }
      } catch (error) {
        console.warn('Could not load available worksheets:', error);
      }
    };
    
    loadAvailableWorksheets();
  }, []);

  // Load system prompt
  useEffect(() => {
    const loadSystemPrompt = async () => {
      setLoadingSystemPrompt(true);
      try {
        const response = await fetch('/api/settings/prompt/system');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.system_prompt) {
            setSystemPrompt(result.system_prompt);
          } else {
            setSystemPrompt('No system prompt configured');
          }
        } else {
          setSystemPrompt('Error loading system prompt');
        }
      } catch (error) {
        console.warn('Could not load system prompt:', error);
        setSystemPrompt('Error loading system prompt');
      } finally {
        setLoadingSystemPrompt(false);
      }
    };

    loadSystemPrompt();
  }, []);

  // Load job notes
  useEffect(() => {
    const loadJobNotes = async () => {
      if (!jobId) return;
      
      setLoadingNotes(true);
      try {
        const response = await fetch(`/api/jobs/${jobId}/notes`);
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setJobNotes(result.notes || '');
          }
        }
      } catch (error) {
        console.warn('Could not load job notes:', error);
      } finally {
        setLoadingNotes(false);
      }
    };
    
    loadJobNotes();
  }, [jobId]);

  // Helper functions
  const saveJobNotes = async () => {
    if (!jobId) return;
    
    setSavingNotes(true);
    try {
      const response = await fetch(`/api/jobs/${jobId}/notes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notes: jobNotes }),
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          toast.success('Job notes saved successfully');
          setShowNotesModal(false);
        } else {
          throw new Error(result.message || 'Failed to save notes');
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to save job notes:', error);
      toast.error('Failed to save job notes');
    } finally {
      setSavingNotes(false);
    }
  };

  const loadWorksheetData = async (columnName: string) => {
    setLoadingWorksheet(true);
    setAdditionalDataError('');
    
    try {
      if (availableWorksheets.includes(columnName)) {
        const response = await fetch(`/api/google-sheets/worksheets?name=${encodeURIComponent(columnName)}&use_cache=false`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success && result.data) {
          setWorksheetData(result.data);
          toast.success(`Loaded additional data for ${columnName}`);
        } else {
          throw new Error(result.message || 'No data returned');
        }
      } else {
        setWorksheetData([]);
      }
    } catch (error) {
      setAdditionalDataError(`Failed to load additional data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setWorksheetData([]);
      toast.error('Failed to load additional data');
    } finally {
      setLoadingWorksheet(false);
    }
  };

  const handleRenderPrompt = async () => {
    const currentRow = sourceRows[currentRowIndex];
    if (currentRow) {
      await renderPromptWithPlaceholders(currentRow, currentRowIndex, worksheetData, jobNotes);
    }
  };

  const handleTestPrompt = async () => {
    const currentRow = sourceRows[currentRowIndex];
    if (currentRow) {
      await testPromptWithLLM(currentRow, currentRowIndex);
    }
  };

  const handleShowSystemPrompt = () => {
    setShowSystemPrompt(!showSystemPrompt);
  };

  // Navigation handlers
  const handleNext = () => {
    if (!allColumnsConfigured) {
      toast.error('Please configure and save all AI transform columns before proceeding');
      return;
    }

    // Save all AI transform mappings
    const mappings: Record<string, string> = {};
    Object.values(configuredColumns).forEach((col) => {
      if (col.isSaved) {
        mappings[col.columnName] = col.promptTemplate;
      }
    });
    
    setAiTransformMappings(mappings);
    markStepCompleted(4);
    setCurrentStep(5); // Move to Data Preview & Transform
  };

  const handlePrevious = () => {
    setCurrentStep(2); // Go back to Target Column Selection
  };

  // Show message if no AI transform columns are available
  if (aiTransformColumns.length === 0) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Brain className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No AI Transform Columns</h3>
            <p className="text-gray-500 text-center mb-4">
              No columns are set for AI transformation. Go back to the previous step to configure columns for AI transformation.
            </p>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={handlePrevious}>
                Previous: Target Column Selection
              </Button>
              <Button onClick={handleNext}>
                Skip to Next Step
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Column Progress Tracker */}
      <ColumnProgressTracker
        currentColumn={currentColumn!}
        currentColumnIndex={currentColumnIndex}
        totalColumns={totalColumns}
        configuredColumns={configuredColumns}
        progressPercentage={progressPercentage}
        allColumnsConfigured={allColumnsConfigured}
        aiTransformColumns={aiTransformColumns}
        onColumnIndexChange={setCurrentColumnIndex}
        onGoToPreviousColumn={goToPreviousColumn}
        onGoToNextColumn={goToNextColumn}
        onSaveAllTemplates={saveAllTemplates}
        onClearAllMappings={clearAllMappings}
        isSavingTemplate={isSavingTemplate}
      />

      {/* Column Configuration Details */}
      {currentColumn && columnConfiguration && (
        <ColumnConfigurationPanel
          currentColumn={currentColumn}
          columnConfiguration={columnConfiguration}
          worksheetData={worksheetData}
          availableWorksheets={availableWorksheets}
          loadingWorksheet={loadingWorksheet}
          additionalDataError={additionalDataError}
          onLoadWorksheetData={loadWorksheetData}
        />
      )}

      {/* Three Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Source Data Panel */}
        <SourceDataPreview
          sourceRows={sourceRows}
          currentRowIndex={currentRowIndex}
          onRowIndexChange={setCurrentRowIndex}
          loading={loadingSourceData}
          error={sourceDataError}
        />

        {/* Prompt Engineering Panel */}
        <PromptTemplateEditor
          currentColumn={currentColumn!}
          currentPromptTemplate={currentPromptTemplate}
          onPromptTemplateChange={setCurrentPromptTemplate}
          renderedPrompt={renderedPrompt}
          isPromptRendered={isPromptRendered}
          systemPrompt={systemPrompt}
          loadingSystemPrompt={loadingSystemPrompt}
          onRenderPrompt={handleRenderPrompt}
          onShowSystemPrompt={handleShowSystemPrompt}
          showSystemPrompt={showSystemPrompt}
          jobNotes={jobNotes}
          onJobNotesChange={setJobNotes}
          onSaveJobNotes={saveJobNotes}
          showNotesModal={showNotesModal}
          onShowNotesModalChange={setShowNotesModal}
          loadingNotes={loadingNotes}
          savingNotes={savingNotes}
        />

        {/* Test LLM Panel */}
        <LLMTester
          currentColumn={currentColumn!}
          currentColumnModel={currentColumnModel}
          onCurrentColumnModelChange={setCurrentColumnModel}
          isTestingLLM={isTestingLLM}
          llmResponse={llmResponse}
          onTestPrompt={handleTestPrompt}
          temperature={temperature}
          onTemperatureChange={setTemperature}
          useStructuredOutput={useStructuredOutput}
          onUseStructuredOutputChange={setUseStructuredOutput}
          showReasoning={showReasoning}
          onShowReasoningChange={setShowReasoning}
          showRawResponse={showRawResponse}
          onShowRawResponseChange={setShowRawResponse}
        />
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous: Column Mapping
        </Button>
        
        <div className="flex items-center space-x-3">
          {!allColumnsConfigured && (
            <span className="text-sm text-gray-500">
              Configure all columns to proceed
            </span>
          )}
          
          {/* Save Current Template Button */}
          <Button 
            onClick={saveCurrentTemplate}
            disabled={isSavingTemplate || !currentPromptTemplate.trim()}
            size="sm"
            variant="outline"
          >
            {isSavingTemplate ? (
              <>
                <Loader2 className="h-3 w-3 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-3 w-3 mr-2" />
                Save Template
              </>
            )}
          </Button>
          
          <Button 
            onClick={handleNext}
            disabled={!allColumnsConfigured}
          >
            {allColumnsConfigured ? (
              <>
                Next: Data Preview & Transform
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            ) : (
              <>
                Configure All Columns First
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
} 