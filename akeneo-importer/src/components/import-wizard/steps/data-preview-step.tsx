"use client";

import { useState, useEffect, use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { 
  Play, 
  Pause, 
  Square,
  Download, 
  FileSpreadsheet, 
  FileText,
  Loader2,
  CheckCircle,
  AlertTriangle,
  Clock,
  BarChart3,
  Eye,
  Settings,
  ArrowRight,
  Brain,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Database,
  Ski<PERSON>For<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Wand2,
  Trash2
} from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useImportWizardStore, type MappingType } from "@/stores/import-wizard-store";
import { SingleColumnTransformDialog } from "./single-column-transform-dialog";
import { InlineModelSelector } from './inline-model-selector';
import { RowNavigation } from './components/RowNavigation';



interface TransformationJob {
  id: string;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'cancelled' | 'error';
  progress: number;
  processedRows: number;
  totalRows: number;
  processedCells: number;
  totalCells: number;
  currentRowCells: number;
  currentRowTotalCells: number;
  elapsedTime: number;
  estimatedTimeRemaining: number;
  startTime?: number;
  pauseTime?: number;
  error?: string;
}

interface TransformedData {
  [key: string]: any;
  _rowIndex: number;
  _transformationStatus: 'pending' | 'processing' | 'completed' | 'error';
  _transformationError?: string;
}

interface RowTransformationResult {
  rowIndex: number;
  sourceData: Record<string, any>;
  transformedData: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
}

interface DataPage {
  rows: any[];
  total_rows: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export function DataPreviewStep() {
  const { 
    jobId, 
    sourceColumns,
    selectedTargetColumns,
    oneToOneMappings,
    columnMappingTypes,
    aiTransformMappings,
    sourceData,
    selectedLlmModel,
    setSelectedLlmModel,
    setCurrentStep,
    reset
  } = useImportWizardStore();

  // Row selection and navigation
  const [selectedRowIndex, setSelectedRowIndex] = useState<number>(0);
  const [selectedRowsForTransform, setSelectedRowsForTransform] = useState<number[]>([]);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalRows, setTotalRows] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [loadingData, setLoadingData] = useState(false);
  const [numRowsToTransform, setNumRowsToTransform] = useState<number>(10);
  
  // Source data and results
  const [sourceDataRows, setSourceDataRows] = useState<any[]>([]);
  const [transformationResults, setTransformationResults] = useState<Record<number, RowTransformationResult>>({});
  
  // Job context data for transformations
  const [jobNotes, setJobNotes] = useState<string>('');
  const [worksheetData, setWorksheetData] = useState<any[]>([]);
  
  // Column model mappings
  const [columnModelMappings, setColumnModelMappings] = useState<Record<string, string | null>>({});
  
  // Transformation state
  const [transformationJobId, setTransformationJobId] = useState<string | null>(null);
  const [activeTaskIds, setActiveTaskIds] = useState<string[]>([]); // Track all active task IDs
  const [isTransforming, setIsTransforming] = useState(false);
  const [isCreatingTasks, setIsCreatingTasks] = useState(false); // Track when tasks are being created
  const [isCancelling, setIsCancelling] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  const [transformationProgress, setTransformationProgress] = useState<{
    totalRows: number;
    totalCells: number;
    processedRows: number;
    processedCells: number;
    currentRow: number;
    currentRowCells: number;
    currentRowTotalCells: number;
    startTime: number;
    elapsedTime: number;
    estimatedTimeRemaining: number;
    overallProgress: number;
  }>({
    totalRows: 0,
    totalCells: 0,
    processedRows: 0,
    processedCells: 0,
    currentRow: 0,
    currentRowCells: 0,
    currentRowTotalCells: 0,
    startTime: 0,
    elapsedTime: 0,
    estimatedTimeRemaining: 0,
    overallProgress: 0
  });
  
  // Timer for progress updates
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Cancellation state that can be accessed in async functions
  const cancellationRef = useRef<{ cancelled: boolean }>({ cancelled: false });
  
  // Single column transform state
  const [singleColumnTransformDialog, setSingleColumnTransformDialog] = useState<{
    isOpen: boolean;
    columnName: string;
    rowIndex: number;
    sourceRow: Record<string, any>;
    currentTransformValue?: string;
    promptTemplate?: string;
  }>({
    isOpen: false,
    columnName: '',
    rowIndex: 0,
    sourceRow: {},
    currentTransformValue: undefined,
    promptTemplate: undefined
  });



  // Load column model mappings from AI transform state
  const loadColumnModelMappings = async () => {
    if (!jobId) return;
    
    try {
      const response = await fetch(`/api/import/ai-transform-state?job_id=${jobId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.columnStates) {
          const mappings: Record<string, string | null> = {};
          Object.values(result.data.columnStates).forEach((columnState: any) => {
            if (columnState.columnName && columnState.selectedModel) {
              mappings[columnState.columnName] = columnState.selectedModel;
            }
          });
          setColumnModelMappings(mappings);
          console.log('✅ Loaded column model mappings:', mappings);
        }
      }
    } catch (error) {
      console.error('Failed to load column model mappings:', error);
    }
  };

  // Load source data and initialize
  useEffect(() => {
    loadSourceData(currentPage);
    loadJobNotes();
    loadWorksheetData();
    loadColumnModelMappings();
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [jobId, currentPage, pageSize]);

  // Load source data from API with pagination
  const loadSourceData = async (page: number = 1) => {
    if (!jobId) return;
    
    setLoadingData(true);
    try {
      const response = await fetch(`/api/import/grid-data?job_id=${jobId}&page=${page}&page_size=${pageSize}`);
      const result = await response.json();
      
      if (result.success && result.data) {
        const dataPage: DataPage = result.data;
        setSourceDataRows(dataPage.rows);
        setTotalRows(dataPage.total_rows);
        setTotalPages(dataPage.total_pages);
        
        // Initialize transformation results for current page
        initializeTransformationResults(dataPage.rows, (page - 1) * pageSize);
      }
    } catch (error) {
      console.error('Failed to load source data:', error);
      toast.error('Failed to load source data');
    } finally {
      setLoadingData(false);
    }
  };

  // Load job notes for transformation context
  const loadJobNotes = async () => {
    if (!jobId) return;
    
    try {
      const response = await fetch(`/api/jobs/${jobId}/notes`);
      if (response.ok) {
        const result = await response.json();
        setJobNotes(result.notes || '');
      }
    } catch (error) {
      console.error('Failed to load job notes:', error);
      // Don't show error toast as this is optional data
    }
  };

  // Load worksheet data for transformation context
  const loadWorksheetData = async () => {
    if (!jobId) return;
    
    try {
      // Try to load validation data that might be used for @prompt_additional_data
      const response = await fetch(`/api/import/mapping-definitions?job_id=${jobId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data && result.data.length > 0) {
          // Use the first column's validation data as default
          const firstColumnData = result.data[0];
          if (firstColumnData.validation_data) {
            setWorksheetData(firstColumnData.validation_data);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load worksheet data:', error);
      // Don't show error toast as this is optional data
    }
  };

  // Initialize transformation results for current page rows
  const initializeTransformationResults = (sourceRows: any[], startIndex: number = 0) => {
    const results: Record<number, RowTransformationResult> = {};
    
    sourceRows.forEach((row, index) => {
      const globalRowIndex = startIndex + index;
      results[globalRowIndex] = {
        rowIndex: globalRowIndex,
        sourceData: row,
        transformedData: {},
        status: 'pending'
      };
    });
    
    setTransformationResults(prev => ({
      ...prev,
      ...results
    }));
  };

  // Transform specific rows
  const transformSelectedRows = async (rowIndices: number[], bulkOperationId?: string) => {
    if (!jobId || rowIndices.length === 0) return;
    
    // Generate a bulk operation ID if not provided (use bulk_op_ prefix for consistency with cancellation logic)
    const operationId = bulkOperationId || `bulk_op_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    console.log(`🚀 [DATA_PREVIEW] Starting bulk transformation with operationId=${operationId}`);
    
    // Set the transformation job ID so cancellation can find it
    setTransformationJobId(operationId);
    
    // Store bulk operation in Redis for cancellation tracking
    try {
      const response = await fetch('/api/grid/bulk-operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId: jobId,
          operationId: operationId,
          type: 'ai-transform',
          config: {
            columns: selectedTargetColumns,
            rowIds: rowIndices.map(i => `row_${i}`), // Convert row indices to row IDs
            totalRows: rowIndices.length
          }
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.warn('Failed to create bulk operation tracker:', response.status, errorText);
      } else {
        const result = await response.json();
        console.log(`✅ [DATA_PREVIEW] Bulk operation created successfully:`, result);
      }
    } catch (error) {
      console.warn('Failed to create bulk operation tracker:', error);
    }
    
    const startTime = Date.now();
    const totalRows = rowIndices.length;
    const totalCells = totalRows * selectedTargetColumns.length;
    
    setIsTransforming(true);
    setTransformationProgress({
      totalRows,
      processedRows: 0,
      currentRow: 0,
      totalCells,
      processedCells: 0,
      currentRowCells: 0,
      currentRowTotalCells: selectedTargetColumns.length,
      startTime,
      elapsedTime: 0,
      estimatedTimeRemaining: 0,
      overallProgress: 0
    });
    
    // Start progress timer
    const progressTimer = setInterval(() => {
      setTransformationProgress(prev => ({
        ...prev,
        elapsedTime: Date.now() - startTime,
        estimatedTimeRemaining: prev.processedCells > 0 ? 
          Math.round(((Date.now() - startTime) / prev.processedCells) * (totalCells - prev.processedCells)) : 0
      }));
    }, 100);
    
    try {
      let processedCells = 0;
      
      for (let i = 0; i < rowIndices.length; i++) {
        // Check for cancellation before each row
        try {
          const cancelCheckResponse = await fetch(`/api/grid/bulk-operations/${operationId}/status`);
          if (cancelCheckResponse.ok) {
            const cancelResult = await cancelCheckResponse.json();
            if (cancelResult.success && cancelResult.status === 'cancelled') {
              console.log(`🛑 [DATA_PREVIEW] Bulk operation ${operationId} was cancelled`);
              clearInterval(progressTimer);
              setIsTransforming(false);
              toast.info('Transformation was cancelled');
              return;
            }
          }
        } catch (error) {
          // Ignore cancellation check errors to not interrupt transformation
          console.warn('Failed to check cancellation status:', error);
        }
        
        const globalRowIndex = rowIndices[i];
        const localRowIndex = globalRowIndex % pageSize;
        const sourceRow = sourceDataRows[localRowIndex];
        
        if (!sourceRow) {
          // Need to load different page
          const requiredPage = Math.floor(globalRowIndex / pageSize) + 1;
          if (requiredPage !== currentPage) {
            await loadSourceData(requiredPage);
            setCurrentPage(requiredPage);
          }
          continue;
        }
        
        // Update current row progress
        setTransformationProgress(prev => ({
          ...prev,
          currentRow: i + 1,
          currentRowCells: 0,
          processedRows: i
        }));
        
        // Update row status to processing
        setTransformationResults(prev => ({
          ...prev,
          [globalRowIndex]: {
            ...prev[globalRowIndex],
            status: 'processing'
          }
        }));
        
        // Transform each target column for this row
        const transformedData: Record<string, any> = {};
        
        for (let j = 0; j < selectedTargetColumns.length; j++) {
          const targetColumn = selectedTargetColumns[j];
          const mappingType = columnMappingTypes[targetColumn];
          
          if (mappingType === 'one-to-one') {
            // Simple one-to-one mapping
            const sourceColumn = oneToOneMappings[targetColumn];
            if (sourceColumn && sourceRow[sourceColumn] !== undefined) {
              transformedData[targetColumn] = sourceRow[sourceColumn];
            }
          } else if (mappingType === 'ai-transform') {
            // AI transformation
            const prompt = aiTransformMappings[targetColumn];
            if (prompt) {
              try {
                const transformedValue = await performAITransformation(sourceRow, targetColumn, prompt, operationId);
                transformedData[targetColumn] = transformedValue;
              } catch (error) {
                console.error(`AI transformation failed for column ${targetColumn}:`, error);
                // Check if this was a cancellation
                if (error instanceof Error && error.message === 'Operation cancelled') {
                  clearInterval(progressTimer);
                  setIsTransforming(false);
                  toast.info('Transformation was cancelled');
                  return;
                }
                transformedData[targetColumn] = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
              }
            }
          } else {
            // Default value
            transformedData[targetColumn] = '';
          }
          
          // Update progress after each cell is processed
          processedCells++;
          const overallProgress = Math.round((processedCells / totalCells) * 100);
          setTransformationProgress(prev => ({
            ...prev,
            currentRowCells: j + 1,
            processedCells,
            overallProgress
          }));
          
          // Small delay to show progress (remove in production for faster processing)
          await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        // Update row with completed transformation
        setTransformationResults(prev => ({
          ...prev,
          [globalRowIndex]: {
            ...prev[globalRowIndex],
            transformedData,
            status: 'completed'
          }
        }));
        
        // Update final progress for this row
        setTransformationProgress(prev => ({
          ...prev,
          processedRows: i + 1,
          currentRowCells: selectedTargetColumns.length
        }));
      }
      
      console.log(`✅ [DATA_PREVIEW] Bulk transformation completed for operationId=${operationId}`);
      toast.success(`Transformed ${rowIndices.length} rows successfully`);
      
      // Note: The bulk operation status will be updated automatically by the backend
      // No need to manually update status as completed
      
    } catch (error) {
      console.error('Transformation failed:', error);
      toast.error('Transformation failed');
      
      // Note: The bulk operation status will be updated automatically by the backend
      // No need to manually update status as failed
    } finally {
      clearInterval(progressTimer);
      setIsTransforming(false);
      setTransformationProgress({
        totalRows: 0,
        processedRows: 0,
        currentRow: 0,
        totalCells: 0,
        processedCells: 0,
        currentRowCells: 0,
        currentRowTotalCells: 0,
        startTime: 0,
        elapsedTime: 0,
        estimatedTimeRemaining: 0,
        overallProgress: 0
      });
    }
  };

  // Perform AI transformation for a single cell
  const performAITransformation = async (sourceRow: any, targetColumn: string, prompt: string, bulkOperationId?: string): Promise<string> => {
    console.log(`🔍 [DATA_PREVIEW] performAITransformation called for column=${targetColumn}, bulkOpId=${bulkOperationId || 'undefined'}`);
    console.log(`🔍 [DATA_PREVIEW] Call stack trace:`, new Error().stack?.split('\n').slice(1, 4));
    
    // Find the row index in the source data
    const rowIndex = sourceDataRows.findIndex(row => row === sourceRow);
    
    // Get the saved model for this column, fallback to global model
    const columnModel = columnModelMappings[targetColumn] || selectedLlmModel;
    
    console.log(`📡 [DATA_PREVIEW] Making HTTP call to /api/import/ai-transform with bulkOperationId=${bulkOperationId || 'undefined'}`);
    
    const response = await fetch('/api/import/ai-transform', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        job_id: jobId,
        column_name: targetColumn,
        prompt_template: prompt, // NEW: Use template mode instead of legacy
        row_index: rowIndex >= 0 ? rowIndex : 0,
        job_notes: jobNotes || '', // Add job notes for context
        worksheet_data: worksheetData || [], // Add worksheet data for @prompt_additional_data
        column_configuration: { // Add column configuration for @description, etc.
          column_name: targetColumn,
          description: `Column ${targetColumn}`,
          output_validation_column: targetColumn
        },
        model_id: columnModel, // Use column-specific model or fallback to global
        bulk_operation_id: bulkOperationId // NEW: Pass bulk operation ID for cancellation
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    // NEW: Check if the operation was cancelled
    if (result.cancelled) {
      console.log(`🛑 [DATA_PREVIEW] Individual transformation was cancelled for ${targetColumn}`);
      throw new Error('Operation cancelled');
    }
    
    if (!result.success) {
      throw new Error(result.error || 'AI transformation failed');
    }
    
    console.log(`✅ [DATA_PREVIEW] Individual transformation completed for ${targetColumn}`);
    
    // Use the parsed answer if available, otherwise fall back to result
    return result.answer || result.result || result.transformed_value || 'No result';
  };

  // Handle row selection for transformation
  const handleRowSelection = (action: 'single' | 'multiple' | 'all', count?: number) => {
    let rowsToTransform: number[] = [];
    
    if (action === 'single') {
      const globalRowIndex = (currentPage - 1) * pageSize + selectedRowIndex;
      rowsToTransform = [globalRowIndex];
    } else if (action === 'multiple' && count) {
      // Select first N rows from current page
      const startIndex = (currentPage - 1) * pageSize;
      rowsToTransform = Array.from({ length: Math.min(count, sourceDataRows.length) }, (_, i) => startIndex + i);
    } else if (action === 'all') {
      // Transform all rows in the dataset using bulk API
      return transformAllRows();
    }
    
    setSelectedRowsForTransform(rowsToTransform);
    transformSelectedRows(rowsToTransform);
  };

  // Cancel transformation job
  const cancelTransformation = async () => {
    console.log('🔍 DEBUG: Cancel button clicked');
    console.log('🔍 DEBUG: transformationJobId:', transformationJobId);
    console.log('🔍 DEBUG: activeTaskIds:', activeTaskIds);
    console.log('🔍 DEBUG: activeTaskIds.length:', activeTaskIds.length);
    console.log('🔍 DEBUG: isTransforming:', isTransforming);
    console.log('🔍 DEBUG: isCreatingTasks:', isCreatingTasks);
    console.log('🔍 DEBUG: isCancelling:', isCancelling);
    
    // Enhanced check - allow cancellation if we're transforming, even if task IDs aren't fully populated yet
    const hasActiveTransformation = transformationJobId || activeTaskIds.length > 0 || isTransforming || isCreatingTasks;
    
    if (!hasActiveTransformation) {
      console.warn('⚠️ No active transformation found');
      toast.error('No active transformation to cancel');
      return;
    }
    
    console.log('🛑 Attempting to cancel transformation tasks');
    console.log('🔍 DEBUG: Transformation state:', {
      transformationJobId,
      activeTaskIds,
      activeTaskCount: activeTaskIds.length,
      isTransforming,
      isCreatingTasks,
      hasActiveTransformation
    });
    
    setIsCancelling(true);
    
    try {
      let cancelledTasks = 0;
      let totalTasks = 0;
      const errors: string[] = [];
      
      // Set cancellation flag immediately to stop frontend processing
      cancellationRef.current.cancelled = true;
      console.log('🛑 Cancellation flag set - frontend processing will stop');
      
      // PRIORITY 1: Cancel bulk operation if it's a bulk operation ID
      if (transformationJobId && transformationJobId.startsWith('bulk_op_')) {
        console.log(`🔄 Cancelling bulk operation: ${transformationJobId}`);
        totalTasks++;
        try {
          const response = await fetch(`/api/grid/bulk-operations/${transformationJobId}/cancel`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          });
          
          if (response.ok) {
            const result = await response.json();
            console.log(`✅ Bulk operation ${transformationJobId} cancelled:`, result);
            cancelledTasks++;
          } else {
            const errorText = await response.text();
            console.error(`❌ Failed to cancel bulk operation ${transformationJobId}:`, response.status, errorText);
            errors.push(`Bulk operation: HTTP ${response.status}: ${response.statusText}`);
          }
        } catch (error) {
          console.error(`❌ Error cancelling bulk operation ${transformationJobId}:`, error);
          errors.push(`Bulk operation: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
      
      // PRIORITY 2: Cancel all individual task IDs
      if (activeTaskIds.length > 0) {
        console.log(`🔄 Cancelling ${activeTaskIds.length} individual tasks...`);
        totalTasks += activeTaskIds.length;
        
        const cancelPromises = activeTaskIds.map(async (taskId) => {
          try {
            console.log(`🔄 Cancelling task: ${taskId}`);
            const response = await fetch(`/api/transform/progress/${taskId}`, {
              method: 'DELETE',
              headers: { 'Content-Type': 'application/json' }
            });
            
            if (response.ok) {
              const result = await response.json();
              console.log(`✅ Task ${taskId} cancelled:`, result);
              return { taskId, success: true };
            } else {
              const errorText = await response.text();
              console.error(`❌ Failed to cancel task ${taskId}:`, response.status, errorText);
              return { taskId, success: false, error: `HTTP ${response.status}: ${response.statusText}` };
            }
          } catch (error) {
            console.error(`❌ Error cancelling task ${taskId}:`, error);
            return { taskId, success: false, error: error instanceof Error ? error.message : 'Unknown error' };
          }
        });
        
        const cancelResults = await Promise.allSettled(cancelPromises);
        
        cancelResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            if (result.value.success) {
              cancelledTasks++;
            } else {
              errors.push(`Task ${result.value.taskId}: ${result.value.error}`);
            }
          } else {
            errors.push(`Task ${activeTaskIds[index]}: ${result.reason}`);
          }
        });
      } else {
        console.log('🔍 DEBUG: No individual task IDs to cancel yet');
      }
      
      // PRIORITY 3: Cancel master task if it exists and follows the task: format (legacy support)
      if (transformationJobId?.startsWith('task:') && !transformationJobId.startsWith('bulk_op_')) {
        console.log(`🔄 Cancelling master task: ${transformationJobId}`);
        totalTasks++;
        try {
          const response = await fetch(`/api/transform/progress/${transformationJobId}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' }
          });
          
          if (response.ok) {
            const result = await response.json();
            console.log(`✅ Master task ${transformationJobId} cancelled:`, result);
            cancelledTasks++;
          } else {
            const errorText = await response.text();
            console.error(`❌ Failed to cancel master task ${transformationJobId}:`, response.status, errorText);
            errors.push(`Master task: HTTP ${response.status}: ${response.statusText}`);
          }
        } catch (error) {
          console.error(`❌ Error cancelling master task ${transformationJobId}:`, error);
          errors.push(`Master task: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
      
      // PRIORITY 4: Try legacy transformation job cancellation as fallback
      if (transformationJobId && !transformationJobId.startsWith('task:') && !transformationJobId.startsWith('bulk_op_')) {
        console.log(`🔄 Trying legacy transformation job cancellation: ${transformationJobId}`);
        totalTasks++;
        try {
          const response = await fetch(`/api/import/transformation-job/${transformationJobId}/cancel`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          });
          
          if (response.ok) {
            const result = await response.json();
            console.log(`✅ Legacy transformation job cancelled:`, result);
            if (result.success) {
              cancelledTasks++;
            } else {
              errors.push(`Legacy job: ${result.error || 'Failed to cancel'}`);
            }
          } else {
            const errorText = await response.text();
            console.error(`❌ Legacy cancellation failed:`, response.status, errorText);
            errors.push(`Legacy job: HTTP ${response.status}: ${response.statusText}`);
          }
        } catch (error) {
          console.error(`❌ Error with legacy cancellation:`, error);
          errors.push(`Legacy job: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
      
      console.log(`🔍 DEBUG: Cancellation results - ${cancelledTasks}/${totalTasks} tasks cancelled`);
      
      // Update UI state
      setIsCancelled(true);
      setIsTransforming(false);
      setActiveTaskIds([]);
      
      // Show appropriate message
      if ((isTransforming || isCreatingTasks) && totalTasks === 0) {
        // Special case: transformation was running but no tasks were tracked yet
        toast.info('Transformation cancellation requested - processing will stop');
        console.log('✅ Cancellation requested for in-progress transformation');
      } else if (cancelledTasks > 0) {
        if (errors.length > 0) {
          toast.warning(`Transformation partially cancelled: ${cancelledTasks}/${totalTasks} tasks cancelled. Some tasks may still be running.`);
          console.warn('⚠️ Partial cancellation errors:', errors);
        } else {
          toast.success(`Transformation cancelled successfully: ${cancelledTasks} task${cancelledTasks > 1 ? 's' : ''} cancelled`);
          console.log('✅ All transformation tasks cancelled successfully');
        }
      } else {
        toast.error('Failed to cancel transformation tasks');
        console.error('❌ No tasks were cancelled. Errors:', errors);
      }
      
    } catch (error) {
      console.error('Failed to cancel transformation:', error);
      toast.error(`Failed to cancel transformation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsCancelling(false);
    }
  };

  // Bulk transformation for all rows using the bulk API
  const transformAllRows = async () => {
    if (!jobId || selectedTargetColumns.length === 0) {
      toast.error('No job ID or target columns selected');
      return;
    }

    const startTime = Date.now();
    let isCancelled = false;
    setIsTransforming(true);
    setIsCreatingTasks(true);
    
    // Create a REAL bulk operation ID for proper cancellation tracking
    const bulkOperationId = `bulk_op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setTransformationJobId(bulkOperationId);
    console.log('🔍 DEBUG: Created bulk operation ID for cancellation:', bulkOperationId);
    
    // Initialize bulk operation in Redis for immediate cancellation support
    try {
      const initResponse = await fetch('/api/grid/bulk-operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId: jobId,
          operationId: bulkOperationId,
          type: 'ai-transform',
          config: {
            columns: selectedTargetColumns,
            totalRows: sourceDataRows.length,
            status: 'running' // Initialize as running so it can be cancelled
          }
        })
      });
      
      if (!initResponse.ok) {
        console.warn('Failed to initialize bulk operation for cancellation tracking');
      }
    } catch (error) {
      console.warn('Failed to initialize bulk operation:', error);
    }
    
    // Reset active task IDs - this will be populated as each column task is created
    setActiveTaskIds([]);
    
    // Reset cancellation state
    cancellationRef.current.cancelled = false;
    
    // Reset progress state
    setTransformationProgress({
      totalRows: sourceDataRows.length,
      totalCells: sourceDataRows.length * selectedTargetColumns.length,
      processedRows: 0,
      processedCells: 0,
      currentRow: 0,
      currentRowCells: 0,
      currentRowTotalCells: selectedTargetColumns.length,
      startTime,
      elapsedTime: 0,
      estimatedTimeRemaining: 0,
      overallProgress: 0
    });
    
    // Start progress timer
    const progressTimer = setInterval(() => {
      setTransformationProgress(prev => ({
        ...prev,
        elapsedTime: Date.now() - startTime
      }));
    }, 100);
    
    try {
      // Process each target column using the concurrent API
      const columnResults = [];
      for (const targetColumn of selectedTargetColumns) {
        if (cancellationRef.current.cancelled) {
          console.log(`🛑 Halting column processing due to cancellation.`);
          isCancelled = true;
          break; 
        }
        
        try {
          const mappingType = columnMappingTypes[targetColumn];
          
          let transformationMode: string;
          let sourceColumn: string | undefined;
          let stringValue: string | undefined;
          let aiPrompt: string | undefined;
          
          switch (mappingType) {
            case 'one-to-one':
              transformationMode = '1to1';
              sourceColumn = oneToOneMappings[targetColumn];
              break;
            case 'ai-transform':
              transformationMode = 'ai_transform';
              aiPrompt = aiTransformMappings[targetColumn];
              break;
            default:
              transformationMode = 'deactivated';
              break;
          }
          
          if (transformationMode === 'deactivated') {
            columnResults.push({ 
              status: 'fulfilled', 
              value: { targetColumn, success: true, skipped: true }
            });
            continue;
          }
          
          console.log(`🚀 Starting transformation for column: ${targetColumn}`);
          
          const dynamicConcurrency = sourceDataRows.length > 1 ? 10 : 1;
          
          const response = await fetch('/api/transform/bulk', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              job_id: jobId,
              column_name: targetColumn,
              transformation_mode: transformationMode,
              source_column: sourceColumn,
              string_value: stringValue,
              ai_prompt: aiPrompt,
              batch_size: 5,
              concurrency: dynamicConcurrency,
              bulk_operation_id: bulkOperationId // Pass bulk operation ID for cancellation tracking
            })
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText} for column ${targetColumn}`);
          }
          
          const result = await response.json();
          
          if (!result.success) {
            throw new Error(result.message || `Transformation failed for column ${targetColumn}`);
          }
          
          console.log(`✅ Column ${targetColumn} completed with ${result.worker_stats?.totalWorkers || 1} workers`);
          
          if (result.task_id) {
            console.log(`📋 Adding task ID to active tasks: ${result.task_id} for column ${targetColumn}`);
            setActiveTaskIds(prev => {
              const updated = [...prev, result.task_id];
              console.log(`🔍 DEBUG: Updated activeTaskIds:`, updated);
              return updated;
            });
          } else {
            console.warn(`⚠️ No task ID returned for column ${targetColumn}. Cancellation may not work for this column.`);
          }
          
          columnResults.push({
            status: 'fulfilled',
            value: {
              targetColumn,
              success: true,
              taskId: result.task_id,
              workerStats: result.worker_stats,
              processingTime: result.processing_time
            }
          });
          
        } catch (error) {
          columnResults.push({ status: 'rejected', reason: error });
        }
      }
      
      console.log(`🔄 Processing complete. Results collected for ${columnResults.length} columns.`);
      setIsCreatingTasks(false); 
      
      // Check if cancelled during processing
      if (cancellationRef.current.cancelled) {
        console.log('🛑 Transformation was cancelled during processing');
        isCancelled = true;
      }
      
      // Process results and collect all task IDs for final verification
      let totalProcessedCells = 0;
      let totalFailedColumns = 0;
      const errors: string[] = [];
      const allTaskIds: string[] = [];
      
      columnResults.forEach((result, index) => {
        const targetColumn = selectedTargetColumns[index];
        
        if (result.status === 'fulfilled' && result.value) {
          if (result.value.success) {
            if (!result.value.skipped) {
              totalProcessedCells += sourceDataRows.length; // Each successful column processes all rows
              const processingTime = (result.value as any).processingTime || 0;
              console.log(`✅ Column ${targetColumn}: Success (${processingTime}ms)`);
              
              // Collect task ID for final verification
              const taskId = (result.value as any).taskId;
              if (taskId) {
                allTaskIds.push(taskId);
              }
            }
          }
        } else if (result.status === 'rejected') {
          totalFailedColumns++;
          const error = result.reason;
          errors.push(`Column ${targetColumn}: ${error}`);
          console.error(`❌ Column ${targetColumn}: Failed -`, error);
        }
      });
      
      // Final update of activeTaskIds with all collected task IDs
      console.log(`🔍 DEBUG: Final task IDs collected:`, allTaskIds);
      setActiveTaskIds(allTaskIds);
      
      // CRITICAL FIX: Verify transformations were actually applied
      console.log('🔍 Verifying transformations were applied...');
      const verificationResults = await verifyTransformationsApplied(allTaskIds);
      
      if (verificationResults.hasTransformations) {
        // Load transformed data with transformation results
        await loadTransformedData(currentPage);
        
        // Update final progress
        setTransformationProgress(prev => ({
          ...prev,
          processedRows: sourceDataRows.length,
          processedCells: totalProcessedCells,
          overallProgress: 100
        }));
        
        if (isCancelled) {
          toast.info('Transformation was cancelled');
        } else if (totalFailedColumns > 0) {
          toast.warning(`Transformation completed with ${totalFailedColumns} failed columns. Check console for details.`);
          errors.forEach(error => console.error(error));
        } else {
          const successfulColumns = selectedTargetColumns.length - totalFailedColumns;
          toast.success(`Successfully transformed ${successfulColumns} columns for ${sourceDataRows.length} rows! ${verificationResults.appliedTransformations} transformations applied.`);
        }
      } else {
        // No transformations were applied - this is the real issue
        console.error('❌ Transformations completed but no results were applied to the data');
        toast.error('Transformation jobs completed but no data was transformed. Please check the transformation configuration.');
        
        // Still load the original data to show current state
        await loadSourceData(currentPage);
      }
      
    } catch (error) {
      console.error('Bulk transformation failed:', error);
      if (!isCancelled) {
        toast.error('Bulk transformation failed');
      }
    } finally {
      clearInterval(progressTimer);
      setIsTransforming(false);
      setIsCreatingTasks(false);
      // Reset cancellation state
      cancellationRef.current.cancelled = false;
      // Clear active task IDs immediately after a short delay to allow cancellation to work
      setTimeout(() => {
        setActiveTaskIds([]);
      }, 5000); // Keep task IDs for 5 seconds after completion for debugging/cancellation
      // Clear job ID after completion
      setTimeout(() => {
        setTransformationJobId(null);
      }, 5000); // Keep it for 5 seconds for debugging
    }
  };

  // NEW: Function to verify transformations were actually applied
  const verifyTransformationsApplied = async (taskIds: string[]): Promise<{
    hasTransformations: boolean;
    appliedTransformations: number;
    errors: string[];
  }> => {
    if (!jobId || taskIds.length === 0) {
      return { hasTransformations: false, appliedTransformations: 0, errors: [] };
    }

    try {
      // Check if transformation history exists for this job
      const response = await fetch(`/api/import/transformation-history?job_id=${jobId}`);
      if (!response.ok) {
        console.warn('Could not fetch transformation history');
        return { hasTransformations: false, appliedTransformations: 0, errors: ['Could not verify transformations'] };
      }

      const result = await response.json();
      if (result.success && result.data && result.data.length > 0) {
        console.log(`✅ Verified: ${result.data.length} transformations applied`);
        return { 
          hasTransformations: true, 
          appliedTransformations: result.data.length, 
          errors: [] 
        };
      } else {
        console.warn('No transformation history found');
        return { hasTransformations: false, appliedTransformations: 0, errors: [] };
      }
    } catch (error) {
      console.error('Error verifying transformations:', error);
      return { hasTransformations: false, appliedTransformations: 0, errors: [error instanceof Error ? error.message : 'Unknown error'] };
    }
  };

  // NEW: Function to load data with transformation results
  const loadTransformedData = async (page: number = 1) => {
    if (!jobId) return;
    
    setLoadingData(true);
    try {
      // Load source data
      const response = await fetch(`/api/import/grid-data?job_id=${jobId}&page=${page}&page_size=${pageSize}`);
      const result = await response.json();
      
      if (result.success && result.data) {
        const dataPage: DataPage = result.data;
        
        // Load transformation results for current page rows
        const transformedRows = await loadTransformationResults(dataPage.rows, (page - 1) * pageSize);
        
        setSourceDataRows(transformedRows);
        setTotalRows(dataPage.total_rows);
        setTotalPages(dataPage.total_pages);
        
        // Initialize transformation results for current page
        initializeTransformationResults(transformedRows, (page - 1) * pageSize);
      } else {
        console.error('Failed to load transformed data:', result.error);
        // Fallback to regular source data loading
        await loadSourceData(page);
      }
    } catch (error) {
      console.error('Failed to load transformed data:', error);
      // Fallback to regular source data loading
      await loadSourceData(page);
    } finally {
      setLoadingData(false);
    }
  };

  // NEW: Function to load and apply transformation results to rows
  const loadTransformationResults = async (sourceRows: any[], startIndex: number): Promise<any[]> => {
    if (!jobId) return sourceRows;

    try {
      // Fetch transformed data for this job
      const response = await fetch(`/api/import/transformed-data?job_id=${jobId}`);
      if (!response.ok) {
        console.warn('Could not fetch transformed data');
        return sourceRows;
      }

      const result = await response.json();
      if (!result.success || !result.data || Object.keys(result.data).length === 0) {
        console.warn('No transformed data available');
        return sourceRows;
      }

      // Apply transformations to the rows
      const transformedRows = sourceRows.map((row, index) => {
        const globalRowIndex = startIndex + index;
        const transformedRow = { ...row };

        // Apply transformations for this row if they exist
        const rowTransformations = result.data[globalRowIndex.toString()];
        if (rowTransformations) {
          // Apply each transformation to the row
          Object.entries(rowTransformations).forEach(([columnName, value]) => {
            transformedRow[`target_${columnName}`] = value;
          });
        }

        return transformedRow;
      });

      console.log(`✅ Applied transformations to ${transformedRows.length} rows from Redis store`);
      return transformedRows;
    } catch (error) {
      console.error('Error loading transformation results:', error);
      return sourceRows;
    }
  };

  // Navigation handlers
  const goToPreviousRow = () => {
    if (selectedRowIndex > 0) {
      setSelectedRowIndex(prev => prev - 1);
    } else if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
      setSelectedRowIndex(pageSize - 1);
    }
  };

  const goToNextRow = () => {
    if (selectedRowIndex < sourceDataRows.length - 1) {
      setSelectedRowIndex(prev => prev + 1);
    } else if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
      setSelectedRowIndex(0);
    }
  };

  const goToFirstRow = () => {
    setCurrentPage(1);
    setSelectedRowIndex(0);
  };

  const goToLastRow = () => {
    setCurrentPage(totalPages);
    setSelectedRowIndex(0); // Will be adjusted when data loads
  };

  const goToSpecificRow = (rowNumber: number) => {
    if (rowNumber < 1 || rowNumber > totalRows) return;
    
    const targetPage = Math.ceil(rowNumber / pageSize);
    const targetRowIndex = (rowNumber - 1) % pageSize;
    
    setCurrentPage(targetPage);
    setSelectedRowIndex(targetRowIndex);
  };

  // Pagination handlers
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
      setSelectedRowIndex(0);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
      setSelectedRowIndex(0);
    }
  };



  // Clear transformation cache functionality
  const clearTransformationCache = async (type: 'current' | 'all') => {
    if (!jobId) {
      toast.error('No job ID available');
      return;
    }

    try {
      const payload: any = { jobId };
      
      if (type === 'current') {
        payload.rowIndex = globalRowIndex;
      } else {
        payload.allRows = true;
      }

      const response = await fetch('/api/import/clear-transformation-cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        if (type === 'current') {
          // Update local state to reflect cleared cache for current row
          setTransformationResults(prev => {
            const newResults = { ...prev };
            if (newResults[globalRowIndex]) {
              newResults[globalRowIndex].transformedData = {};
              newResults[globalRowIndex].status = 'pending';
              delete newResults[globalRowIndex].error;
            }
            return newResults;
          });
          toast.success(`Cache cleared for row ${globalRowIndex + 1}`);
        } else {
          // Clear all transformation results
          setTransformationResults({});
          toast.success(`Cache cleared for all ${result.clearedCount || 0} rows`);
        }
      } else {
        toast.error(result.error || 'Failed to clear cache');
      }
    } catch (error) {
      console.error('Error clearing transformation cache:', error);
      toast.error('Failed to clear transformation cache');
    }
  };

  // Export functionality
  const exportData = async (format: 'excel' | 'csv') => {
    if (!jobId) {
      toast.error('No job ID available for export');
      return;
    }

    try {
      const response = await fetch(`/api/import/jobs/${jobId}/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          format, 
          includeTransformed: true 
        })
      });
      
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Export failed');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transformed_data_${jobId}.${format === 'excel' ? 'xlsx' : 'csv'}`;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Data exported successfully!');
    } catch (error) {
      console.error('Export failed:', error);
      toast.error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Get current row data
  const currentRowData = sourceDataRows[selectedRowIndex];
  const globalRowIndex = (currentPage - 1) * pageSize + selectedRowIndex;
  const currentRowResult = transformationResults[globalRowIndex];

  // Format duration for display
  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Get mapping type display info
  const getMappingTypeDisplay = (targetColumn: string) => {
    const mappingType = columnMappingTypes[targetColumn];
    
    switch (mappingType) {
      case 'one-to-one':
        return {
          label: '1:1 Mapping',
          icon: ArrowRight,
          color: 'text-blue-600',
          bg: 'bg-blue-50'
        };
      case 'ai-transform':
        return {
          label: 'AI Transform',
          icon: Brain,
          color: 'text-purple-600',
          bg: 'bg-purple-50'
        };
      default:
        return {
          label: 'Default',
          icon: Settings,
          color: 'text-gray-600',
          bg: 'bg-gray-50'
        };
    }
  };

  const handlePrevious = () => {
    setCurrentStep(3); // Go back to AI Transform Mapping (renumbered from 4 to 3)
  };

  const handleComplete = () => {
    // This was step 6, but since we only have 4 steps now, we'll complete the wizard
    // Could navigate to a success page or back to jobs list
    window.location.href = '/jobs';
  };

  // Single column transform functions
  const openSingleColumnTransform = (columnName: string, rowIndex: number) => {
    const sourceRow = sourceDataRows[rowIndex] || {};
    const currentResult = transformationResults[rowIndex];
    const currentTransformValue = currentResult?.transformedData[columnName];
    const promptTemplate = aiTransformMappings[columnName];
    
    setSingleColumnTransformDialog({
      isOpen: true,
      columnName,
      rowIndex,
      sourceRow,
      currentTransformValue: currentTransformValue ? String(currentTransformValue) : undefined,
      promptTemplate
    });
  };

  const closeSingleColumnTransform = () => {
    setSingleColumnTransformDialog(prev => ({
      ...prev,
      isOpen: false
    }));
  };

  const handleSingleColumnTransformComplete = (result: {
    column_name: string;
    row_index: number;
    result: string;
    reasoning?: string;
  }) => {
    // Update the transformation results for this specific cell
    setTransformationResults(prev => {
      const updated = { ...prev };
      if (!updated[result.row_index]) {
        // Initialize the row if it doesn't exist
        updated[result.row_index] = {
          rowIndex: result.row_index,
          sourceData: sourceDataRows[result.row_index] || {},
          transformedData: {},
          status: 'pending'
        };
      }
      
      // Update the specific column value
      updated[result.row_index] = {
        ...updated[result.row_index],
        transformedData: {
          ...updated[result.row_index].transformedData,
          [result.column_name]: result.result
        },
        status: 'completed' // Mark as completed since we have at least one transformed column
      };
      
      return updated;
    });
    
    toast.success(`Column ${result.column_name} transformed successfully!`);
  };

  return (
    <div className="space-y-6">
      {/* Main Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Data Preview & Transformation</span>
              </CardTitle>
              <CardDescription>
                Preview your data and run transformations on selected rows
              </CardDescription>
            </div>
            <Badge variant="secondary">Step 4 of 4</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Data Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{totalRows.toLocaleString()}</div>
                <div className="text-sm text-gray-600">Total Rows</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{selectedTargetColumns.length}</div>
                <div className="text-sm text-gray-600">Target Columns</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Object.values(transformationResults).filter(r => r.status === 'completed').length}
                </div>
                <div className="text-sm text-gray-600">Rows Transformed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round((Object.values(transformationResults).filter(r => r.status === 'completed').length / totalRows) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Progress</div>
              </div>
            </div>

            {/* Unified Horizontal Control Bar */}
            <div className="bg-white border rounded-lg shadow-sm">
              {/* Main Controls Row */}
              <div className="flex items-center justify-between p-4 border-b">
                {/* Transform Controls */}
                <div className="flex items-center gap-3">
                  <span className="text-sm font-semibold text-gray-800">Transform:</span>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRowSelection('single')}
                      disabled={isTransforming || loadingData}
                      className="h-9 px-4 text-sm font-medium"
                    >
                      {isTransforming ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                      Current Row
                    </Button>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        value={numRowsToTransform}
                        onChange={(e) => setNumRowsToTransform(parseInt(e.target.value, 10))}
                        className="w-20 h-9 text-sm"
                        min="1"
                        max={totalRows}
                        disabled={isTransforming || loadingData}
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleRowSelection('multiple', numRowsToTransform)}
                        disabled={isTransforming || loadingData || !numRowsToTransform || numRowsToTransform <= 0}
                        className="h-9 px-4 text-sm"
                      >
                        Transform X Rows
                      </Button>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleRowSelection('all')}
                      disabled={isTransforming || loadingData}
                      className="bg-blue-600 hover:bg-blue-700 h-9 px-4 text-sm font-medium"
                    >
                      All Rows ({totalRows.toLocaleString()})
                    </Button>
                  </div>
                </div>

                {/* AI Model Selector */}
                <div className="flex items-center gap-3 px-4 border-l">
                  <span className="text-sm font-semibold text-gray-800">AI Model:</span>
                  <div className="min-w-[280px]">
                    <InlineModelSelector
                      selectedModel={selectedLlmModel}
                      onModelChange={setSelectedLlmModel}
                      disabled={isTransforming || loadingData}
                    />
                  </div>
                </div>
              </div>

              {/* Navigation and Actions Row */}
              <div className="flex items-center justify-between p-4">
                {/* Row Navigation - Using New Reusable Component */}
                <div className="flex items-center gap-4">
                  <span className="text-sm font-semibold text-gray-800">Row Navigation:</span>
                  <RowNavigation
                    currentRowIndex={selectedRowIndex}
                    totalRows={sourceDataRows.length}
                    onRowIndexChange={setSelectedRowIndex}
                    disabled={isTransforming || loadingData}
                    size="md"
                    showJumpToRow={true}
                    showFirstLast={true}
                    label="Row"
                  />
                  
                  {/* Current Position Display */}
                  <div className="flex items-center gap-4 text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md">
                    <span>Page {currentPage} of {totalPages}</span>
                    <span className="border-l pl-4">Global Row {((currentPage - 1) * pageSize + selectedRowIndex) + 1} of {totalRows}</span>
                  </div>
                </div>

                {/* Row Status Display */}
                <div className="flex items-center gap-3 border-l pl-4">
                  <div className="text-sm text-gray-500 font-medium">
                    {totalRows === 0 ? 'No rows' : `${totalRows} rows processed`}
                  </div>
                  
                  {/* Cache Management Buttons */}
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => clearTransformationCache('current')}
                      disabled={isTransforming || !currentRowResult}
                      className="text-xs"
                    >
                      <Trash2 className="mr-1 h-3 w-3" />
                      Clear Current
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => clearTransformationCache('all')}
                      disabled={isTransforming || Object.keys(transformationResults).length === 0}
                      className="text-xs"
                    >
                      <Trash2 className="mr-1 h-3 w-3" />
                      Clear All Cache
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Information */}
            {isTransforming && (
              <div className="space-y-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span>
                    Row {transformationProgress.currentRow} of {transformationProgress.totalRows} 
                    {transformationProgress.currentRowTotalCells > 0 && (
                      <span className="text-gray-500 ml-2">
                        - Cell {transformationProgress.currentRowCells} of {transformationProgress.currentRowTotalCells}
                      </span>
                    )}
                  </span>
                  <span className="flex items-center space-x-4">
                    <span className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>Elapsed: {formatDuration(transformationProgress.elapsedTime)}</span>
                    </span>
                    {transformationProgress.estimatedTimeRemaining > 0 && (
                      <span className="flex items-center space-x-1">
                        <BarChart3 className="h-3 w-3" />
                        <span>ETA: {formatDuration(transformationProgress.estimatedTimeRemaining)}</span>
                      </span>
                    )}
                  </span>
                </div>
                <Progress value={transformationProgress.overallProgress} className="h-2" />
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>
                    Cells: {transformationProgress.processedCells} of {transformationProgress.totalCells}
                  </span>
                  <span>{transformationProgress.overallProgress}% complete</span>
                </div>
                {/* Cancel Button */}
                <div className="flex justify-center pt-2">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={cancelTransformation}
                    disabled={isCancelling || (!transformationJobId && activeTaskIds.length === 0 && !isTransforming && !isCreatingTasks)}
                    className="flex items-center space-x-2"
                    title={(!transformationJobId && activeTaskIds.length === 0 && !isTransforming && !isCreatingTasks) ? "No active transformation to cancel" : "Cancel running transformation"}
                  >
                    {isCancelling ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Cancelling...</span>
                      </>
                    ) : (
                      <>
                        <Square className="h-4 w-4" />
                        <span>Cancel Transformation</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Cancelled State */}
            {isCancelled && (
              <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center space-x-2 text-orange-800">
                  <AlertTriangle className="h-5 w-5" />
                  <span className="font-medium">Transformation Cancelled</span>
                </div>
                <p className="text-sm text-orange-700 mt-2">
                  The bulk transformation job was cancelled. You can restart the transformation at any time.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Transposed Data View */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Source Data Panel */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Source Data</span>
            </CardTitle>
            <CardDescription>
              Row {globalRowIndex + 1} of {totalRows}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {currentRowData ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-1/3">Column</TableHead>
                      <TableHead>Value</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sourceColumns.map((column) => (
                      <TableRow key={column}>
                        <TableCell className="font-medium text-sm">{column}</TableCell>
                        <TableCell className="text-sm">{currentRowData[column] || ''}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  {loadingData ? 'Loading data...' : 'No source data available'}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Transformation Results Panel - Now Expanded */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="h-5 w-5" />
              <span>Transformation Results</span>
            </CardTitle>
            <CardDescription>
              LLM processed output
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {currentRowResult ? (
                <>
                  <div className="flex items-center space-x-2 mb-4">
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
                      currentRowResult.status === 'completed' ? 'bg-green-100 text-green-700' :
                      currentRowResult.status === 'processing' ? 'bg-blue-100 text-blue-700' :
                      currentRowResult.status === 'error' ? 'bg-red-100 text-red-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {currentRowResult.status === 'completed' && <CheckCircle className="h-3 w-3" />}
                      {currentRowResult.status === 'processing' && <Loader2 className="h-3 w-3 animate-spin" />}
                      {currentRowResult.status === 'error' && <AlertTriangle className="h-3 w-3" />}
                      {currentRowResult.status === 'pending' && <Clock className="h-3 w-3" />}
                      <span className="capitalize">{currentRowResult.status}</span>
                    </div>
                  </div>
                  
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Target Column</TableHead>
                        <TableHead>Result</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedTargetColumns.map((targetColumn) => {
                        // Get the transformation result value for this column
                        const transformedValue = currentRowResult.transformedData[targetColumn];
                        
                        // Check if this column has AI transform mapping
                        const hasAiTransform = columnMappingTypes[targetColumn] === 'ai-transform';
                        const hasPromptTemplate = aiTransformMappings[targetColumn];
                        
                        // Helper function to safely render transformation results
                        const renderTransformationValue = (value: any) => {
                          if (value === null || value === undefined) {
                            return <span className="text-gray-400 italic">Not processed</span>;
                          }
                          
                          // If it's a complex object, try to extract meaningful content
                          if (typeof value === 'object') {
                            console.log('🔍 Complex transformation result detected:', value);
                            
                            // Handle objects with specific keys that might be returned by AI transformations
                            if (value.notes || value.row || value.description || value.prompt_additional_data || value.output_validation_field) {
                              // Extract the main result or description
                              const displayValue = value.description || value.notes || value.output_validation_field || JSON.stringify(value);
                              console.log('🔍 Extracted display value:', displayValue);
                              return <span className="text-sm">{String(displayValue)}</span>;
                            }
                            
                            // For other objects, safely stringify
                            try {
                              return <span className="text-sm font-mono text-xs">{JSON.stringify(value, null, 2)}</span>;
                            } catch (error) {
                              console.error('🔍 Error stringifying transformation result:', error);
                              return <span className="text-red-500 text-xs">Error displaying result</span>;
                            }
                          }
                          
                          // For primitive values, render directly
                          return <span className="text-sm">{String(value)}</span>;
                        };
                        
                        return (
                          <TableRow key={targetColumn}>
                            <TableCell className="font-medium text-sm">{targetColumn}</TableCell>
                            <TableCell>
                              {renderTransformationValue(transformedValue)}
                            </TableCell>
                            <TableCell>
                              {hasAiTransform && hasPromptTemplate && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => openSingleColumnTransform(targetColumn, selectedRowIndex)}
                                  className="flex items-center space-x-1"
                                  title={`Transform ${targetColumn} for row ${selectedRowIndex + 1}`}
                                >
                                  <Wand2 className="h-3 w-3" />
                                  <span className="hidden sm:inline">Transform</span>
                                </Button>
                              )}
                              {!hasAiTransform && (
                                <span className="text-xs text-gray-400">No AI transform</span>
                              )}
                              {hasAiTransform && !hasPromptTemplate && (
                                <span className="text-xs text-amber-600">No prompt configured</span>
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                  
                  {currentRowResult.error && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-700">{currentRowResult.error}</p>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  No transformation results yet
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Transformed Data Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Transformed Data Grid</span>
          </CardTitle>
          <CardDescription>
            View multiple rows of transformed data in a tabular format
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.keys(transformationResults).length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">Row</TableHead>
                      {selectedTargetColumns.map((column) => (
                        <TableHead key={column} className="min-w-[150px]">
                          <div className="flex items-center space-x-1">
                            <span>{column}</span>
                            {columnMappingTypes[column] === 'ai-transform' && (
                              <div className="flex items-center space-x-1 px-1 py-0.5 rounded text-xs bg-purple-100 text-purple-700">
                                <Brain className="h-3 w-3" />
                                <span>AI</span>
                              </div>
                            )}
                            {columnMappingTypes[column] === 'one-to-one' && (
                              <div className="flex items-center space-x-1 px-1 py-0.5 rounded text-xs bg-blue-100 text-blue-700">
                                <ArrowRight className="h-3 w-3" />
                                <span>1:1</span>
                              </div>
                            )}
                          </div>
                        </TableHead>
                      ))}
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sourceDataRows.map((sourceRow, index) => {
                      const globalRowIndex = (currentPage - 1) * pageSize + index;
                      const transformResult = transformationResults[globalRowIndex];
                      
                      return (
                        <TableRow 
                          key={globalRowIndex}
                          className={selectedRowIndex === index ? "bg-blue-50" : ""}
                        >
                          <TableCell className="font-medium">
                            {globalRowIndex + 1}
                          </TableCell>
                          {selectedTargetColumns.map((targetColumn) => {
                            const transformedValue = transformResult?.transformedData?.[targetColumn];
                            const sourceColumn = oneToOneMappings[targetColumn];
                            
                            // For one-to-one mapping, show source value; for AI transform, show transformed value
                            let displayValue;
                            if (columnMappingTypes[targetColumn] === 'one-to-one' && sourceColumn) {
                              displayValue = sourceRow[sourceColumn];
                            } else if (columnMappingTypes[targetColumn] === 'ai-transform') {
                              displayValue = transformedValue;
                            } else {
                              displayValue = transformedValue || sourceRow[targetColumn];
                            }
                            
                            return (
                              <TableCell key={targetColumn} className="max-w-[200px]">
                                <div className="truncate" title={String(displayValue || '')}>
                                  {displayValue !== null && displayValue !== undefined ? (
                                    <span className="text-sm">
                                      {typeof displayValue === 'object' ? 
                                        JSON.stringify(displayValue) : 
                                        String(displayValue)
                                      }
                                    </span>
                                  ) : (
                                    <span className="text-gray-400 italic text-sm">
                                      {columnMappingTypes[targetColumn] === 'ai-transform' ? 'Not transformed' : 'No value'}
                                    </span>
                                  )}
                                </div>
                              </TableCell>
                            );
                          })}
                          <TableCell>
                            <div className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
                              transformResult?.status === 'completed' ? 'bg-green-100 text-green-700' :
                              transformResult?.status === 'processing' ? 'bg-blue-100 text-blue-700' :
                              transformResult?.status === 'error' ? 'bg-red-100 text-red-700' :
                              'bg-gray-100 text-gray-700'
                            }`}>
                              {transformResult?.status === 'completed' && <CheckCircle className="h-3 w-3" />}
                              {transformResult?.status === 'processing' && <Loader2 className="h-3 w-3 animate-spin" />}
                              {transformResult?.status === 'error' && <AlertTriangle className="h-3 w-3" />}
                              {(!transformResult || transformResult?.status === 'pending') && <Clock className="h-3 w-3" />}
                              <span className="capitalize">
                                {transformResult?.status || 'pending'}
                              </span>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">No transformed data yet</p>
                <p className="text-sm">Run transformations to see results in this grid</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Export and Navigation */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="outline" onClick={() => exportData('excel')}>
                <FileSpreadsheet className="h-4 w-4 mr-1" />
                Export Excel
              </Button>
              <Button size="sm" variant="outline" onClick={() => exportData('csv')}>
                <FileText className="h-4 w-4 mr-1" />
                Export CSV
              </Button>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {Object.values(transformationResults).filter(r => r.status === 'completed').length} of {totalRows} rows transformed
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          Previous: AI Transform Mapping
        </Button>
        <Button onClick={handleComplete}>
          Complete Import Wizard
        </Button>
      </div>
      


      {/* Single Column Transform Dialog */}
      <SingleColumnTransformDialog
        isOpen={singleColumnTransformDialog.isOpen}
        onClose={closeSingleColumnTransform}
        jobId={jobId || ''}
        columnName={singleColumnTransformDialog.columnName}
        rowIndex={singleColumnTransformDialog.rowIndex}
        sourceRow={singleColumnTransformDialog.sourceRow}
        currentTransformValue={singleColumnTransformDialog.currentTransformValue}
        promptTemplate={singleColumnTransformDialog.promptTemplate}
        jobNotes={jobNotes}
        worksheetData={worksheetData}
        columnConfiguration={null}
        onTransformComplete={handleSingleColumnTransformComplete}
      />
    </div>
  );
} 