"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  <PERSON>ap, 
  Eye
} from "lucide-react";
import { useImportWizardStore } from "@/stores/import-wizard-store";
import { useQuery } from "@tanstack/react-query";
import { DirectDataGrid } from "@/components/data-table/enhanced-data-grid";
import { ModelSelector } from "@/components/ui/model-selector";

export function DataTransformationStep() {
  const [showDataPreview, setShowDataPreview] = useState(true);

  const { 
    jobId, 
    setCurrentStep,
    selectedLlmModel,
    setSelectedLlmModel
  } = useImportWizardStore();

  // Fetch job data for preview with larger page size
  const { data: jobData } = useQuery({
    queryKey: ['job-data', jobId, 1],
    queryFn: async () => {
      const response = await fetch(`/api/import/grid-data?job_id=${jobId}&page=1&page_size=100`);
      if (!response.ok) throw new Error('Failed to fetch job data');
      
      const responseText = await response.text();
      if (!responseText || responseText === 'undefined') {
        throw new Error('Empty or invalid response from data endpoint');
      }
      
      return JSON.parse(responseText);
    },
    enabled: !!jobId && showDataPreview,
  });

  return (
    <div className="space-y-6">
      {/* Model Selection */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ModelSelector
          selectedModel={selectedLlmModel}
          onModelChange={setSelectedLlmModel}
          label="AI Model for Data Transformation"
          description="Select the model to use for batch data transformation"
          showPricing={true}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Data Preview</span>
          </CardTitle>
          <CardDescription>
            Review your data before proceeding to the transformation step
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDataPreview(!showDataPreview)}
            >
              <Eye className="h-4 w-4 mr-2" />
              {showDataPreview ? 'Hide' : 'Show'} Preview
            </Button>
            
            <Button 
              onClick={() => setCurrentStep(5)}
              className="bg-green-600 hover:bg-green-700"
            >
              Proceed to Data Transformation
            </Button>
          </div>

          {/* Data Summary */}
          {jobData && jobData.success && jobData.data && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {jobData.data.total_rows?.toLocaleString() || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Total Rows</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {jobData.data.rows?.length || 0}
                </div>
                <div className="text-sm text-gray-600">Rows Loaded</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {jobData.data.total_pages || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Total Pages</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {showDataPreview && jobId && (
        <Card>
          <CardHeader>
            <CardTitle>Data Preview</CardTitle>
            <CardDescription>
              First 100 rows of your data. Use the transformation step to process all rows.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {jobData ? (
              <DirectDataGrid
                jobId={jobId}
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Loading data preview...</p>
                <p className="text-sm mt-2">If data doesn't load, please ensure your job has been processed successfully.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
} 