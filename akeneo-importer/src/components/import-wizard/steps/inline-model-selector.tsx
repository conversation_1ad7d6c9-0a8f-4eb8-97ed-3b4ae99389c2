"use client";

import { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface InlineModelSelectorProps {
  selectedModel: string | null;
  onModelChange: (modelId: string | null) => void;
  disabled?: boolean;
}

export function InlineModelSelector({ selectedModel, onModelChange, disabled = false }: InlineModelSelectorProps) {
  const [models, setModels] = useState<any[]>([]);
  const [enabledModels, setEnabledModels] = useState<string[]>([]);
  const [defaultModel, setDefaultModel] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [currentProvider, setCurrentProvider] = useState<'openrouter' | 'groq'>('groq');
  
  const modelsLoadedRef = useRef<boolean>(false);
  const isLoadingRef = useRef<boolean>(false);

  useEffect(() => {
    if (!modelsLoadedRef.current && !isLoadingRef.current) {
      loadModels();
    }
  }, []);

  const loadModels = async () => {
    if (isLoadingRef.current || modelsLoadedRef.current) {
      return;
    }
    isLoadingRef.current = true;
    setLoading(true);

    try {
      let provider = currentProvider;
      const providerResponse = await fetch('/api/models/provider');
      if (providerResponse.ok) {
        const providerData = await providerResponse.json();
        if (providerData.success) {
          provider = providerData.provider;
          setCurrentProvider(provider);
        }
      }

      const enabledResponse = await fetch(`/api/models/enabled?provider=${provider}`);
      if (enabledResponse.ok) {
        const enabledData = await enabledResponse.json();
        if (enabledData.success) {
          const enabledModelIds = enabledData.enabled_models.map((m: any) => m.id);
          setEnabledModels(enabledModelIds);
          setDefaultModel(enabledData.default_model);
          if (!selectedModel && enabledData.default_model) {
            onModelChange(enabledData.default_model);
          }
        }
      }

      const availableResponse = await fetch(`/api/models/available?provider=${provider}`);
      if (availableResponse.ok) {
        const availableData = await availableResponse.json();
        if (availableData.success) {
          setModels(availableData.models);
          modelsLoadedRef.current = true;
        }
      }
    } catch (error) {
      console.error('Error loading models:', error);
      toast.error('Failed to load available models');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  };

  const availableModels = models.filter(model => enabledModels.includes(model.id));

  if (availableModels.length === 0 && !loading) {
    return (
      <div className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
        No enabled models available
      </div>
    );
  }

  return (
    <Select
      value={selectedModel || ""}
      onValueChange={(value) => onModelChange(value || null)}
      disabled={disabled || loading}
    >
      <SelectTrigger className="h-9 text-sm">
        <SelectValue placeholder={loading ? "Loading models..." : "Select AI model"} />
      </SelectTrigger>
      <SelectContent>
        {availableModels.map((model) => {
          const contextLength = model.context_length || model.context_window;
          const isDefault = model.id === defaultModel;
          
          return (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex items-center gap-2 w-full">
                <div className="flex flex-col">
                  <span className="font-medium">{model.name}</span>
                  <div className="text-xs text-gray-500 flex items-center gap-2">
                    {contextLength && <span>Context: {contextLength.toLocaleString()} tokens</span>}
                    {isDefault && <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">Default</span>}
                  </div>
                </div>
              </div>
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
} 