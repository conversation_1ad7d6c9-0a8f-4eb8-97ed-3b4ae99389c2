"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { FileSpreadsheet, Eye, BarChart3, AlertCircle } from "lucide-react";

interface SheetPreview {
  headers: string[];
  previewRows: any[][];
  totalRows: number;
  totalColumns: number;
  error?: string;
}

interface SheetSelectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filename: string;
  sheetNames: string[];
  sheetsPreview: Record<string, SheetPreview>;
  selectedSheet: string | null;
  onSheetSelect: (sheetName: string) => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function SheetSelectionDialog({
  open,
  onOpenChange,
  filename,
  sheetNames,
  sheetsPreview,
  selectedSheet,
  onSheetSelect,
  onConfirm,
  isLoading = false
}: SheetSelectionDialogProps) {
  const [previewSheet, setPreviewSheet] = useState<string | null>(null);

  const handleSheetClick = (sheetName: string) => {
    onSheetSelect(sheetName);
    setPreviewSheet(sheetName);
  };

  const currentPreview = previewSheet ? sheetsPreview[previewSheet] : null;
  const isWideTable = currentPreview && currentPreview.totalColumns > 10;
  const isMassiveTable = currentPreview && currentPreview.totalColumns > 25;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[95vw] max-h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <FileSpreadsheet className="h-5 w-5" />
            <span>Select Excel Worksheet</span>
          </DialogTitle>
          <DialogDescription>
            Choose which worksheet to import from <strong>{filename}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4">
          {/* Sheet Selection */}
          <div>
            <h4 className="text-sm font-medium mb-3">Available Worksheets ({sheetNames.length})</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3">
              {sheetNames.map((sheetName) => {
                const preview = sheetsPreview[sheetName];
                const isSelected = selectedSheet === sheetName;
                const isPreviewing = previewSheet === sheetName;

                return (
                  <Card 
                    key={sheetName}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    } ${isPreviewing ? 'ring-1 ring-gray-300' : ''}`}
                    onClick={() => handleSheetClick(sheetName)}
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center justify-between">
                        <span className="truncate" title={sheetName}>{sheetName}</span>
                        {isSelected && <Badge variant="default" className="ml-2 text-xs">Selected</Badge>}
                      </CardTitle>
                      <CardDescription className="text-xs">
                        {preview?.error ? (
                          <span className="text-red-500 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {preview.error}
                          </span>
                        ) : (
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center">
                              <BarChart3 className="h-3 w-3 mr-1" />
                              {preview?.totalRows?.toLocaleString() || 0} rows
                            </span>
                            <span className="flex items-center">
                              {preview?.totalColumns || 0} cols
                              {preview && preview.totalColumns > 25 && (
                                <AlertCircle className="h-3 w-3 ml-1 text-orange-500" />
                              )}
                            </span>
                          </div>
                        )}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          setPreviewSheet(previewSheet === sheetName ? null : sheetName);
                        }}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        {isPreviewing ? 'Hide Preview' : 'Preview Data'}
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Data Preview */}
          {currentPreview && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">
                  Data Preview - {previewSheet}
                </h4>
                {isWideTable && (
                  <div className="flex items-center space-x-2">
                    {isMassiveTable && (
                      <Badge variant="destructive" className="text-xs">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Very Wide Table
                      </Badge>
                    )}
                    <Badge variant="secondary" className="text-xs">
                      {currentPreview.totalColumns} columns
                    </Badge>
                  </div>
                )}
              </div>
              
              <Card>
                <CardContent className="p-0">
                  {currentPreview.error ? (
                    <div className="text-center py-8 text-red-500 p-4">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                      <p>{currentPreview.error}</p>
                    </div>
                  ) : currentPreview.headers.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 p-4">
                      <p>No data found in this worksheet</p>
                    </div>
                  ) : (
                    <div className="space-y-3 p-4">
                      <div className="flex items-center justify-between text-xs text-gray-600">
                        <span>
                          Showing first 3 rows of {currentPreview.totalRows.toLocaleString()} total rows
                        </span>
                        {isWideTable && (
                          <span className="text-orange-600">
                            💡 Tip: Use keyboard arrows to navigate the preview
                          </span>
                        )}
                      </div>
                      
                      {/* Table Container with Fixed Height and Scrolling */}
                      <div className="border rounded-lg overflow-hidden">
                        <div 
                          className="overflow-auto"
                          style={{ 
                            maxHeight: isMassiveTable ? '300px' : '400px',
                            maxWidth: '100%'
                          }}
                        >
                          <Table>
                            <TableHeader className="sticky top-0 z-10">
                              <TableRow>
                                {currentPreview.headers.map((header, index) => (
                                  <TableHead 
                                    key={index} 
                                    className="text-xs font-semibold bg-gray-50 border-r border-gray-200 last:border-r-0"
                                    style={{ 
                                      minWidth: isWideTable ? '120px' : '100px',
                                      maxWidth: isMassiveTable ? '150px' : '200px'
                                    }}
                                  >
                                    <div className="truncate" title={String(header) || `Column ${index + 1}`}>
                                      {String(header) || `Column ${index + 1}`}
                                    </div>
                                  </TableHead>
                                ))}
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {currentPreview.previewRows.map((row, rowIndex) => (
                                <TableRow key={rowIndex} className="hover:bg-gray-50">
                                  {currentPreview.headers.map((_, colIndex) => (
                                    <TableCell 
                                      key={colIndex} 
                                      className="text-xs border-r border-gray-100 last:border-r-0"
                                      style={{ 
                                        minWidth: isWideTable ? '120px' : '100px',
                                        maxWidth: isMassiveTable ? '150px' : '200px'
                                      }}
                                    >
                                      <div 
                                        className="truncate" 
                                        title={String(row[colIndex] || '')}
                                      >
                                        {String(row[colIndex] || '')}
                                      </div>
                                    </TableCell>
                                  ))}
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                      
                      {/* Table Info Footer */}
                      <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                        <span>
                          {currentPreview.totalRows > 3 && 
                            `... and ${(currentPreview.totalRows - 3).toLocaleString()} more rows`
                          }
                        </span>
                        {isWideTable && (
                          <span className="text-orange-600">
                            💡 Tip: Use keyboard arrows to navigate the preview
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={onConfirm}
            disabled={!selectedSheet || isLoading}
          >
            {isLoading ? 'Processing...' : `Import ${selectedSheet || 'Selected Sheet'}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 