"use client";

import { useState, useEffect, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { 
  Zap, 
  Loader2, 
  Edit3,
  Database,
  Play,
  Wand2,
  <PERSON><PERSON><PERSON>riangle,
  ChevronLeft,
  ChevronRight,
  Table as TableIcon,
  Brain,
  Save,
  CheckCircle,
  Circle,
  ArrowLeft,
  ArrowRight,
  Trash2
} from "lucide-react";
import { useImportWizardStore, type MappingType } from "@/stores/import-wizard-store";
import { ModelSelector } from "@/components/ui/model-selector";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useLLMDefaults } from "@/hooks/use-llm-defaults";

interface ColumnConfiguration {
  column_name: string;
  type: string;
  prompt: string | null;
  required: boolean;
  validation_data?: string[] | null;
  output_validation_column?: string | null;
  default_mapping?: string | null;
  default_mapping_content?: string | null;
  description?: string | null;
}

interface WorksheetData {
  [column: string]: unknown;
}

interface ConfiguredColumn {
  columnName: string;
  promptTemplate: string;
  selectedModel: string | null; // Add model selection
  isConfigured: boolean;
  isSaved: boolean;
}

interface TestResult {
  isLoading: boolean;
  result: string;
  reasoning: string;
  error: string | null;
  renderedPrompt: string;
}

export function AiTransformMappingStep() {
  // Column tracking state - restored to local state but with persistence
  const [currentColumnIndex, setCurrentColumnIndex] = useState<number>(0);
  const [configuredColumns, setConfiguredColumns] = useState<Record<string, ConfiguredColumn>>({});
  
  // Add a state refresh trigger to force re-render after saving
  const [stateRefreshTrigger, setStateRefreshTrigger] = useState<number>(0);
  
  // Explicit button state to override React's reconciliation issues
  const [nextButtonEnabled, setNextButtonEnabled] = useState<boolean>(false);
  
  // Add state for current column's model selection
  const [currentColumnModel, setCurrentColumnModel] = useState<string | null>(null);
  
  // Use hook for LLM defaults instead of hardcoded values
  const { defaults: llmDefaults } = useLLMDefaults();
  const [temperature, setTemperature] = useState<number>(llmDefaults.temperature);
  
  // Update temperature when defaults load
  useEffect(() => {
    if (!llmDefaults.loading && llmDefaults.temperature) {
      setTemperature(llmDefaults.temperature);
    }
  }, [llmDefaults.loading, llmDefaults.temperature]);
  
  // Persist current column index to store for page reload preservation
  useEffect(() => {
    const store = useImportWizardStore.getState();
    if (store.currentAiTransformColumnIndex !== currentColumnIndex) {
      store.setCurrentAiTransformColumnIndex(currentColumnIndex);
    }
  }, [currentColumnIndex]);
  

  const [isSavingTemplate, setIsSavingTemplate] = useState<boolean>(false);
  
  // Existing state
  const [currentRowIndex, setCurrentRowIndex] = useState<number>(0);
  const [columnConfiguration, setColumnConfiguration] = useState<ColumnConfiguration | null>(null);
  const [worksheetData, setWorksheetData] = useState<WorksheetData[]>([]);
  const [currentPromptTemplate, setCurrentPromptTemplate] = useState<string>('');
  const [renderedPrompt, setRenderedPrompt] = useState<string>('');
  const [isPromptRendered, setIsPromptRendered] = useState<boolean>(false);
  const [isTestingLLM, setIsTestingLLM] = useState<boolean>(false);
  const [llmResponse, setLlmResponse] = useState<{ 
    raw: string; 
    extracted: string;
    fromCache?: boolean;
    cacheBypassed?: boolean;
    cacheStats?: any;
    // Structured output fields
    structured_output?: boolean;
    reasoning?: string;
    answer?: string;
    provider?: 'openrouter' | 'groq';
  } | null>(null);
  const [showRawResponse, setShowRawResponse] = useState<boolean>(false);
  const [llmRawContent, setLlmRawContent] = useState<string>('');
  const [showLlmRawContent, setShowLlmRawContent] = useState<boolean>(false);
  
  // Structured output controls
  const [useStructuredOutput, setUseStructuredOutput] = useState<boolean>(true);
  const [showReasoning, setShowReasoning] = useState<boolean>(false);
  
  // System prompt state
  const [systemPrompt, setSystemPrompt] = useState<string>('');
  const [loadingSystemPrompt, setLoadingSystemPrompt] = useState<boolean>(false);
  const [showSystemPrompt, setShowSystemPrompt] = useState<boolean>(false);
  
  // New state for additional data loading
  const [availableWorksheets, setAvailableWorksheets] = useState<string[]>([]);
  const [additionalDataError, setAdditionalDataError] = useState<string>('');
  
  // Job Notes state
  const [jobNotes, setJobNotes] = useState<string>('');
  const [showNotesModal, setShowNotesModal] = useState<boolean>(false);
  const [loadingNotes, setLoadingNotes] = useState<boolean>(false);
  const [savingNotes, setSavingNotes] = useState<boolean>(false);
  
  // Loading states
  const [loadingConfiguration, setLoadingConfiguration] = useState<boolean>(false);
  const [loadingSourceData, setLoadingSourceData] = useState<boolean>(false);
  const [loadingWorksheet, setLoadingWorksheet] = useState<boolean>(false);
  const [loadingAdditionalData, setLoadingAdditionalData] = useState<boolean>(false);
  
  // Errors
  const [sourceDataError, setSourceDataError] = useState<string>('');

  const [testResult, setTestResult] = useState<TestResult>({
    isLoading: false,
    result: '',
    reasoning: '',
    error: null,
    renderedPrompt: ''
  });

  const { 
    selectedTargetColumns,
    oneToOneMappings,
    columnMappingTypes,
    setAiTransformMappings,
    setCurrentStep,
    jobId,
    markStepCompleted,
    setCurrentAiTransformColumnIndex,
    selectedLlmModel,
    setSelectedLlmModel
  } = useImportWizardStore();

  // Get columns that are set for AI transform mapping
  const aiTransformColumns = useMemo(() => {
    return selectedTargetColumns.filter(col => 
      columnMappingTypes[col] === 'ai-transform'
    );
  }, [selectedTargetColumns, columnMappingTypes]);

  // Current column being configured
  const currentColumn = aiTransformColumns[currentColumnIndex];

  // Source data rows for current job
  const [sourceRows, setSourceRows] = useState<Record<string, unknown>[]>([]);

  // Progress calculations (including stateRefreshTrigger to force recalculation)
  const totalColumns = aiTransformColumns.length;
  const configuredCount = useMemo(() => {
    return Object.values(configuredColumns).filter(col => col.isConfigured && col.isSaved).length;
  }, [configuredColumns, stateRefreshTrigger]);
  
  const progressPercentage = totalColumns > 0 ? (configuredCount / totalColumns) * 100 : 0;
  const allColumnsConfigured = useMemo(() => {
    return configuredCount === totalColumns && totalColumns > 0;
  }, [configuredCount, totalColumns, stateRefreshTrigger]);
  
  // Debug logging for state tracking
  console.log('🔍 AI Transform Step - Progress Check:', { 
    totalColumns, 
    configuredCount, 
    allColumnsConfigured,
    configuredColumns: Object.keys(configuredColumns).length,
    stateRefreshTrigger,
    columnsStatus: Object.entries(configuredColumns).map(([name, col]) => ({
      name,
      isConfigured: col.isConfigured,
      isSaved: col.isSaved
    }))
  });

  // Restore current column index from store on mount (after aiTransformColumns is defined)
  useEffect(() => {
    const store = useImportWizardStore.getState();
    if (store.currentAiTransformColumnIndex > 0 && store.currentAiTransformColumnIndex < aiTransformColumns.length) {
      setCurrentColumnIndex(store.currentAiTransformColumnIndex);
    }
  }, [aiTransformColumns.length]);

  // Sync current column's model selection when switching columns
  useEffect(() => {
    if (currentColumn && configuredColumns[currentColumn]) {
      setCurrentColumnModel(configuredColumns[currentColumn].selectedModel || selectedLlmModel);
    } else {
      setCurrentColumnModel(selectedLlmModel);
    }
  }, [currentColumn, configuredColumns, selectedLlmModel]);

  // Update button enabled state when configuration changes
  useEffect(() => {
    const shouldEnable = allColumnsConfigured;
    console.log('🔘 Updating button state:', { shouldEnable, allColumnsConfigured, configuredCount, totalColumns });
    setNextButtonEnabled(shouldEnable);
  }, [allColumnsConfigured, configuredCount, totalColumns, stateRefreshTrigger]);

  // Save configuredColumns state to Redis whenever it changes
  useEffect(() => {
    const saveStateToRedis = async () => {
      if (!jobId) return;
      
      // Only save if we have some configured columns (avoid saving during initialization)
      const hasConfiguredColumns = Object.values(configuredColumns).some(col => col.isConfigured || col.isSaved);
      if (!hasConfiguredColumns && Object.keys(configuredColumns).length > 0) {
        console.log('⏳ Skipping save - no configured columns yet');
        return;
      }
      
      try {
        console.log('💾 Saving AI transform state to Redis:', Object.keys(configuredColumns).length, 'columns');
        const response = await fetch('/api/import/ai-transform-state', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            job_id: jobId,
            column_states: configuredColumns
          }),
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log('✅ AI transform state saved to Redis:', result.data);
          }
        } else {
          console.error('❌ Failed to save AI transform state - HTTP', response.status);
        }
      } catch (error) {
        console.warn('Failed to save AI transform state to Redis:', error);
      }
    };

    // Debounce the save operation to avoid too many requests
    const timeoutId = setTimeout(saveStateToRedis, 500);
    return () => clearTimeout(timeoutId);
  }, [configuredColumns, jobId]);

     // Initialize configured columns tracking - start fresh for user workflow
   useEffect(() => {
     const initialConfigured: Record<string, ConfiguredColumn> = {};
     
     // Try to restore from Redis first, then initialize with defaults
     const loadSavedState = async () => {
       try {
         if (jobId) {
           console.log('📖 Loading AI transform state from Redis for job:', jobId);
           const response = await fetch(`/api/import/ai-transform-state?job_id=${jobId}`);
           console.log('📖 Response status:', response.status);
           if (response.ok) {
             const result = await response.json();
             console.log('📖 Response data:', result);
             if (result.success && result.data.columnStates && Object.keys(result.data.columnStates).length > 0) {
               console.log('✅ AI Transform: Restored state from Redis:', result.data.configuredColumns, '/', result.data.totalColumns, 'columns');
               setConfiguredColumns(result.data.columnStates);
               return;
             } else {
               console.log('⚠️ No saved state found or empty state');
             }
           } else {
             console.log('❌ Failed to load state - HTTP', response.status);
           }
         }
       } catch (error) {
         console.warn('Failed to load saved AI transform state:', error);
       }
       
       // Fallback: Initialize with defaults if no saved state
       aiTransformColumns.forEach(col => {
         initialConfigured[col] = {
           columnName: col,
           promptTemplate: '',
           selectedModel: null,
           isConfigured: false,
           isSaved: false
         };
       });
       
       setConfiguredColumns(initialConfigured);
       console.log('AI Transform: Initialized', aiTransformColumns.length, 'columns for configuration');
     };
     
     loadSavedState();
   }, [aiTransformColumns, jobId]);

  // Load current column's saved template when column changes (but avoid infinite loop)
  useEffect(() => {
    if (currentColumn && configuredColumns[currentColumn]) {
      const savedTemplate = configuredColumns[currentColumn].promptTemplate;
      // Only update if the template is different from what's currently shown
      if (savedTemplate !== currentPromptTemplate) {
        setCurrentPromptTemplate(savedTemplate);
      }
      setIsPromptRendered(false);
      setLlmResponse(null);
    }
  }, [currentColumn]); // Remove configuredColumns from dependency to avoid loop

  // Automatically load additional data when column changes and worksheets are available
  useEffect(() => {
    if (currentColumn && availableWorksheets.length > 0) {
      console.log(`🔍 Checking for additional data for column: ${currentColumn}`);
      loadWorksheetData(currentColumn);
    }
  }, [currentColumn, availableWorksheets]);

  // Load job notes when job ID changes
  useEffect(() => {
    const loadJobNotes = async () => {
      if (!jobId) return;
      
      setLoadingNotes(true);
      try {
        const response = await fetch(`/api/jobs/${jobId}/notes`);
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setJobNotes(result.notes || '');
          }
        }
      } catch (error) {
        console.warn('Could not load job notes:', error);
      } finally {
        setLoadingNotes(false);
      }
    };
    
    loadJobNotes();
  }, [jobId]);

  // Load source data when job ID changes
  useEffect(() => {
    const loadSourceData = async () => {
      if (!jobId) {
        setSourceDataError('No job ID available');
        return;
      }
      
      console.log('🔍 AI Transform Mapping - Loading source data for job ID:', jobId);
      setLoadingSourceData(true);
      setSourceDataError('');
      
      try {
        const url = `/api/import/grid-data?job_id=${jobId}&page=1&page_size=10`;
        console.log('🔍 AI Transform Mapping - Fetching from URL:', url);
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('🔍 AI Transform Mapping - API Response:', result);
        
        if (!result.success) {
          if (result.error === 'Source Data Error: No source data available in job') {
            setSourceDataError(`No source data found for this job. ${result.details || ''}`);
            toast.error('No Source Data Available', {
              description: 'Please return to step 1 and re-upload your file.',
            });
          } else if (result.error === 'Source Data Error: No column information available') {
            setSourceDataError(`Column information missing. ${result.details || ''}`);
            toast.error('Column Information Missing', {
              description: 'Please return to step 1 and re-upload your file.',
            });
          } else {
            setSourceDataError(result.error || 'API returned success: false');
            toast.error('Data Loading Error', {
              description: result.error || 'Unknown error occurred'
            });
          }
          return;
        }
        
        if (!result.data || !result.data.rows || result.data.rows.length === 0) {
          console.warn('🔍 AI Transform Mapping - Empty data response from API');
          setSourceDataError('No data rows available in the response');
          toast.error('Empty Data Response', {
            description: 'The data source appears to be empty. Please re-upload your file.'
          });
          return;
        }

        console.log('🔍 AI Transform Mapping - Setting source rows:', result.data.rows.length, 'rows');
        setSourceRows(result.data.rows);
        
        // Also store in the wizard store for consistency
        useImportWizardStore.getState().setSourceData(result.data.rows);
        
        toast.success('Source data loaded successfully', {
          description: `${result.data.rows.length} rows loaded for transformation`
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setSourceDataError(`Failed to load source data: ${errorMessage}`);
        console.error('🔍 AI Transform Mapping - Error:', error);
        console.log('🔍 AI Transform Mapping - Job ID was:', jobId);
        toast.error('Failed to load source data', {
          description: 'Please check your connection and try again, or return to step 1 to re-upload.'
        });
      } finally {
        setLoadingSourceData(false);
      }
    };

    loadSourceData();
  }, [jobId]);

  // Load configuration when current column changes
  useEffect(() => {
    const loadColumnConfiguration = async () => {
      if (!currentColumn) return;
      
      setLoadingConfiguration(true);
      
      try {
        const response = await fetch('/api/unified-config');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to load configuration');
        }
        
        const columnConfig = result.data.columns.find((col: { column_name: string }) => col.column_name === currentColumn);
        
        if (columnConfig) {
          setColumnConfiguration({
            column_name: columnConfig.column_name,
            type: columnConfig.type,
            prompt: columnConfig.prompt,
            required: columnConfig.required,
            validation_data: columnConfig.validation_data,
            output_validation_column: columnConfig.output_validation_column,
            default_mapping: columnConfig.default_mapping,
            default_mapping_content: columnConfig.default_mapping_content,
            description: columnConfig.description,
          });
          
          // Debug logging to see what we loaded
          console.log('📋 Column configuration loaded for:', currentColumn, {
            description: columnConfig.description,
            prompt: columnConfig.prompt,
            type: columnConfig.type,
            output_validation_column: columnConfig.output_validation_column,
            default_mapping_content: columnConfig.default_mapping_content,
            has_custom_template: !!columnConfig.default_mapping_content?.trim()
          });
          
          // Set initial prompt template if not already set
          if (!configuredColumns[currentColumn]?.promptTemplate) {
            console.log('Loading template for column:', currentColumn);
            try {
              // First check if column has custom mapping prompt template
              console.log('🔍 Checking for custom template:', {
                columnName: currentColumn,
                hasDefaultMappingContent: !!columnConfig.default_mapping_content,
                defaultMappingContentLength: columnConfig.default_mapping_content?.length || 0,
                trimmedLength: columnConfig.default_mapping_content?.trim().length || 0
              });
              
              if (columnConfig.default_mapping_content?.trim()) {
                console.log('✅ Using custom mapping prompt template for column:', currentColumn);
                console.log('🎯 Custom template content preview:', columnConfig.default_mapping_content.substring(0, 100) + '...');
                setCurrentPromptTemplate(columnConfig.default_mapping_content);
                console.log('✅ Custom template set successfully');
              } else {
                // Fall back to global template
                console.log('❌ No custom template found, loading global template for column:', currentColumn);
                const globalTemplate = await getDefaultPrompt();
                console.log('Global template loaded:', globalTemplate ? 'Success' : 'Failed');
                
                if (globalTemplate) {
                  // Use global template as-is, keeping all placeholders intact
                  // Don't replace @prompt or any other placeholders here
                  console.log('Using global template with placeholders intact');
                  setCurrentPromptTemplate(globalTemplate);
                  console.log('Template set successfully');
                } else {
                  console.error('No global template found');
                  toast.error('Failed to load global prompt template');
                }
              }
            } catch (error) {
              console.error('Error loading template:', error);
              toast.error('Error loading prompt template');
            }
          }
        }
        
      } catch (error) {
        console.error('Failed to load column configuration:', error);
        toast.error('Failed to load column configuration');
      } finally {
        setLoadingConfiguration(false);
      }
    };

    loadColumnConfiguration();
  }, [currentColumn]);

  // Save template to database
  const saveTemplateToDatabase = async (goToNext: boolean = false) => {
    if (!currentColumn || !currentPromptTemplate.trim()) {
      toast.error('Please enter a prompt template before saving');
      return;
    }

    setIsSavingTemplate(true);
    
    try {
      // API call to save template to database
      const response = await fetch('/api/unified-config/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          column_name: currentColumn,
          prompt_template: currentPromptTemplate,
          selected_model: currentColumnModel,
          job_id: jobId
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save template');
      }

      // Update configured columns state
      const newConfiguredColumns = {
        ...configuredColumns,
        [currentColumn]: {
          ...configuredColumns[currentColumn],
          promptTemplate: currentPromptTemplate,
          selectedModel: currentColumnModel,
          isConfigured: true,
          isSaved: true
        }
      };
      setConfiguredColumns(newConfiguredColumns);

      // Immediately save state to Redis
      try {
        console.log('💾 Saving individual template state to Redis for column:', currentColumn);
        const stateResponse = await fetch('/api/import/ai-transform-state', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            job_id: jobId,
            column_states: newConfiguredColumns
          }),
        });
        
        if (stateResponse.ok) {
          const result = await stateResponse.json();
          console.log('✅ AI transform state saved to Redis after template save:', result.data);
        } else {
          console.error('❌ Failed to save state - HTTP', stateResponse.status);
        }
      } catch (error) {
        console.warn('Failed to save state to Redis:', error);
      }

      toast.success(`Template saved for column: ${currentColumn}`);
      
      // Go to next column if requested and available
      if (goToNext && currentColumnIndex < aiTransformColumns.length - 1) {
        setCurrentColumnIndex(currentColumnIndex + 1);
      }
      
    } catch (error) {
      console.error('Failed to save template:', error);
      toast.error('Failed to save template to database');
    } finally {
      setIsSavingTemplate(false);
    }
  };

  // Save all templates to database
  const saveAllTemplates = async () => {
    setIsSavingTemplate(true);
    let savedCount = 0;
    let errorCount = 0;

    try {
      // Load unified config to get column configurations for all columns
      const configResponse = await fetch('/api/unified-config');
      if (!configResponse.ok) {
        throw new Error('Failed to load unified configuration');
      }
      const configResult = await configResponse.json();
      if (!configResult.success) {
        throw new Error(configResult.error || 'Failed to load configuration');
      }

      // Load default global template once
      const globalTemplate = await getDefaultPrompt();
      if (!globalTemplate) {
        throw new Error('Failed to load default global template');
      }

      for (const column of aiTransformColumns) {
        const columnConfig = configuredColumns[column];
        // Find the column configuration from unified config
        const unifiedColumnConfig = configResult.data.columns.find((col: { column_name: string }) => col.column_name === column);
        let templateToSave: string;
        
        // For current column, use what user is typing; for others, use saved template or check unified config
        if (column === currentColumn) {
          templateToSave = currentPromptTemplate;
        } else if (columnConfig?.promptTemplate?.trim()) {
          templateToSave = columnConfig.promptTemplate;
        } else if (unifiedColumnConfig?.default_mapping_content?.trim()) {
          // Use custom template from unified config
          console.log(`Using custom mapping prompt template for ${column} from unified config`);
          templateToSave = unifiedColumnConfig.default_mapping_content;
        } else {
          // Use global template as-is, keeping placeholders intact
          // Don't replace @prompt or any other placeholders here
          templateToSave = globalTemplate;
        }

        // Skip if still no template (shouldn't happen with global template)
        if (!templateToSave?.trim()) {
          console.warn(`No template available for ${column}`);
          errorCount++;
          continue;
        }

        try {
          const response = await fetch('/api/unified-config/prompts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              column_name: column,
              prompt_template: templateToSave,
              selected_model: column === currentColumn ? currentColumnModel : (configuredColumns[column]?.selectedModel || selectedLlmModel),
              job_id: jobId
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const result = await response.json();
          
          if (!result.success) {
            throw new Error(result.error || 'Failed to save template');
          }

          savedCount++;
        } catch (error) {
          console.error(`Failed to save template for ${column}:`, error);
          errorCount++;
        }
      }

      // Update all configured columns state
      const updatedColumns = { ...configuredColumns };
      aiTransformColumns.forEach(column => {
        // Find the column configuration from unified config
        const unifiedColumnConfig = configResult.data.columns.find((col: { column_name: string }) => col.column_name === column);
        let finalTemplate: string;
        
        if (column === currentColumn) {
          finalTemplate = currentPromptTemplate;
        } else if (updatedColumns[column]?.promptTemplate?.trim()) {
          finalTemplate = updatedColumns[column].promptTemplate;
        } else if (unifiedColumnConfig?.default_mapping_content?.trim()) {
          // Use custom template from unified config
          finalTemplate = unifiedColumnConfig.default_mapping_content;
        } else {
          // Use global template as-is, keeping placeholders intact
          // Don't replace @prompt or any other placeholders here
          finalTemplate = globalTemplate;
        }
        
        // Always mark as configured and saved when saving all
        updatedColumns[column] = {
          columnName: column,
          promptTemplate: finalTemplate || '',
          selectedModel: updatedColumns[column]?.selectedModel || selectedLlmModel || null,
          isConfigured: true,
          isSaved: true
        };
      });
      
      // Update the state immediately
      setConfiguredColumns(updatedColumns);
      
      // Force a small delay to ensure state update is processed
      await new Promise(resolve => setTimeout(resolve, 100));

      // Save updated state to Redis
      try {
        const stateResponse = await fetch('/api/import/ai-transform-state', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            job_id: jobId,
            column_states: updatedColumns
          }),
        });
        
        if (stateResponse.ok) {
          console.log('✅ AI transform state saved to Redis after saving all templates');
        }
      } catch (error) {
        console.warn('Failed to save state to Redis:', error);
      }

      if (errorCount > 0) {
        toast.warning(`Saved ${savedCount} templates, ${errorCount} failed`);
      } else {
        toast.success(`All ${savedCount} templates saved successfully`);
        // Mark step as completed when all templates are saved
        markStepCompleted(4);
      }

      console.log('🚀 Save All completed - updatedColumns:', updatedColumns);
      console.log('🔍 All columns configured check:', Object.values(updatedColumns).filter(col => col.isConfigured && col.isSaved).length, 'of', aiTransformColumns.length);
      
      // Trigger state refresh to force re-render and re-calculation
      setStateRefreshTrigger(prev => prev + 1);
      
    } catch (error) {
      console.error('Failed to save all templates:', error);
      toast.error('Failed to save all templates');
    } finally {
      setIsSavingTemplate(false);
    }
  };

  // Column navigation
  const goToPreviousColumn = () => {
    if (currentColumnIndex > 0) {
      setCurrentColumnIndex(currentColumnIndex - 1);
    }
  };

  const goToNextColumn = () => {
    if (currentColumnIndex < aiTransformColumns.length - 1) {
      setCurrentColumnIndex(currentColumnIndex + 1);
    }
  };

  // Check if current column is configured
  const isCurrentColumnConfigured = currentColumn && configuredColumns[currentColumn]?.isConfigured && configuredColumns[currentColumn]?.isSaved;

  // Clear all mappings back to default
  const clearAllMappings = async () => {
    try {
      // Reset all configured columns to default state
      const clearedColumns: Record<string, ConfiguredColumn> = {};
      aiTransformColumns.forEach(col => {
        clearedColumns[col] = {
          columnName: col,
          promptTemplate: '',
          selectedModel: null,
          isConfigured: false,
          isSaved: false
        };
      });
      
      setConfiguredColumns(clearedColumns);
      
      // Reset current template if viewing a column
      if (currentColumn) {
        setCurrentPromptTemplate('');
        setIsPromptRendered(false);
        setLlmResponse(null);
      }
      
      // Clear from Redis
      if (jobId) {
        await fetch('/api/import/ai-transform-state', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            job_id: jobId,
            column_states: clearedColumns
          }),
        });
      }
      
      toast.success('All column mappings cleared', {
        description: 'All columns have been reset to default configuration'
      });
      
      console.log('🧹 All AI transform mappings cleared');
    } catch (error) {
      console.error('Failed to clear mappings:', error);
      toast.error('Failed to clear mappings');
    }
  };

  // Load available worksheets on component mount
  useEffect(() => {
    const loadAvailableWorksheets = async () => {
      try {
        const response = await fetch('/api/google-sheets/worksheets');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setAvailableWorksheets(result.data || []);
            console.log('📋 Available worksheets:', result.data);
          }
        }
      } catch (error) {
        console.warn('Could not load available worksheets:', error);
      }
    };
    
    loadAvailableWorksheets();
  }, []);

  // Enhanced loadWorksheetData function that uses API endpoints
  const loadWorksheetData = async (columnName: string, worksheets?: Record<string, Record<string, unknown>[]>) => {
    setLoadingWorksheet(true);
    setAdditionalDataError('');
    
    try {
      // First, try to use provided worksheets object (for backward compatibility)
      if (worksheets && worksheets[columnName]) {
        setWorksheetData(worksheets[columnName]);
        console.log(`✅ Loaded worksheet data for ${columnName} from provided worksheets`);
        return;
      }
      
      // Check if there's a worksheet with the same name as the column
      if (availableWorksheets.includes(columnName)) {
        console.log(`🔍 Loading additional data for column ${columnName} from its own worksheet...`);
        
        const response = await fetch(`/api/google-sheets/worksheets?name=${encodeURIComponent(columnName)}&use_cache=false`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success && result.data) {
          setWorksheetData(result.data);
          console.log(`✅ Loaded ${result.data.length} rows of additional data for ${columnName}`);
          toast.success(`Loaded additional data for ${columnName}`, {
            description: `Found ${result.data.length} rows in the ${columnName} worksheet`
          });
        } else {
          throw new Error(result.message || 'No data returned');
        }
      } else {
        // No additional worksheet found for this column
        setWorksheetData([]);
        console.log(`ℹ️ No additional worksheet found for column ${columnName}`);
      }
    } catch (error) {
      console.error(`Failed to load worksheet data for ${columnName}:`, error);
      setAdditionalDataError(`Failed to load additional data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setWorksheetData([]);
      toast.error('Failed to load additional data', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setLoadingWorksheet(false);
    }
  };

  // Save job notes function
  const saveJobNotes = async () => {
    if (!jobId) return;
    
    setSavingNotes(true);
    try {
      const response = await fetch(`/api/jobs/${jobId}/notes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes: jobNotes }),
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          toast.success('Job notes saved successfully');
          setShowNotesModal(false);
        } else {
          throw new Error(result.message || 'Failed to save notes');
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to save job notes:', error);
      toast.error('Failed to save job notes', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setSavingNotes(false);
    }
  };

  // Helper function to format additional data as markdown table
  const formatAdditionalDataAsMarkdown = (data: WorksheetData[]): string => {
    if (!data || data.length === 0) {
      return 'No additional data available';
    }
    
    const columns = Object.keys(data[0]);
    if (columns.length === 0) {
      return 'No additional data available';
    }
    
    // Create markdown table
    let markdown = '| ' + columns.join(' | ') + ' |\n';
    markdown += '|' + columns.map(() => '---').join('|') + '|\n';
    
    // Add all data rows
    for (const row of data) {
      const values = columns.map(col => {
        const value = row[col];
        return value ? String(value).replace(/\|/g, '\\|') : ''; // Escape pipes in data
      });
      markdown += '| ' + values.join(' | ') + ' |\n';
    }
    
    return markdown;
  };

  const getDefaultPrompt = async (): Promise<string> => {
    try {
      console.log('Fetching default prompt from API...');
      const response = await fetch('/api/settings/prompt/default');
      console.log('API response status:', response.status);
      
      if (response.ok) {
        const responseText = await response.text();
        console.log('Raw response text:', responseText);
        
        if (!responseText || responseText === 'undefined') {
          console.error('API returned undefined or empty response');
          return 'Transformation instructions: @description\n\nValidation data for reference: @prompt_additional_data\nThe validation column is: @output_validation_field\n\nPlease use the information in the notes: @notes\nCurrent row data: @row\n\nPlease provide your response in the expected format. Be precise and follow any constraints provided.';
        }
        
        const result = JSON.parse(responseText);
        console.log('Parsed result:', result);
        
        if (result.success) {
          console.log('Successfully retrieved default prompt:', result.default_prompt?.substring(0, 100) + '...');
          return result.default_prompt || 'Transformation instructions: @description\n\nValidation data for reference: @prompt_additional_data\nThe validation column is: @output_validation_field\n\nPlease use the information in the notes: @notes\nCurrent row data: @row\n\nPlease provide your response in the expected format. Be precise and follow any constraints provided.';
        } else {
          console.error('API returned error:', result.message);
        }
      } else {
        console.error('API request failed:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Failed to fetch default prompt:', error);
    }
    return 'Transformation instructions: @description\n\nValidation data for reference: @prompt_additional_data\nThe validation column is: @output_validation_field\n\nPlease use the information in the notes: @notes\nCurrent row data: @row\n\nPlease provide your response in the expected format. Be precise and follow any constraints provided.';
  };

  // Load system prompt from API
  const loadSystemPrompt = async () => {
    setLoadingSystemPrompt(true);
    try {
      const response = await fetch('/api/settings/prompt/system');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.system_prompt) {
          setSystemPrompt(result.system_prompt);
        } else {
          setSystemPrompt('No system prompt configured');
        }
      } else {
        setSystemPrompt('Error loading system prompt');
      }
    } catch (error) {
      console.warn('Could not load system prompt:', error);
      setSystemPrompt('Error loading system prompt');
    } finally {
      setLoadingSystemPrompt(false);
    }
  };

  // Load system prompt on component mount
  useEffect(() => {
    loadSystemPrompt();
  }, []);

  // Use centralized prompt service instead of hardcoded templates

  const renderPromptWithPlaceholders = async () => {
    if (!currentPromptTemplate || sourceRows.length === 0) {
      toast.error('No prompt template or source data available');
      return;
    }

    const currentRow = sourceRows[currentRowIndex];
    if (!currentRow) {
      toast.error('No current row data available');
      return;
    }

    try {
      let renderedText = currentPromptTemplate;
      
      // Helper function to escape HTML and wrap replacement values with styled spans
      const escapeHtml = (text: string) => {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
      };
      
      const wrapPlaceholderValue = (value: string) => {
        const escapedValue = escapeHtml(value);
        return `<span style="color: #6b7280; font-style: italic;">${escapedValue}</span>`;
      };
      
      // Replace placeholders in order of specificity (longest first to avoid partial matches)
      
      // 1. Replace specific compound placeholders first
      const validationData = formatAdditionalDataAsMarkdown(worksheetData);
      console.log('🔧 Replacing @prompt_additional_data with:', validationData.substring(0, 100) + '...');
      renderedText = renderedText.replace(/@prompt_additional_data/g, wrapPlaceholderValue(validationData));
      
      const outputValidationFieldValue = columnConfiguration?.output_validation_column || 'No validation field specified';
      console.log('🔧 Replacing @output_validation_field with:', outputValidationFieldValue);
      renderedText = renderedText.replace(/@output_validation_field/g, wrapPlaceholderValue(outputValidationFieldValue));
      // Keep old placeholder for backward compatibility during transition
      renderedText = renderedText.replace(/@prompt_additional_data_column/g, wrapPlaceholderValue(outputValidationFieldValue));
      
      // 2. Replace other standard placeholders
      const notesValue = jobNotes || 'No job notes specified';
      console.log('🔧 Replacing @notes with:', notesValue);
      renderedText = renderedText.replace(/@notes/g, wrapPlaceholderValue(notesValue));
      
      const rowValue = JSON.stringify(currentRow, null, 2);
      console.log('🔧 Replacing @row with:', rowValue.substring(0, 100) + '...');
      renderedText = renderedText.replace(/@row/g, wrapPlaceholderValue(rowValue));
      
      // Get description from column configuration
      const descriptionValue = columnConfiguration?.description || columnConfiguration?.prompt || 'No description specified';
      console.log('🔧 Replacing @description with:', descriptionValue);
      renderedText = renderedText.replace(/@description/g, wrapPlaceholderValue(descriptionValue));
      // Keep @prompt for backward compatibility during transition
      renderedText = renderedText.replace(/@prompt/g, wrapPlaceholderValue(descriptionValue));
      
      const taskDescriptionValue = `Transform the source data to match the requirements for the "${currentColumn}" column`;
      console.log('🔧 Replacing @taskDescription with:', taskDescriptionValue);
      renderedText = renderedText.replace(/@taskDescription/g, wrapPlaceholderValue(taskDescriptionValue));
      
      // 3. Replace dynamic column placeholders
      console.log('🔧 Replacing dynamic column placeholders from row:', Object.keys(currentRow || {}));
      Object.entries(currentRow || {}).forEach(([column, value]) => {
        const placeholder = `@${column}`;
        const valueStr = String(value || '');
        if (renderedText.includes(placeholder)) {
          console.log(`🔧 Replacing ${placeholder} with:`, valueStr.substring(0, 50) + '...');
          renderedText = renderedText.replace(new RegExp(placeholder, 'g'), wrapPlaceholderValue(valueStr));
        }
      });

      // FINAL DEBUG: Show the result
      console.log('🎯 FINAL RENDERED PROMPT:', {
        length: renderedText.length,
        first200: renderedText.substring(0, 200),
        stillHasPlaceholders: /@\w+/.test(renderedText),
        remainingPlaceholders: renderedText.match(/@\w+/g)
      });

      setRenderedPrompt(renderedText);
      setIsPromptRendered(true);
      toast.success('Prompt rendered with current row data');
      
      // Debug logging
      console.log('Placeholder replacement debug:', {
        currentColumn,
        columnConfiguration,
        worksheetData: worksheetData.length,
        validationData: validationData.substring(0, 100) + '...',
        descriptionValue,
        outputValidationFieldValue
      });
      
    } catch (error) {
      console.error('Failed to render prompt:', error);
      toast.error('Failed to render prompt with placeholders');
    }
  };

  const togglePromptView = () => {
    if (!isPromptRendered) {
      renderPromptWithPlaceholders();
    } else {
      setIsPromptRendered(false);
    }
  };

  const sendPromptToLLM = async () => {
    if (!currentPromptTemplate || sourceRows.length === 0) {
      toast.error('No prompt template or source data available');
      return;
    }

    if (isTestingLLM) return;
    setIsTestingLLM(true);
    setLlmResponse(null);

    try {
      // Get the current row for context
      const currentRow = sourceRows[currentRowIndex];
      if (!currentRow) {
        toast.error('No source data available for the current row.');
        setIsTestingLLM(false);
        return;
      }
      
      console.log('▶️ Sending prompt to LLM for column:', currentColumn);
      console.log('💿 With model:', currentColumnModel || 'default');

      const requestBody: any = {
        job_id: jobId, // Can be null, backend will handle
        column_name: currentColumn,
        row_index: currentRowIndex,
        model_id: currentColumnModel || selectedLlmModel,
        bypass_cache: true, // Always bypass cache for interactive testing
        // NEW: Send row data if no job_id is present
        worksheet_data: !jobId ? [currentRow] : [],
        // Pass the full column config for context
        column_configuration: columnConfiguration
      };

      const response = await fetch('/api/import/ai-transform', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'LLM transformation failed');
      }

      setLlmResponse({
        raw: JSON.stringify(result, null, 2),
        extracted: result.result || result.transformed_value || 'No transformed value returned',
        fromCache: result.fromCache || false,
        cacheBypassed: result.cache_bypassed || false,
        cacheStats: result.cache_stats || null,
        // Structured output fields
        structured_output: result.structured_output || false,
        reasoning: result.reasoning || '',
        answer: result.answer || result.result || result.transformed_value || '',
        provider: result.provider || 'openrouter'
      });

      // Show appropriate toast based on cache status and structured output
      const structuredMsg = result.structured_output ? ' with structured output' : '';
      const providerMsg = result.provider ? ` (${result.provider})` : '';
      
      if (result.cache_bypassed) {
        toast.success(`LLM transformation completed (cache bypassed)${structuredMsg}${providerMsg}`);
      } else if (result.fromCache) {
        toast.success(`LLM transformation completed (from cache)${structuredMsg}${providerMsg}`);
      } else {
        toast.success(`LLM transformation completed (fresh API call)${structuredMsg}${providerMsg}`);
      }
      
    } catch (error) {
      console.error('Failed to send prompt to LLM:', error);
      toast.error(`LLM test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTestingLLM(false);
    }
  };

  // Navigation handlers
  const handleNext = () => {
    console.log('🚀 handleNext called - State check:', { allColumnsConfigured, configuredCount, totalColumns });
    
    if (!allColumnsConfigured) {
      toast.error('Please configure and save all AI transform columns before proceeding');
      return;
    }

    // Save all AI transform mappings
    const mappings: Record<string, string> = {};
    Object.values(configuredColumns).forEach((col) => {
      if (col.isSaved) {
        mappings[col.columnName] = col.promptTemplate;
      }
    });
    
    console.log('🚀 Saving AI transform mappings:', Object.keys(mappings).length, 'mappings');
    setAiTransformMappings(mappings);
    
    console.log('🚀 Moving to step 5 (Data Preview & Transform)');
    setCurrentStep(5); // Move to Data Preview & Transform (step 5)
  };

  const handlePrevious = () => {
    setCurrentStep(2); // Go back to Target Column Selection (since we removed step 3)
  };

  // Row navigation
  const goToPreviousRow = () => {
    if (currentRowIndex > 0) {
      setCurrentRowIndex(currentRowIndex - 1);
    }
  };

  const goToNextRow = () => {
    if (currentRowIndex < sourceRows.length - 1) {
      setCurrentRowIndex(currentRowIndex + 1);
    }
  };

  const currentRow = sourceRows[currentRowIndex];

  // Show message if no AI transform columns are available
  if (aiTransformColumns.length === 0) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Brain className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No AI Transform Columns</h3>
            <p className="text-gray-500 text-center mb-4">
              No columns are set for AI transformation. Go back to the previous step to configure columns for AI transformation.
            </p>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={handlePrevious}>
                Previous: Target Column Selection
              </Button>
              <Button onClick={handleNext}>
                Skip to Next Step
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

      return (
      <div className="space-y-4">
        {/* Compact Combined Header */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Wand2 className="h-4 w-4" />
                  AI Transform Mapping - Column {currentColumnIndex + 1} of {totalColumns}: {currentColumn}
                </CardTitle>
                <CardDescription className="text-sm">
                  {configuredCount} of {totalColumns} columns configured • Configure prompt template, test with source data, and save
                </CardDescription>
              </div>
              <Badge variant="secondary" className="text-xs">Step 3 of 4</Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {/* Column Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-1">
                {aiTransformColumns.map((column, index) => (
                  <div 
                    key={column}
                    className={`p-1.5 border rounded cursor-pointer transition-colors text-xs ${
                      index === currentColumnIndex 
                        ? 'border-blue-500 bg-blue-50' 
                        : configuredColumns[column]?.isSaved 
                          ? 'border-green-300 bg-green-50' 
                          : 'border-gray-200 bg-white hover:bg-gray-50'
                    }`}
                    onClick={() => setCurrentColumnIndex(index)}
                    title={column}
                  >
                    <div className="flex items-center space-x-1">
                      {configuredColumns[column]?.isSaved ? (
                        <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                      ) : (
                        <Circle className="h-3 w-3 text-gray-400 flex-shrink-0" />
                      )}
                      <span className="truncate font-medium">{column}</span>
                    </div>
                  </div>
                ))}
              </div>



              {/* Column Configuration Details */}
              {currentColumn && columnConfiguration && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 p-3 bg-gray-50 rounded text-xs">
                  <div>
                    <label className="font-medium text-gray-600">Output Validation</label>
                    <p className="text-gray-800">{columnConfiguration.output_validation_column || 'None'}</p>
                  </div>
                  <div>
                    <label className="font-medium text-gray-600">Default Mapping</label>
                    <p className="text-gray-800">{columnConfiguration.default_mapping || 'AI'}</p>
                  </div>
                  <div>
                    <label className="font-medium text-gray-600">Custom Mapping Prompt Template</label>
                    <p className="text-gray-800 truncate" title={columnConfiguration.default_mapping_content || 'None'}>
                      {columnConfiguration.default_mapping_content ? 'Custom template defined' : 'Using default template'}
                    </p>
                  </div>
                  <div>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="h-6 text-xs" 
                          disabled={loadingWorksheet}
                        >
                          {loadingWorksheet ? (
                            <>
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              Loading...
                            </>
                          ) : (
                            <>
                              <TableIcon className="h-3 w-3 mr-1" />
                              Additional Data
                              {worksheetData.length > 0 && (
                                <Badge variant="secondary" className="ml-1 h-4 text-xs">
                                  {worksheetData.length}
                                </Badge>
                              )}
                            </>
                          )}
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Additional Data: {currentColumn}</DialogTitle>
                          <DialogDescription>
                            {availableWorksheets.includes(currentColumn) 
                              ? `Data from the "${currentColumn}" worksheet`
                              : `Looking for additional data for "${currentColumn}"`
                            }
                          </DialogDescription>
                        </DialogHeader>
                        
                        {loadingWorksheet ? (
                          <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin mr-2" />
                            <span>Loading additional data...</span>
                          </div>
                        ) : additionalDataError ? (
                          <Alert className="mb-4">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertTitle>Error Loading Additional Data</AlertTitle>
                            <AlertDescription>{additionalDataError}</AlertDescription>
                          </Alert>
                        ) : worksheetData.length > 0 ? (
                          <>
                            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded">
                              <p className="text-sm text-green-800">
                                ✅ Found {worksheetData.length} rows of additional data from the "{currentColumn}" worksheet
                              </p>
                            </div>
                            <div className="max-h-96 overflow-auto">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    {Object.keys(worksheetData[0]).map(key => (
                                      <TableHead key={key}>{key}</TableHead>
                                    ))}
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {worksheetData.map((row, index) => (
                                    <TableRow key={index}>
                                      {Object.values(row).map((value, cellIndex) => (
                                        <TableCell key={cellIndex}>
                                          {String(value)}
                                        </TableCell>
                                      ))}
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </>
                        ) : (
                          <div className="text-center py-8">
                            <TableIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                            <p className="text-gray-500 mb-2">
                              No additional worksheet found for "{currentColumn}"
                            </p>
                            <p className="text-sm text-gray-400">
                              Available worksheets: {availableWorksheets.length > 0 ? availableWorksheets.join(', ') : 'None loaded'}
                            </p>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="mt-3"
                              onClick={() => loadWorksheetData(currentColumn)}
                            >
                              Retry Loading
                            </Button>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

      {/* Three Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Source Data Panel */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Database className="h-4 w-4" />
                  Source Data Preview
                </CardTitle>
                <CardDescription className="text-sm">
                  Row {currentRowIndex + 1} of {sourceRows.length}
                </CardDescription>
              </div>
              
              {/* Column Navigation */}
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={goToPreviousColumn}
                  disabled={currentColumnIndex === 0}
                  className="text-xs"
                >
                  <ArrowLeft className="h-3 w-3" />
                  Prev Col
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={goToNextColumn}
                  disabled={currentColumnIndex === aiTransformColumns.length - 1}
                  className="text-xs"
                >
                  Next Col
                  <ArrowRight className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0 space-y-4">
            {/* Row Navigation */}
            <div className="flex items-center justify-between">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={goToPreviousRow}
                disabled={currentRowIndex === 0 || loadingSourceData}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <span className="text-sm text-gray-600">
                Row {currentRowIndex + 1}
              </span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={goToNextRow}
                disabled={currentRowIndex === sourceRows.length - 1 || loadingSourceData}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Row Data Display */}
            {loadingSourceData ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : sourceDataError ? (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{sourceDataError}</AlertDescription>
              </Alert>
            ) : currentRow ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {Object.entries(currentRow).map(([column, content]) => (
                  <div key={column} className="border rounded p-3">
                    <div className="font-medium text-sm mb-1">{column}</div>
                    <div className="text-sm text-gray-700 break-words">
                      {String(content || '')}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-gray-500 py-8">No source data available</p>
            )}
          </CardContent>
        </Card>

        {/* Prompt Engineering Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Edit3 className="h-4 w-4" />
              Prompt Engineering
            </CardTitle>
            <CardDescription>
              Template and rendered prompt for {currentColumn}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Template/Rendered/System Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={!isPromptRendered && !showSystemPrompt ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setIsPromptRendered(false);
                  setShowSystemPrompt(false);
                }}
              >
                Template
              </Button>
              <Button
                variant={isPromptRendered && !showSystemPrompt ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setShowSystemPrompt(false);
                  togglePromptView();
                }}
                disabled={!currentPromptTemplate || sourceRows.length === 0}
              >
                Rendered Prompt
              </Button>
              <Button
                variant={showSystemPrompt ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setShowSystemPrompt(true);
                  setIsPromptRendered(false);
                }}
                disabled={loadingSystemPrompt}
              >
                {loadingSystemPrompt ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'System Prompt'
                )}
              </Button>
              <Dialog open={showNotesModal} onOpenChange={setShowNotesModal}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                  >
                    <Edit3 className="h-3 w-3 mr-1" />
                    Job Notes
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Job Notes</DialogTitle>
                    <DialogDescription>
                      Add notes for this job that will be used in the @notes placeholder
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <Textarea
                      value={jobNotes}
                      onChange={(e) => setJobNotes(e.target.value)}
                      placeholder="Enter notes for this job..."
                      className="min-h-[200px]"
                      disabled={loadingNotes}
                    />
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setShowNotesModal(false)}
                        disabled={savingNotes}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={saveJobNotes}
                        disabled={savingNotes}
                      >
                        {savingNotes ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Notes
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            {/* Prompt Content */}
            {showSystemPrompt ? (
              <div className="space-y-2">
                <label className="text-sm font-medium">System Prompt</label>
                <div 
                  className="min-h-[300px] p-3 border rounded font-mono text-sm bg-blue-50 overflow-y-auto whitespace-pre-wrap"
                >
                  {loadingSystemPrompt ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin" />
                      <span className="ml-2">Loading system prompt...</span>
                    </div>
                  ) : (
                    systemPrompt || 'No system prompt available'
                  )}
                </div>
                <div className="text-xs text-gray-500">
                  This is the system prompt that will be sent to the LLM for all transformations.
                </div>
              </div>
            ) : !isPromptRendered ? (
              <div className="space-y-2">
                <label className="text-sm font-medium">Prompt Template</label>
                <Textarea
                  value={currentPromptTemplate}
                  onChange={(e) => setCurrentPromptTemplate(e.target.value)}
                  placeholder="Enter your prompt template with @placeholders..."
                  className="min-h-[300px] font-mono text-sm"
                />
              </div>
            ) : (
              <div className="space-y-2">
                <label className="text-sm font-medium">Rendered Prompt</label>
                <div 
                  className="min-h-[300px] p-3 border rounded font-mono text-sm bg-gray-50 overflow-y-auto whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: renderedPrompt || 'Click "Rendered Prompt" to process template...'
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test LLM Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Test Prompt with LLM
            </CardTitle>
            <CardDescription>
              Test transformation for {currentColumn}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Model Selection */}
            <ModelSelector
              selectedModel={currentColumnModel}
              onModelChange={setCurrentColumnModel}
              label="AI Model for Column Test"
              description={`Select the model to test for the "${currentColumn}" column`}
              showPricing={true}
              className="mb-4"
            />
            
            {/* Structured Output Controls */}
            <div className="space-y-3 p-3 bg-gray-50 rounded border">
              <div className="flex items-center justify-between text-sm">
                <label className="font-medium text-gray-700">Advanced Settings</label>
                <Badge variant="secondary" className="text-xs">
                  {useStructuredOutput ? 'Groq Enhanced' : 'Standard'}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                {/* Left Column - Checkboxes */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="useStructuredOutput"
                      checked={useStructuredOutput}
                      onChange={(e) => setUseStructuredOutput(e.target.checked)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <label htmlFor="useStructuredOutput" className="text-sm text-gray-700">
                      Use Structured Output
                    </label>
                  </div>
                  
                  {useStructuredOutput && (
                    <div className="flex items-center space-x-2 ml-6">
                      <input
                        type="checkbox"
                        id="showReasoning"
                        checked={showReasoning}
                        onChange={(e) => setShowReasoning(e.target.checked)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      />
                      <label htmlFor="showReasoning" className="text-sm text-gray-700">
                        Show Reasoning
                      </label>
                    </div>
                  )}
                </div>
                
                {/* Right Column - Temperature */}
                <div className="space-y-2">
                  <label htmlFor="temperature" className="text-sm font-medium text-gray-700">
                    Temperature: {temperature}
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      id="temperature"
                      min="0"
                      max="2"
                      step="0.1"
                      value={temperature}
                      onChange={(e) => setTemperature(parseFloat(e.target.value))}
                      className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <input
                      type="number"
                      min="0"
                      max="2"
                      step="0.1"
                      value={temperature}
                      onChange={(e) => setTemperature(Math.min(2, Math.max(0, parseFloat(e.target.value) || 0)))}
                      className="w-16 px-2 py-1 text-xs border rounded"
                    />
                  </div>
                  <div className="text-xs text-gray-500">
                    Controls randomness (0=focused, 2=creative)
                  </div>
                </div>
              </div>
              
              <div className="text-xs text-gray-500">
                {useStructuredOutput 
                  ? 'Separates reasoning from final answer for cleaner extraction'
                  : 'Uses standard response format'
                }
              </div>
            </div>

            <Button 
              onClick={sendPromptToLLM}
              disabled={isTestingLLM || !currentPromptTemplate || sourceRows.length === 0}
              className="w-full"
            >
              {isTestingLLM ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Test Prompt with LLM
                </>
              )}
            </Button>
            
            {/* Raw Request Content */}
            {llmRawContent && (
              <div className="space-y-2">
                <Button
                  onClick={() => setShowLlmRawContent(!showLlmRawContent)}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  {showLlmRawContent ? 'Hide' : 'Show'} Raw Request Content
                </Button>
                
                {showLlmRawContent && (
                  <div className="border rounded p-3 bg-gray-50 max-h-[200px] overflow-y-auto">
                    <div className="font-medium text-sm mb-2">Request sent to LLM API:</div>
                    <div className="font-mono text-xs whitespace-pre-wrap">
                      {llmRawContent}
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {llmResponse && (
              <div className="space-y-4">
                {/* Status Badges */}
                <div className="flex items-center gap-2 text-sm flex-wrap">
                  {/* Cache Status */}
                  {(llmResponse.fromCache || llmResponse.cacheBypassed) && (
                    <>
                      <span className="text-gray-600">Cache:</span>
                      {llmResponse.cacheBypassed ? (
                        <Badge variant="outline" className="text-yellow-600 border-yellow-300">
                          🚫 Bypassed
                        </Badge>
                      ) : llmResponse.fromCache ? (
                        <Badge variant="outline" className="text-green-600 border-green-300">
                          ⚡ Hit
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-blue-600 border-blue-300">
                          🔄 Miss
                        </Badge>
                      )}
                    </>
                  )}
                  
                  {/* Provider Badge */}
                  {llmResponse.provider && (
                    <>
                      <span className="text-gray-600">Provider:</span>
                      <Badge variant="outline" className="text-purple-600 border-purple-300">
                        {llmResponse.provider}
                      </Badge>
                    </>
                  )}
                  
                  {/* Structured Output Badge */}
                  {llmResponse.structured_output && (
                    <>
                      <span className="text-gray-600">Format:</span>
                      <Badge variant="outline" className="text-green-600 border-green-300">
                        🧠 Structured
                      </Badge>
                    </>
                  )}
                </div>

                {/* Response View Toggle */}
                <div className="flex items-center gap-2 flex-wrap">
                  <Button
                    variant={showRawResponse ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowRawResponse(true)}
                  >
                    Raw Response
                  </Button>
                  <Button
                    variant={!showRawResponse && !showReasoning ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setShowRawResponse(false);
                      setShowReasoning(false);
                    }}
                  >
                    {llmResponse.structured_output ? "Final Answer" : "Extracted Response"}
                  </Button>
                  {/* Always show Reasoning button when reasoning is available */}
                  {llmResponse.reasoning && llmResponse.reasoning.trim() && (
                    <Button
                      variant={!showRawResponse && showReasoning ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        setShowRawResponse(false);
                        setShowReasoning(true);
                      }}
                    >
                      Reasoning
                    </Button>
                  )}
                </div>
                
                {/* Response Display */}
                <div className="border rounded p-3 bg-gray-50 min-h-[200px] overflow-y-auto">
                  <div className="font-medium text-sm mb-2">
                    {showRawResponse ? "Raw Response:" :
                     showReasoning ? "Reasoning Process:" :
                     llmResponse.structured_output ? "Final Answer:" :
                     "Extracted Response:"}
                  </div>
                  <div className="font-mono text-sm whitespace-pre-wrap">
                    {showRawResponse ? llmResponse.raw :
                     showReasoning ? (llmResponse.reasoning || 'No reasoning available') :
                     llmResponse.structured_output ? (llmResponse.answer || llmResponse.extracted) :
                     llmResponse.extracted}
                  </div>
                  
                  {/* Helper Text */}
                  {!showRawResponse && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="text-xs text-gray-500">
                        {showReasoning ? (
                          <>💭 This shows the AI's step-by-step reasoning process. Switch to "{llmResponse.structured_output ? 'Final Answer' : 'Extracted Response'}" to see just the result.</>
                        ) : (
                          <>✨ This is the clean extracted answer. {llmResponse.reasoning && llmResponse.reasoning.trim() && 'Switch to "Reasoning" to see the thought process.'}</>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center" key={`navigation-${stateRefreshTrigger}-${allColumnsConfigured}`}>
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous: Column Mapping
        </Button>
        
        <div className="flex items-center space-x-3">
          {!allColumnsConfigured && (
            <Badge variant="outline" className="px-2 py-1 text-xs">
              {configuredCount} of {totalColumns} configured
            </Badge>
          )}
          
          {/* Save Current Template Button */}
          {!isCurrentColumnConfigured && (
            <Button 
              onClick={() => saveTemplateToDatabase(true)}
              disabled={isSavingTemplate || !currentPromptTemplate.trim()}
              className="flex items-center space-x-2"
              size="sm"
            >
              {isSavingTemplate ? (
                <>
                  <Loader2 className="h-3 w-3 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="h-3 w-3" />
                  <span>Save Template</span>
                </>
              )}
            </Button>
          )}
          
          {/* Save All Templates Button */}
          <Button 
            variant="outline"
            onClick={saveAllTemplates}
            disabled={isSavingTemplate || aiTransformColumns.length === 0}
            className="flex items-center space-x-2"
            size="sm"
          >
            {isSavingTemplate ? (
              <>
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>Save All</span>
              </>
            ) : (
              <>
                <Save className="h-3 w-3" />
                <span>Save All</span>
              </>
            )}
          </Button>
          
          {/* Clear All Mappings Button */}
          <Button 
            variant="outline"
            onClick={clearAllMappings}
            disabled={isSavingTemplate || configuredCount === 0}
            className="flex items-center space-x-2 border-red-200 text-red-600 hover:bg-red-50"
            size="sm"
          >
            <Trash2 className="h-3 w-3" />
            <span>Clear All</span>
          </Button>
          
          <Button 
            onClick={() => {
              console.log('🔘 Button clicked - State:', { 
                allColumnsConfigured, 
                nextButtonEnabled,
                configuredCount, 
                totalColumns,
                disabled: !nextButtonEnabled 
              });
              handleNext();
            }}
            disabled={!nextButtonEnabled}
            onMouseEnter={() => console.log('🔘 Button hover - State:', { 
              nextButtonEnabled, 
              allColumnsConfigured, 
              disabled: !nextButtonEnabled 
            })}
          >
            {nextButtonEnabled ? (
              <>
                Next: Data Preview & Transform
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            ) : (
              <>
                Configure All Columns First
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
} 