"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Edit3,
  Loader2,
  Save
} from "lucide-react";

interface PromptTemplateEditorProps {
  currentColumn: string;
  currentPromptTemplate: string;
  onPromptTemplateChange: (template: string) => void;
  renderedPrompt: string;
  isPromptRendered: boolean;
  systemPrompt: string;
  loadingSystemPrompt: boolean;
  onRenderPrompt: () => void;
  onShowSystemPrompt: () => void;
  showSystemPrompt: boolean;
  jobNotes: string;
  onJobNotesChange: (notes: string) => void;
  onSaveJobNotes: () => void;
  showNotesModal: boolean;
  onShowNotesModalChange: (show: boolean) => void;
  loadingNotes: boolean;
  savingNotes: boolean;
}

export function PromptTemplateEditor({ 
  currentColumn,
  currentPromptTemplate,
  onPromptTemplateChange,
  renderedPrompt,
  isPromptRendered,
  systemPrompt,
  loadingSystemPrompt,
  onRenderPrompt,
  onShowSystemPrompt,
  showSystemPrompt,
  jobNotes,
  onJobNotesChange,
  onSaveJobNotes,
  showNotesModal,
  onShowNotesModalChange,
  loadingNotes,
  savingNotes
}: PromptTemplateEditorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Edit3 className="h-4 w-4" />
          Prompt Engineering
        </CardTitle>
        <CardDescription>
          Template and rendered prompt for {currentColumn}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Template/Rendered/System Toggle */}
        <div className="flex items-center gap-2">
          <Button
            variant={!isPromptRendered && !showSystemPrompt ? "default" : "outline"}
            size="sm"
            onClick={() => {
              onShowSystemPrompt();
              // Reset to template view
            }}
          >
            Template
          </Button>
          <Button
            variant={isPromptRendered && !showSystemPrompt ? "default" : "outline"}
            size="sm"
            onClick={onRenderPrompt}
            disabled={!currentPromptTemplate}
          >
            Rendered Prompt
          </Button>
          <Button
            variant={showSystemPrompt ? "default" : "outline"}
            size="sm"
            onClick={onShowSystemPrompt}
            disabled={loadingSystemPrompt}
          >
            {loadingSystemPrompt ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Loading...
              </>
            ) : (
              'System Prompt'
            )}
          </Button>
          <Dialog open={showNotesModal} onOpenChange={onShowNotesModalChange}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
              >
                <Edit3 className="h-3 w-3 mr-1" />
                Job Notes
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Job Notes</DialogTitle>
                <DialogDescription>
                  Add notes for this job that will be used in the @notes placeholder
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <Textarea
                  value={jobNotes}
                  onChange={(e) => onJobNotesChange(e.target.value)}
                  placeholder="Enter notes for this job..."
                  className="min-h-[200px]"
                  disabled={loadingNotes}
                />
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => onShowNotesModalChange(false)}
                    disabled={savingNotes}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={onSaveJobNotes}
                    disabled={savingNotes}
                  >
                    {savingNotes ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Notes
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* Prompt Content */}
        {showSystemPrompt ? (
          <div className="space-y-2">
            <label className="text-sm font-medium">System Prompt</label>
            <div 
              className="min-h-[300px] p-3 border rounded font-mono text-sm bg-blue-50 overflow-y-auto whitespace-pre-wrap"
            >
              {loadingSystemPrompt ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading system prompt...</span>
                </div>
              ) : (
                systemPrompt || 'No system prompt available'
              )}
            </div>
            <div className="text-xs text-gray-500">
              This is the system prompt that will be sent to the LLM for all transformations.
            </div>
          </div>
        ) : !isPromptRendered ? (
          <div className="space-y-2">
            <label className="text-sm font-medium">Prompt Template</label>
            <Textarea
              value={currentPromptTemplate}
              onChange={(e) => onPromptTemplateChange(e.target.value)}
              placeholder="Enter your prompt template with @placeholders..."
              className="min-h-[300px] font-mono text-sm"
            />
            <div className="text-xs text-gray-500">
              Use placeholders like @description, @row, @notes, @prompt_additional_data, etc.
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <label className="text-sm font-medium">Rendered Prompt</label>
            <div 
              className="min-h-[300px] p-3 border rounded font-mono text-sm bg-gray-50 overflow-y-auto whitespace-pre-wrap"
              dangerouslySetInnerHTML={{
                __html: renderedPrompt || 'Click "Rendered Prompt" to process template...'
              }}
            />
            <div className="text-xs text-gray-500">
              This is how the prompt will look with placeholders replaced by actual data.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 