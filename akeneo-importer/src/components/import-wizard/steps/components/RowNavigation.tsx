"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  ChevronLeft,
  ChevronRight,
  SkipForward,
  Ski<PERSON>Back,
  FastForward,
  Rewind
} from "lucide-react";
import { useState } from "react";

export interface RowNavigationProps {
  currentRowIndex: number;
  totalRows: number;
  onRowIndexChange: (index: number) => void;
  disabled?: boolean;
  showJumpToRow?: boolean;
  showFirstLast?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  label?: string;
}

export function RowNavigation({
  currentRowIndex,
  totalRows,
  onRowIndexChange,
  disabled = false,
  showJumpToRow = true,
  showFirstLast = true,
  size = 'md',
  className = '',
  label = 'Row'
}: RowNavigationProps) {
  const [jumpToRowValue, setJumpToRowValue] = useState<string>('');

  // Computed values
  const currentRowNumber = currentRowIndex + 1;
  const hasNextRow = currentRowIndex < totalRows - 1;
  const hasPreviousRow = currentRowIndex > 0;
  
  // Size variants
  const sizeVariants = {
    sm: {
      button: 'h-8 px-2',
      input: 'h-8 w-16 text-sm',
      text: 'text-sm'
    },
    md: {
      button: 'h-9 px-3',
      input: 'h-9 w-20 text-sm',
      text: 'text-sm'
    },
    lg: {
      button: 'h-10 px-4',
      input: 'h-10 w-24',
      text: 'text-base'
    }
  };
  
  const variant = sizeVariants[size];

  // Navigation handlers
  const goToFirstRow = () => {
    if (!disabled && totalRows > 0) {
      onRowIndexChange(0);
    }
  };

  const goToPreviousRow = () => {
    if (!disabled && hasPreviousRow) {
      onRowIndexChange(currentRowIndex - 1);
    }
  };

  const goToNextRow = () => {
    if (!disabled && hasNextRow) {
      onRowIndexChange(currentRowIndex + 1);
    }
  };

  const goToLastRow = () => {
    if (!disabled && totalRows > 0) {
      onRowIndexChange(totalRows - 1);
    }
  };

  const goToSpecificRow = () => {
    const rowNumber = parseInt(jumpToRowValue);
    if (!isNaN(rowNumber) && rowNumber >= 1 && rowNumber <= totalRows) {
      onRowIndexChange(rowNumber - 1);
      setJumpToRowValue('');
    }
  };

  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      goToSpecificRow();
    }
  };

  if (totalRows === 0) {
    return (
      <div className={`flex items-center justify-center py-4 ${className}`}>
        <span className={`text-gray-500 ${variant.text}`}>No data available</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-between gap-2 ${className}`}>
      {/* Row counter and info */}
      <div className="flex items-center gap-2">
        <span className={`text-gray-600 ${variant.text}`}>
          {label} {currentRowNumber} of {totalRows}
        </span>
      </div>

      {/* Navigation controls */}
      <div className="flex items-center gap-1">
        {/* First/Last buttons (optional) */}
        {showFirstLast && (
          <>
            <Button
              variant="outline"
              size="sm"
              className={variant.button}
              onClick={goToFirstRow}
              disabled={disabled || currentRowIndex === 0}
              title="Go to first row"
            >
              <Rewind className="h-4 w-4" />
            </Button>
          </>
        )}

        {/* Previous button */}
        <Button
          variant="outline"
          size="sm"
          className={variant.button}
          onClick={goToPreviousRow}
          disabled={disabled || !hasPreviousRow}
          title="Previous row"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Jump to row input (optional) */}
        {showJumpToRow && (
          <div className="flex items-center gap-1">
            <Input
              type="number"
              min="1"
              max={totalRows}
              placeholder={currentRowNumber.toString()}
              value={jumpToRowValue}
              onChange={(e) => setJumpToRowValue(e.target.value)}
              onKeyPress={handleJumpInputKeyPress}
              onBlur={goToSpecificRow}
              disabled={disabled}
              className={variant.input}
              title="Jump to row number"
            />
            <Button
              variant="outline"
              size="sm"
              className={variant.button}
              onClick={goToSpecificRow}
              disabled={disabled || !jumpToRowValue || isNaN(parseInt(jumpToRowValue))}
              title="Go to specified row"
            >
              Go
            </Button>
          </div>
        )}

        {/* Next button */}
        <Button
          variant="outline"
          size="sm"
          className={variant.button}
          onClick={goToNextRow}
          disabled={disabled || !hasNextRow}
          title="Next row"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* First/Last buttons (optional) */}
        {showFirstLast && (
          <Button
            variant="outline"
            size="sm"
            className={variant.button}
            onClick={goToLastRow}
            disabled={disabled || currentRowIndex === totalRows - 1}
            title="Go to last row"
          >
            <FastForward className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

// Compact version for smaller spaces
export interface CompactRowNavigationProps {
  currentRowIndex: number;
  totalRows: number;
  onRowIndexChange: (index: number) => void;
  disabled?: boolean;
  className?: string;
}

export function CompactRowNavigation({
  currentRowIndex,
  totalRows,
  onRowIndexChange,
  disabled = false,
  className = ''
}: CompactRowNavigationProps) {
  const currentRowNumber = currentRowIndex + 1;
  const hasNextRow = currentRowIndex < totalRows - 1;
  const hasPreviousRow = currentRowIndex > 0;

  const goToPreviousRow = () => {
    if (!disabled && hasPreviousRow) {
      onRowIndexChange(currentRowIndex - 1);
    }
  };

  const goToNextRow = () => {
    if (!disabled && hasNextRow) {
      onRowIndexChange(currentRowIndex + 1);
    }
  };

  if (totalRows === 0) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <Button 
        variant="outline" 
        size="sm" 
        onClick={goToPreviousRow}
        disabled={disabled || !hasPreviousRow}
        className="h-8 px-2"
      >
        <ChevronLeft className="h-4 w-4" />
        Previous
      </Button>
      
      <span className="text-sm text-gray-600">
        Row {currentRowNumber}
      </span>
      
      <Button 
        variant="outline" 
        size="sm" 
        onClick={goToNextRow}
        disabled={disabled || !hasNextRow}
        className="h-8 px-2"
      >
        Next
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
} 