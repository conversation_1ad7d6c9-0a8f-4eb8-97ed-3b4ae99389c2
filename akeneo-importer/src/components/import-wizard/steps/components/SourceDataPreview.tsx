"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  Database,
  ChevronLeft,
  ChevronRight,
  Loader2,
  AlertTriangle
} from "lucide-react";

interface SourceDataPreviewProps {
  sourceRows: Record<string, unknown>[];
  currentRowIndex: number;
  onRowIndexChange: (index: number) => void;
  loading?: boolean;
  error?: string;
}

export function SourceDataPreview({ 
  sourceRows, 
  currentRowIndex, 
  onRowIndexChange, 
  loading = false, 
  error 
}: SourceDataPreviewProps) {
  const currentRow = sourceRows[currentRowIndex];

  const goToPreviousRow = () => {
    if (currentRowIndex > 0) {
      onRowIndexChange(currentRowIndex - 1);
    }
  };

  const goToNextRow = () => {
    if (currentRowIndex < sourceRows.length - 1) {
      onRowIndexChange(currentRowIndex + 1);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-base">
              <Database className="h-4 w-4" />
              Source Data Preview
            </CardTitle>
            <p className="text-sm text-gray-600">
              Row {currentRowIndex + 1} of {sourceRows.length}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0 space-y-4">
        {/* Row Navigation */}
        <div className="flex items-center justify-between">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={goToPreviousRow}
            disabled={currentRowIndex === 0 || loading}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Row {currentRowIndex + 1}
          </span>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={goToNextRow}
            disabled={currentRowIndex === sourceRows.length - 1 || loading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Row Data Display */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : error ? (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : currentRow ? (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {Object.entries(currentRow).map(([column, content]) => (
              <div key={column} className="border rounded p-3">
                <div className="font-medium text-sm mb-1">{column}</div>
                <div className="text-sm text-gray-700 break-words">
                  {String(content || '')}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500 py-8">No source data available</p>
        )}
      </CardContent>
    </Card>
  );
} 