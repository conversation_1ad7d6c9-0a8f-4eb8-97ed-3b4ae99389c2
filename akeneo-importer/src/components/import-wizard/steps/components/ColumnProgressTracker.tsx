"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { 
  Wand2,
  CheckCircle,
  Circle,
  ArrowLeft,
  ArrowRight,
  Save,
  Trash2,
  Loader2
} from "lucide-react";
import { ConfiguredColumn } from "@/hooks/use-ai-transform-column";

interface ColumnProgressTrackerProps {
  currentColumn: string;
  currentColumnIndex: number;
  totalColumns: number;
  configuredColumns: Record<string, ConfiguredColumn>;
  progressPercentage: number;
  allColumnsConfigured: boolean;
  aiTransformColumns: string[];
  onColumnIndexChange: (index: number) => void;
  onGoToPreviousColumn: () => void;
  onGoToNextColumn: () => void;
  onSaveAllTemplates: () => void;
  onClearAllMappings: () => void;
  isSavingTemplate: boolean;
}

export function ColumnProgressTracker({
  currentColumn,
  currentColumnIndex,
  totalColumns,
  configuredColumns,
  progressPercentage,
  allColumnsConfigured,
  aiTransformColumns,
  onColumnIndexChange,
  onGoToPreviousColumn,
  onGoToNextColumn,
  onSaveAllTemplates,
  onClearAllMappings,
  isSavingTemplate
}: ColumnProgressTrackerProps) {
  const configuredCount = Object.values(configuredColumns).filter(col => col.isConfigured && col.isSaved).length;

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Wand2 className="h-4 w-4" />
              AI Transform Mapping - Column {currentColumnIndex + 1} of {totalColumns}: {currentColumn}
            </CardTitle>
            <CardDescription className="text-sm">
              {configuredCount} of {totalColumns} columns configured • Configure prompt template, test with source data, and save
            </CardDescription>
          </div>
          <Badge variant="secondary" className="text-xs">Step 3 of 4</Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Configuration Progress</span>
              <span className="text-gray-600">{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Column Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-1">
            {aiTransformColumns.map((column, index) => (
              <div 
                key={column}
                className={`p-1.5 border rounded cursor-pointer transition-colors text-xs ${
                  index === currentColumnIndex 
                    ? 'border-blue-500 bg-blue-50' 
                    : configuredColumns[column]?.isSaved 
                      ? 'border-green-300 bg-green-50' 
                      : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
                onClick={() => onColumnIndexChange(index)}
                title={column}
              >
                <div className="flex items-center space-x-1">
                  {configuredColumns[column]?.isSaved ? (
                    <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                  ) : (
                    <Circle className="h-3 w-3 text-gray-400 flex-shrink-0" />
                  )}
                  <span className="truncate font-medium">{column}</span>
                </div>
              </div>
            ))}
          </div>

          {/* Column Navigation */}
          <div className="flex items-center justify-between">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onGoToPreviousColumn}
              disabled={currentColumnIndex === 0}
              className="text-xs"
            >
              <ArrowLeft className="h-3 w-3" />
              Prev Col
            </Button>
            
            <div className="flex items-center space-x-2">
              {/* Save All Templates Button */}
              <Button 
                variant="outline"
                onClick={onSaveAllTemplates}
                disabled={isSavingTemplate || aiTransformColumns.length === 0}
                className="flex items-center space-x-2"
                size="sm"
              >
                {isSavingTemplate ? (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span>Save All</span>
                  </>
                ) : (
                  <>
                    <Save className="h-3 w-3" />
                    <span>Save All</span>
                  </>
                )}
              </Button>
              
              {/* Clear All Mappings Button */}
              <Button 
                variant="outline"
                onClick={onClearAllMappings}
                disabled={isSavingTemplate || configuredCount === 0}
                className="flex items-center space-x-2 border-red-200 text-red-600 hover:bg-red-50"
                size="sm"
              >
                <Trash2 className="h-3 w-3" />
                <span>Clear All</span>
              </Button>
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onGoToNextColumn}
              disabled={currentColumnIndex === aiTransformColumns.length - 1}
              className="text-xs"
            >
              Next Col
              <ArrowRight className="h-3 w-3" />
            </Button>
          </div>

          {/* Status Summary */}
          {allColumnsConfigured ? (
            <div className="p-3 bg-green-50 border border-green-200 rounded">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  All columns configured! Ready to proceed to data transformation.
                </span>
              </div>
            </div>
          ) : (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded">
              <div className="flex items-center gap-2">
                <Circle className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-800">
                  {totalColumns - configuredCount} columns remaining to configure
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 