"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  TableIcon,
  Loader2,
  AlertTriangle
} from "lucide-react";
import { ColumnConfiguration } from "@/hooks/use-ai-transform-column";

interface ColumnConfigurationPanelProps {
  currentColumn: string;
  columnConfiguration: ColumnConfiguration | null;
  worksheetData: any[];
  availableWorksheets: string[];
  loadingWorksheet: boolean;
  additionalDataError: string;
  onLoadWorksheetData: (columnName: string) => void;
}

export function ColumnConfigurationPanel({
  currentColumn,
  columnConfiguration,
  worksheetData,
  availableWorksheets,
  loadingWorksheet,
  additionalDataError,
  onLoadWorksheetData
}: ColumnConfigurationPanelProps) {
  if (!columnConfiguration) return null;

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 p-3 bg-gray-50 rounded text-xs">
      <div>
        <label className="font-medium text-gray-600">Output Validation</label>
        <p className="text-gray-800">{columnConfiguration.output_validation_column || 'None'}</p>
      </div>
      <div>
        <label className="font-medium text-gray-600">Default Mapping</label>
        <p className="text-gray-800">{columnConfiguration.default_mapping || 'AI'}</p>
      </div>
      <div>
        <label className="font-medium text-gray-600">Custom Mapping Prompt Template</label>
        <p className="text-gray-800 truncate" title={columnConfiguration.default_mapping_content || 'None'}>
          {columnConfiguration.default_mapping_content ? 'Custom template defined' : 'Using default template'}
        </p>
      </div>
      <div>
        <Dialog>
          <DialogTrigger asChild>
            <Button 
              variant="outline" 
              size="sm" 
              className="h-6 text-xs" 
              disabled={loadingWorksheet}
            >
              {loadingWorksheet ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Loading...
                </>
              ) : (
                <>
                  <TableIcon className="h-3 w-3 mr-1" />
                  Additional Data
                  {worksheetData.length > 0 && (
                    <Badge variant="secondary" className="ml-1 h-4 text-xs">
                      {worksheetData.length}
                    </Badge>
                  )}
                </>
              )}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Additional Data: {currentColumn}</DialogTitle>
              <DialogDescription>
                {availableWorksheets.includes(currentColumn) 
                  ? `Data from the "${currentColumn}" worksheet`
                  : `Looking for additional data for "${currentColumn}"`
                }
              </DialogDescription>
            </DialogHeader>
            
            {loadingWorksheet ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading additional data...</span>
              </div>
            ) : additionalDataError ? (
              <Alert className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error Loading Additional Data</AlertTitle>
                <AlertDescription>{additionalDataError}</AlertDescription>
              </Alert>
            ) : worksheetData.length > 0 ? (
              <>
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-sm text-green-800">
                    ✅ Found {worksheetData.length} rows of additional data from the "{currentColumn}" worksheet
                  </p>
                </div>
                <div className="max-h-96 overflow-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {Object.keys(worksheetData[0]).map(key => (
                          <TableHead key={key}>{key}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {worksheetData.map((row, index) => (
                        <TableRow key={index}>
                          {Object.values(row).map((value, cellIndex) => (
                            <TableCell key={cellIndex}>
                              {String(value)}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <TableIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 mb-2">
                  No additional worksheet found for "{currentColumn}"
                </p>
                <p className="text-sm text-gray-400">
                  Available worksheets: {availableWorksheets.length > 0 ? availableWorksheets.join(', ') : 'None loaded'}
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-3"
                  onClick={() => onLoadWorksheetData(currentColumn)}
                >
                  Retry Loading
                </Button>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
} 