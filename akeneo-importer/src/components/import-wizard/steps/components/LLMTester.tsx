"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ModelSelector } from "@/components/ui/model-selector";
import { 
  Zap,
  Loader2,
  Play
} from "lucide-react";
import { LLMResponse } from "@/hooks/use-ai-transform-column";
import { LLM_CONSTANTS } from "@/lib/constants/llm-constants";

interface LLMTesterProps {
  currentColumn: string;
  currentColumnModel: string | null;
  onCurrentColumnModelChange: (model: string | null) => void;
  isTestingLLM: boolean;
  llmResponse: LLMResponse | null;
  onTestPrompt: () => void;
  temperature: number;
  onTemperatureChange: (temp: number) => void;
  useStructuredOutput: boolean;
  onUseStructuredOutputChange: (use: boolean) => void;
  showReasoning: boolean;
  onShowReasoningChange: (show: boolean) => void;
  showRawResponse: boolean;
  onShowRawResponseChange: (show: boolean) => void;
}

export function LLMTester({
  currentColumn,
  currentColumnModel,
  onCurrentColumnModelChange,
  isTestingLLM,
  llmResponse,
  onTestPrompt,
  temperature,
  onTemperatureChange,
  useStructuredOutput,
  onUseStructuredOutputChange,
  showReasoning,
  onShowReasoningChange,
  showRawResponse,
  onShowRawResponseChange
}: LLMTesterProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-4 w-4" />
          Test Prompt with LLM
        </CardTitle>
        <CardDescription>
          Test transformation for {currentColumn}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Model Selection */}
        <ModelSelector
          selectedModel={currentColumnModel}
          onModelChange={onCurrentColumnModelChange}
          label="AI Model for Column Test"
          description={`Select the model to test for the "${currentColumn}" column`}
          showPricing={true}
          className="mb-4"
        />
        
        {/* Advanced Settings */}
        <div className="space-y-3 p-3 bg-gray-50 rounded border">
          <div className="flex items-center justify-between text-sm">
            <label className="font-medium text-gray-700">Advanced Settings</label>
            <Badge variant="secondary" className="text-xs">
              {useStructuredOutput ? 'Groq Enhanced' : 'Standard'}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            {/* Left Column - Checkboxes */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="useStructuredOutput"
                  checked={useStructuredOutput}
                  onChange={(e) => onUseStructuredOutputChange(e.target.checked)}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="useStructuredOutput" className="text-sm text-gray-700">
                  Use Structured Output
                </label>
              </div>
              
              {useStructuredOutput && (
                <div className="flex items-center space-x-2 ml-6">
                  <input
                    type="checkbox"
                    id="showReasoning"
                    checked={showReasoning}
                    onChange={(e) => onShowReasoningChange(e.target.checked)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="showReasoning" className="text-sm text-gray-700">
                    Show Reasoning
                  </label>
                </div>
              )}
            </div>
            
            {/* Right Column - Temperature */}
            <div className="space-y-2">
              <label htmlFor="temperature" className="text-sm font-medium text-gray-700">
                Temperature: {temperature}
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  id="temperature"
                  min={LLM_CONSTANTS.TEMPERATURE.MIN}
                  max={LLM_CONSTANTS.TEMPERATURE.MAX}
                  step={LLM_CONSTANTS.TEMPERATURE.STEP}
                  value={temperature}
                  onChange={(e) => onTemperatureChange(parseFloat(e.target.value))}
                  className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  min={LLM_CONSTANTS.TEMPERATURE.MIN}
                  max={LLM_CONSTANTS.TEMPERATURE.MAX}
                  step={LLM_CONSTANTS.TEMPERATURE.STEP}
                  value={temperature}
                  onChange={(e) => onTemperatureChange(Math.min(LLM_CONSTANTS.TEMPERATURE.MAX, Math.max(LLM_CONSTANTS.TEMPERATURE.MIN, parseFloat(e.target.value) || 0)))}
                  className="w-16 px-2 py-1 text-xs border rounded"
                />
              </div>
              <div className="text-xs text-gray-500">
                Controls randomness (0=focused, 2=creative)
              </div>
            </div>
          </div>
          
          <div className="text-xs text-gray-500">
            {useStructuredOutput 
              ? 'Separates reasoning from final answer for cleaner extraction'
              : 'Uses standard response format'
            }
          </div>
        </div>

        <Button 
          onClick={onTestPrompt}
          disabled={isTestingLLM}
          className="w-full"
        >
          {isTestingLLM ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Testing...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Test Prompt with LLM
            </>
          )}
        </Button>
        
        {llmResponse && (
          <div className="space-y-4">
            {/* Status Badges */}
            <div className="flex items-center gap-2 text-sm flex-wrap">
              {/* Cache Status */}
              {(llmResponse.fromCache || llmResponse.cacheBypassed) && (
                <>
                  <span className="text-gray-600">Cache:</span>
                  {llmResponse.cacheBypassed ? (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-300">
                      🚫 Bypassed
                    </Badge>
                  ) : llmResponse.fromCache ? (
                    <Badge variant="outline" className="text-green-600 border-green-300">
                      ⚡ Hit
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-blue-600 border-blue-300">
                      🔄 Miss
                    </Badge>
                  )}
                </>
              )}
              
              {/* Provider Badge */}
              {llmResponse.provider && (
                <>
                  <span className="text-gray-600">Provider:</span>
                  <Badge variant="outline" className="text-purple-600 border-purple-300">
                    {llmResponse.provider}
                  </Badge>
                </>
              )}
              
              {/* Structured Output Badge */}
              {llmResponse.structured_output && (
                <>
                  <span className="text-gray-600">Format:</span>
                  <Badge variant="outline" className="text-green-600 border-green-300">
                    🧠 Structured
                  </Badge>
                </>
              )}
            </div>

            {/* Response view options */}
            <div className="flex items-center gap-2 flex-wrap">
              <Button
                variant={showRawResponse ? "default" : "outline"}
                size="sm"
                onClick={() => onShowRawResponseChange(true)}
              >
                Raw Response
              </Button>
              <Button
                variant={!showRawResponse && !showReasoning ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  onShowRawResponseChange(false);
                  onShowReasoningChange(false);
                }}
              >
                {llmResponse.structured_output ? "Final Answer" : "Extracted Response"}
              </Button>
              {/* Always show Reasoning button when reasoning is available */}
              {llmResponse.reasoning && llmResponse.reasoning.trim() && (
                <Button
                  variant={!showRawResponse && showReasoning ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    onShowRawResponseChange(false);
                    onShowReasoningChange(true);
                  }}
                >
                  Reasoning
                </Button>
              )}
            </div>
            
            {/* Response Display */}
            <div className="border rounded p-3 bg-gray-50 min-h-[200px] overflow-y-auto">
              <div className="font-medium text-sm mb-2">
                {showRawResponse ? "Raw Response:" :
                 showReasoning ? "Reasoning Process:" :
                 llmResponse.structured_output ? "Final Answer:" :
                 "Extracted Response:"}
              </div>
              <div className="font-mono text-sm whitespace-pre-wrap">
                {showRawResponse ? llmResponse.raw :
                 showReasoning ? (llmResponse.reasoning || 'No reasoning available') :
                 llmResponse.structured_output ? (llmResponse.answer || llmResponse.extracted) :
                 llmResponse.extracted}
              </div>
              
              {/* Helper Text */}
              {!showRawResponse && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="text-xs text-gray-500">
                    {showReasoning ? (
                      <>💭 This shows the AI's step-by-step reasoning process. Switch to "{llmResponse.structured_output ? 'Final Answer' : 'Extracted Response'}" to see just the result.</>
                    ) : (
                      <>✨ This is the clean extracted answer. {llmResponse.reasoning && llmResponse.reasoning.trim() && 'Switch to "Reasoning" to see the thought process.'}</>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 