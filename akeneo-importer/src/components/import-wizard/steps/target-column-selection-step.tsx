"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, CheckCircle, Database, Package, Settings } from "lucide-react";
import { toast } from "sonner";
import { useImportWizardStore } from "@/stores/import-wizard-store";
import { useUnifiedConfig } from "@/hooks/use-unified-config";
import { GoogleSheetsAuthDialog } from "../google-sheets-auth-dialog";
import type { MappingType } from "@/stores/import-wizard-store";

interface TargetColumn {
  column_name: string;
  display_name: string;
  type: "Navisionvorlage" | "Akeneo" | "eBay" | "Amazon";
  required: boolean;
  default_mapping: string | null;
  default_mapping_content: string | null;
  prompt: string | null;
  output_validation_column: string | null;
}

type ColumnSystemType = "navision" | "akeneo" | "ebay" | "amazon";

// Helper function to determine mapping type from configuration
function getMappingTypeFromConfig(defaultMapping: string | null): MappingType {
  if (!defaultMapping) return 'ai-transform';
  
  switch (defaultMapping.toLowerCase()) {
    case 'string':
    case 'deactivated':
    case 'static':
      return 'default';
    case 'ai':
      return 'ai-transform';
    default:
      return 'ai-transform'; // Default to AI transform for unknown types
  }
}

export function TargetColumnSelectionStep() {
  const { 
    selectedTargetColumns, 
    setSelectedTargetColumns,
    setColumnMappingTypes,
    columnMappingTypes,
    markStepCompleted,
    selectedTargetSystemType,
    setSelectedTargetSystemType
  } = useImportWizardStore();
  
  const [columnSystemType, setColumnSystemType] = useState<ColumnSystemType>(
    selectedTargetSystemType || "navision"
  );
  const [allTargetColumns, setAllTargetColumns] = useState<TargetColumn[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  
  const unifiedConfig = useUnifiedConfig();

  // Filter columns based on system type
  const filteredColumns = allTargetColumns.filter(col => {
    switch (columnSystemType) {
      case "navision":
        return col.type === "Navisionvorlage";
      case "akeneo":
        return col.type === "Akeneo";
      case "ebay":
        return col.type === "eBay";
      case "amazon":
        return col.type === "Amazon";
      default:
        return true;
    }
  });

  // Initialize with empty selection - let user manually select columns
  useEffect(() => {
    // Reset selection when system type changes to avoid infinite loops
    setSelectedTargetColumns([]);
  }, [columnSystemType]);

  // Load initial columns using unified config
  const loadInitialColumns = useCallback(async () => {
    setLoading(true);
    
    // Check if unified config has auth error
    if (unifiedConfig.authError && unifiedConfig.retryCount >= unifiedConfig.maxRetries) {
      console.warn('Max retries reached and auth error detected - showing auth dialog');
      setShowAuthDialog(true);
      setLoading(false);
      return;
    }
    
    try {
      // Use unified config data if available
      if (unifiedConfig.columns.length > 0) {
        console.log('📋 Using columns from unified config:', unifiedConfig.columns.length);
        
        const targetColumns: TargetColumn[] = unifiedConfig.columns.map((col) => ({
          column_name: col.column_name,
          display_name: col.column_name,
          // TYPE MAPPING LOGIC - ROBUST HANDLING:
          // - From configuration (Excel/Google Sheets):
          //   - Type contains "Navision" or "ERP" → ERP (Navisionvorlage)
          //   - Type contains "Akeneo" or "PIM" → PIM (Akeneo)
          // - From external APIs:
          //   - Type = "eBay" → eBay
          //   - Type = "Amazon" → Amazon
          type: (() => {
            const typeStr = String(col.type).toLowerCase().trim();
            // Check for ERP/Navision patterns first
            if (typeStr.includes('navision') || typeStr === 'erp') {
              return 'Navisionvorlage' as const;
            }
            // Check for eBay
            if (typeStr.includes('ebay')) {
              return 'eBay' as const;
            }
            // Check for Amazon
            if (typeStr.includes('amazon')) {
              return 'Amazon' as const;
            }
            // Default to Akeneo for PIM or anything else
            return 'Akeneo' as const;
          })(),
          required: col.required || false,
          default_mapping: col.default_mapping || null,
          default_mapping_content: col.default_mapping_content || null,
          prompt: col.prompt || null,
          output_validation_column: col.output_validation_column || null,
        }));
        
        // Debug: Check what types are coming from unified config
        const typeDistribution = unifiedConfig.columns.reduce((acc, col) => {
          const type = String(col.type);
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        console.log('🔍 Raw column types from unified config:', typeDistribution);
        console.log('📊 Sample raw columns:', unifiedConfig.columns.slice(0, 5).map(c => ({
          name: c.column_name,
          type: c.type,
          typeString: String(c.type)
        })));
        
        console.log('✅ Mapped target columns:', {
          total: targetColumns.length,
          erp: targetColumns.filter(c => c.type === 'Navisionvorlage').length,
          pim: targetColumns.filter(c => c.type === 'Akeneo').length,
          ebay: targetColumns.filter(c => c.type === 'eBay').length,
          amazon: targetColumns.filter(c => c.type === 'Amazon').length
        });
        
        setAllTargetColumns(targetColumns);
        return;
      }
      
      // If no columns and not loading, trigger refresh
      if (!unifiedConfig.isLoading) {
        console.log('🔄 Triggering unified config refresh...');
        await unifiedConfig.refreshConfig();
      }
      
    } catch (error) {
      console.error('Failed to load target columns:', error);
      
      // Check if this is an auth error
      if (unifiedConfig.authError || (error instanceof Error && error.message.includes('authentication'))) {
        setShowAuthDialog(true);
      } else {
        toast.error(`Failed to load target columns (${unifiedConfig.retryCount}/${unifiedConfig.maxRetries} attempts)`);
      }
    } finally {
      setLoading(false);
    }
  }, [unifiedConfig]);

  // Load external system columns (Akeneo, eBay, Amazon)
  const loadExternalColumns = useCallback(async () => {
    // Skip loading if system is navision
    if (columnSystemType === "navision") {
      return;
    }
    
    try {
      let endpoint = '';
      let systemType: "Akeneo" | "eBay" | "Amazon" = "Akeneo";
      
      switch (columnSystemType) {
        case "akeneo":
          endpoint = '/api/akeneo/attributes?all=true';
          systemType = "Akeneo";
          break;
        case "ebay":
          endpoint = '/api/ebay/columns';
          systemType = "eBay";
          break;
        case "amazon":
          endpoint = '/api/amazon/columns';
          systemType = "Amazon";
          break;
        default:
          return;
      }
      
      const response = await fetch(endpoint);
      
      // Check if response is ok and has content
      if (!response.ok) {
        console.warn(`Failed to load ${systemType} columns:`, response.status, response.statusText);
        return;
      }
      
      // Check if response has content
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.warn(`${systemType} API returned non-JSON response`);
        return;
      }
      
      const result = await response.json();
      
      if (result.success && result.attributes) {
        const externalColumns: TargetColumn[] = result.attributes.map((attr: any) => ({
          column_name: attr.code,
          display_name: attr.display_name || attr.labels?.en_US || attr.code,
          type: systemType,
          required: attr.required || false,
          default_mapping: "String",
          default_mapping_content: null,
          prompt: null,
          output_validation_column: null,
        }));
        
        // Replace all columns with the new external columns
        setAllTargetColumns(externalColumns);
        
        console.log(`✅ Loaded ${externalColumns.length} ${systemType} columns`);
      } else {
        console.warn(`${systemType} API returned unexpected format:`, result);
      }
    } catch (error) {
      console.warn(`Failed to load ${columnSystemType} columns:`, error);
      // Don't show error to user since external systems might be disabled
    }
  }, [columnSystemType]);

  // Load initial columns on mount
  useEffect(() => {
    loadInitialColumns();
  }, [loadInitialColumns]);

  // Ensure we have ERP columns available when system type is ERP
  useEffect(() => {
    if (columnSystemType === "navision" && allTargetColumns.length === 0 && !loading) {
      console.log('No columns loaded yet, loading sample ERP columns...');
      loadInitialColumns();
    }
  }, [columnSystemType, allTargetColumns.length, loading, loadInitialColumns]);

  // Load external columns when system type changes
  useEffect(() => {
    // Only load external columns if system type allows it
    if (columnSystemType !== "navision") {
      loadExternalColumns();
    }
  }, [columnSystemType, loadExternalColumns]); // Only depend on system type change

  const handleSystemTypeChange = (type: ColumnSystemType) => {
    setColumnSystemType(type);
    setSelectedTargetSystemType(type);
    // Clear existing columns when switching system type
    setAllTargetColumns([]);
    // Reset selection
    setSelectedTargetColumns([]);
  };

  const getSystemTypeIcon = (type: ColumnSystemType) => {
    switch (type) {
      case "navision":
        return <Database className="h-4 w-4" />;
      case "akeneo":
        return <Package className="h-4 w-4" />;
      case "ebay":
        return <Package className="h-4 w-4" />;
      case "amazon":
        return <Package className="h-4 w-4" />;
    }
  };

  const getSystemTypeLabel = (type: ColumnSystemType) => {
    switch (type) {
      case "navision":
        return "Navision";
      case "akeneo":
        return "Akeneo";
      case "ebay":
        return "eBay";
      case "amazon":
        return "Amazon";
    }
  };

  const getSystemTypeDescription = (type: ColumnSystemType) => {
    switch (type) {
      case "navision":
        return "Select columns from your Navision ERP system. These are typically business-focused columns like pricing, inventory, and financial data.";
      case "akeneo":
        return "Select columns from your Akeneo Product Information Management system. These are typically product-focused columns like descriptions, categories, and attributes.";
      case "ebay":
        return "Select columns for eBay import. These are typically eBay-specific columns like listing details, shipping information, and payment data.";
      case "amazon":
        return "Select columns for Amazon import. These are typically Amazon-specific columns like order details, inventory management, and customer data.";
    }
  };

  const erpColumnCount = allTargetColumns.filter(col => col.type === "Navisionvorlage").length;
  const pimColumnCount = allTargetColumns.filter(col => col.type === "Akeneo").length;

  // Handle auth success
  const handleAuthSuccess = useCallback(async () => {
    console.log('🔑 Authentication successful - reloading columns...');
    setShowAuthDialog(false);
    // Reset state and reload
    setAllTargetColumns([]);
    await unifiedConfig.refreshConfig();
    await loadInitialColumns();
  }, [unifiedConfig, loadInitialColumns]);

  // Debug logging
  console.log('Target Column Selection Debug:', {
    columnSystemType,
    allTargetColumnsCount: allTargetColumns.length,
    erpColumnCount,
    pimColumnCount,
    filteredColumnsCount: filteredColumns.length,
    selectedTargetColumnsCount: selectedTargetColumns.length,
    loading,
    authError: unifiedConfig.authError,
    retryCount: unifiedConfig.retryCount,
    showAuthDialog
  });

  return (
    <>
      <GoogleSheetsAuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        onAuthSuccess={handleAuthSuccess}
      />
      
      <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Select Target System</span>
          </CardTitle>
          <CardDescription>
            Choose which system columns you want to create. After selecting a system, click "Select All" to include all available columns.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* System Type Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {(["navision", "akeneo", "ebay", "amazon"] as ColumnSystemType[]).map((type) => {
              return (
                <Card
                  key={type}
                  className={`transition-all border-2 cursor-pointer ${
                    columnSystemType === type 
                      ? "border-blue-500 bg-blue-50" 
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => handleSystemTypeChange(type)}
                >
                <CardContent className="p-6 text-center">
                  <div className="flex flex-col items-center space-y-3">
                    <div className={`p-3 rounded-full ${
                      columnSystemType === type ? "bg-blue-100" : "bg-gray-100"
                    }`}>
                      {getSystemTypeIcon(type)}
                    </div>
                    <div>
                      <h3 className="font-semibold text-sm mb-1">
                        {getSystemTypeLabel(type)}
                      </h3>
                      <p className="text-xs mb-2 text-gray-600">
                        {getSystemTypeDescription(type)}
                      </p>
                      {(() => {
                        const typeColumnCount = allTargetColumns.filter(col => {
                          switch (type) {
                            case "navision":
                              return col.type === "Navisionvorlage";
                            case "akeneo":
                              return col.type === "Akeneo";
                            case "ebay":
                              return col.type === "eBay";
                            case "amazon":
                              return col.type === "Amazon";
                            default:
                              return false;
                          }
                        }).length;
                        
                        return typeColumnCount > 0 && (
                          <Badge variant="secondary" className="text-xs">
                            {typeColumnCount} columns
                          </Badge>
                        );
                      })()}
                    </div>
                    {columnSystemType === type && (
                      <CheckCircle className="h-5 w-5 text-blue-600" />
                    )}
                  </div>
                </CardContent>
              </Card>
              );
            })}
          </div>

          {/* Select All Columns Button */}
          {filteredColumns.length > 0 && !loading && (
            <div className="flex flex-col items-center space-y-3">
              <Button 
                onClick={() => {
                  const columnNames = filteredColumns.map(col => col.column_name);
                  
                  // Determine mapping types based on configuration
                  const mappingTypes: Record<string, MappingType> = {};
                  filteredColumns.forEach(col => {
                    mappingTypes[col.column_name] = getMappingTypeFromConfig(col.default_mapping);
                  });
                  
                  console.log(`Selecting ${getSystemTypeLabel(columnSystemType)} columns with mapping types:`, columnNames, mappingTypes);
                  setSelectedTargetColumns(columnNames);
                  setColumnMappingTypes(mappingTypes);
                  markStepCompleted(2);
                  
                  const aiTransformCount = Object.values(mappingTypes).filter(type => type === 'ai-transform').length;
                  const defaultCount = Object.values(mappingTypes).filter(type => type === 'default').length;
                  
                  toast.success(`Selected ${columnNames.length} ${getSystemTypeLabel(columnSystemType)} columns (${aiTransformCount} for AI Transform, ${defaultCount} with defaults)`);
                }}
                className="flex items-center space-x-2"
                size="lg"
              >
                <CheckCircle className="h-4 w-4" />
                <span>Select All {filteredColumns.length} {getSystemTypeLabel(columnSystemType)} Columns</span>
              </Button>
              
              {/* Show selected count */}
              {selectedTargetColumns.length > 0 && (
                <div className="text-sm text-green-600 font-medium">
                  ✓ {selectedTargetColumns.length} columns selected
                </div>
              )}
            </div>
          )}

          {/* Show message when no columns are selected */}
          {filteredColumns.length > 0 && selectedTargetColumns.length === 0 && !loading && (
            <div className="text-center py-4">
              <div className="text-sm text-orange-600 bg-orange-50 border border-orange-200 rounded-lg p-3">
                <div className="font-medium">Please select columns to continue</div>
                <div className="text-xs mt-1">Click "Select All {getSystemTypeLabel(columnSystemType)} Columns" above to proceed to the next step</div>
              </div>
            </div>
          )}

          {/* Debug information for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded border">
              Debug: System={columnSystemType}, Total={allTargetColumns.length}, ERP={erpColumnCount}, 
              Filtered={filteredColumns.length}, Selected={selectedTargetColumns.length}, Loading={loading.toString()}
            </div>
          )}

          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading columns...</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
    </>
  );
} 