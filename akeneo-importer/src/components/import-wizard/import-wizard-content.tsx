"use client";

import { useState, useEffect } from "react";
import { use<PERSON>ear<PERSON><PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { 
  Upload, 
  FileSpreadsheet, 
  Settings,
  Brain,
  Zap, 
  Download,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Edit,
  RefreshCw,
  AlertTriangle
} from "lucide-react";
import { FileUploadStep } from "./steps/file-upload-step";
import { TargetColumnSelectionStep } from "./steps/target-column-selection-step";
import { EnhancedColumnMappingStep } from "./steps/enhanced-column-mapping-step";
import { AiTransformMappingStep } from "./steps/ai-transform-mapping-step";
import { DataTransformationStep } from "./steps/data-transformation-step";
import { DataPreviewStep } from "./steps/data-preview-step";
import { useImportWizardStore } from "@/stores/import-wizard-store";

// Helper function to get step key names
function getStepKey(step: number): string {
  const stepKeys: Record<number, string> = {
    1: 'fileUpload',
    2: 'targetColumns', 
    3: 'oneToOneMappings',
    4: 'aiTransform',
    5: 'dataPreview'
  };
  return stepKeys[step] || 'unknown';
}

const WIZARD_STEPS = [
  {
    id: 1,
    title: "File Upload",
    description: "Upload your data file or connect to Google Sheets",
    icon: Upload,
  },
  {
    id: 2,
    title: "Select Target Columns",
    description: "Choose which columns to create in your target system",
    icon: Settings,
  },
  {
    id: 3,
    title: "One-to-One Mappings",
    description: "Map source columns directly to target columns",
    icon: Brain,
  },
  {
    id: 4,
    title: "AI Transform Mapping",
    description: "Apply AI transformation to columns",
    icon: Zap,
  },
  {
    id: 5,
    title: "Data Preview & Transform",
    description: "Preview data and run transformations with progress tracking",
    icon: FileSpreadsheet,
  },
];

const STEP_NAMES: Record<number, string> = {
  1: "File Upload",
  2: "Target Column Selection", 
  3: "One-to-One Mappings",
  4: "AI Transform Mapping",
  5: "Data Preview & Transform"
};

export function ImportWizardContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { 
    currentStep, 
    jobId, 
    jobName,
    selectedTargetColumns,
    setCurrentStep,
    setJobId,
    setJobName,
    reset,
    markStepCompleted,
    isStepCompleted,
    saveStepSnapshot,
    getConflictingSteps,
    initializeFromUrl,
    syncStepWithUrl
  } = useImportWizardStore();
  
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [showConflictDialog, setShowConflictDialog] = useState(false);
  const [newJobName, setNewJobName] = useState(jobName || '');
  const [pendingStepNavigation, setPendingStepNavigation] = useState<number | null>(null);
  const [conflictingSteps, setConflictingSteps] = useState<number[]>([]);

  // Handle job_id and step from URL parameters
  useEffect(() => {
    const jobIdFromUrl = searchParams.get('job_id');
    const stepFromUrl = searchParams.get('step');
    const stepNumber = stepFromUrl ? parseInt(stepFromUrl, 10) : null;
    
    console.log('Import wizard: URL params - job_id:', jobIdFromUrl, 'step:', stepNumber);
    
    // Initialize store from URL parameters
    initializeFromUrl(jobIdFromUrl, stepNumber);
    
    if (jobIdFromUrl && jobIdFromUrl !== jobId) {
      const store = useImportWizardStore.getState();
      store.setJobId(jobIdFromUrl);
      
      console.log('Import wizard: Loading job details for ID:', jobIdFromUrl);
      
      // Fetch job details to get job name and check if data has been uploaded
      fetch(`/api/jobs/${jobIdFromUrl}`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          console.log('Import wizard: Job details response:', data);
          
          // Handle API response format
          let jobData = data;
          if (data.success && data.job) {
            jobData = data.job;
          }
          
          if (jobData.name) {
            console.log('Import wizard: Setting job name to:', jobData.name);
            store.setJobName(jobData.name);
          }
          
          // If no step in URL and job has data, suggest step 2
          if (!stepNumber && jobData.row_count > 0 && currentStep === 1) {
            console.log('Import wizard: Job has data, suggesting step 2');
            // Don't auto-navigate, just mark step 1 as completed
            store.markStepCompleted(1);
          }
        })
        .catch(error => {
          console.error('Error fetching job details:', error);
          // If job not found, reset the wizard state but don't reset the entire store
          if (error.message.includes('404')) {
            console.log('Import wizard: Job not found, staying on step 1');
            store.setJobName(null);
            if (!stepNumber) {
              store.setCurrentStep(1, false); // Don't update URL
            }
          }
        });
    }
  }, [searchParams]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      const url = new URL(window.location.href);
      const stepFromUrl = url.searchParams.get('step');
      const stepNumber = stepFromUrl ? parseInt(stepFromUrl, 10) : 1;
      
      if (stepNumber !== currentStep) {
        console.log('Import wizard: Browser navigation detected, step:', stepNumber);
        // Use conflict resolution for browser navigation too
        handleStepNavigation(stepNumber);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [currentStep]);

  const canGoNext = () => {
    switch (currentStep) {
      case 1:
        return jobId !== null;
      case 2:
        return selectedTargetColumns.length > 0;
      case 3: // One-to-One Mappings
      case 4: // AI Transform Mapping
      case 5: // Data Preview & Transform
        return true; // These steps are optional or handle their own validation
      default:
        return false;
    }
  };

  const canGoPrevious = () => {
    return currentStep > 1;
  };

  const handleNext = () => {
    const store = useImportWizardStore.getState();
    
    if (canGoNext() && currentStep < 5) {
      // Save snapshot of current step before moving
      saveCurrentStepSnapshot();
      markStepCompleted(currentStep);
      
      // Special handling for step 2 (target column selection)
      if (currentStep === 2) {
        const nextStep = store.getNextStepAfterTargetSelection();
        setCurrentStep(nextStep);
      } else {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevious = () => {
    if (canGoPrevious()) {
      handleStepNavigation(currentStep - 1);
    }
  };

  const handleStepNavigation = (targetStep: number) => {
    // Check for conflicts
    const conflicts = getConflictingSteps(targetStep);
    
    if (conflicts.length > 0 && targetStep < currentStep) {
      // Show conflict resolution dialog
      setConflictingSteps(conflicts);
      setPendingStepNavigation(targetStep);
      setShowConflictDialog(true);
    } else {
      // No conflicts, navigate directly
      navigateToStep(targetStep);
    }
  };

  const navigateToStep = (stepId: number) => {
    if (stepId >= 1 && stepId <= WIZARD_STEPS.length) {
      saveCurrentStepSnapshot();
      setCurrentStep(stepId);
    }
  };

  const handleConflictResolution = (overrideProgress: boolean) => {
    setShowConflictDialog(false);
    
    if (overrideProgress && pendingStepNavigation) {
      // Clear conflicting step completions and snapshots
      const store = useImportWizardStore.getState();
      const newStepCompletionState = { ...store.stepCompletionState };
      const newSnapshots = [...store.stepStateSnapshots];
      
      conflictingSteps.forEach(step => {
        const stepKey = `step${step}_${getStepKey(step)}` as keyof typeof newStepCompletionState;
        newStepCompletionState[stepKey] = false;
        // Remove snapshots for conflicting steps
        const snapshotIndex = newSnapshots.findIndex(s => s.step === step);
        if (snapshotIndex >= 0) {
          newSnapshots.splice(snapshotIndex, 1);
        }
      });
      
      store.reset();
      store.initializeFromUrl(jobId, null);
      navigateToStep(pendingStepNavigation);
    }
    
    setPendingStepNavigation(null);
    setConflictingSteps([]);
  };

  const saveCurrentStepSnapshot = () => {
    const store = useImportWizardStore.getState();
    const stepData: Record<string, any> = {};
    
    switch (currentStep) {
      case 1:
        stepData.jobId = store.jobId;
        stepData.jobName = store.jobName;
        stepData.sourceColumns = store.sourceColumns;
        stepData.sourceData = store.sourceData;
        break;
      case 2:
        stepData.selectedTargetColumns = store.selectedTargetColumns;
        stepData.mappingDefinitions = store.mappingDefinitions;
        break;
      case 3: // AI Transform Mapping (renumbered from 4)
        stepData.aiTransformMappings = store.aiTransformMappings;
        break;
      case 4: // Data Preview & Transform (renumbered from 5)
        stepData.columnMappings = store.columnMappings;
        stepData.validationConfigs = store.validationConfigs;
        break;
    }
    
    saveStepSnapshot(currentStep, stepData);
  };

  const handleReset = () => {
    setShowResetDialog(false);
    reset();
    // Clear URL parameters
    router.push('/import-wizard');
  };

  const handleConfirmReset = () => {
    setShowResetDialog(true);
  };

  const handleStepClick = (stepId: number) => {
    // Allow navigation to any step that's been reached before or the next logical step
    if (stepId >= 1 && stepId <= WIZARD_STEPS.length) {
      // For step 1, always allow navigation
      if (stepId === 1) {
        handleStepNavigation(stepId);
        return;
      }
      
      // For step 2 and beyond, check if we have the necessary data
      switch (stepId) {
        case 2:
          // Can go to step 2 if we have a job ID
          if (jobId) {
            handleStepNavigation(stepId);
          }
          break;
        case 3: // AI Transform Mapping (renumbered from 4)
        case 4: // Data Preview & Transform (renumbered from 5)
          // Can go to these steps if we have target columns selected or if we're already past step 2
          if (selectedTargetColumns.length > 0 || currentStep >= 2) {
            handleStepNavigation(stepId);
          }
          break;
        default:
          handleStepNavigation(stepId);
      }
    }
  };

  const handleRename = async () => {
    if (!newJobName.trim() || !jobId) return;
    
    try {
      const response = await fetch(`/api/jobs/${jobId}/rename`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newJobName.trim() }),
      });

      if (response.ok) {
        setJobName(newJobName.trim());
        setShowRenameDialog(false);
      } else {
        console.error('Failed to rename job');
      }
    } catch (error) {
      console.error('Error renaming job:', error);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <FileUploadStep />;
      case 2:
        return <TargetColumnSelectionStep />;
      case 3:
        return (
          <Card className="p-8 text-center">
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <Brain className="h-16 w-16 mx-auto text-gray-400" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    One-to-One Column Mapping
                  </h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    This feature will allow you to manually map source columns directly to target columns without AI transformation.
                  </p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg max-w-sm mx-auto">
                  <p className="text-sm text-blue-800 font-medium">
                    🚧 Feature coming soon
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    This functionality is currently under development and will be available in a future update.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 4:
        return <AiTransformMappingStep />;
      case 5:
        return <DataPreviewStep />;
      default:
        return <FileUploadStep />;
    }
  };

  const getStepStatus = (stepId: number) => {
    if (isStepCompleted(stepId)) return "completed";
    if (stepId === currentStep) return "current";
    return "upcoming";
  };

  return (
    <div className="w-full space-y-8">
      {/* Combined Header and Progress */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {jobName && (
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">{jobName}</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => {
                      setNewJobName(jobName);
                      setShowRenameDialog(true);
                    }}
                    className="p-1"
                    title="Rename Job"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleConfirmReset}
                    className="p-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                    title="Reset Wizard"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold">Step {currentStep} of {WIZARD_STEPS.length}</div>
              <div className="text-sm text-gray-500">
                {WIZARD_STEPS.find(step => step.id === currentStep)?.description}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* Progress Bar */}
            <Progress value={(currentStep / WIZARD_STEPS.length) * 100} className="w-full" />
            
            {/* Step Indicators */}
            <div className="flex items-center justify-between">
              {WIZARD_STEPS.map((step, index) => {
                const status = getStepStatus(step.id);
                const StepIcon = step.icon;
                
                // Determine if step is clickable based on navigation logic
                const isClickable = (() => {
                  if (step.id === 1) return true; // Always allow going back to start
                  if (step.id === 2) return !!jobId; // Step 2 requires job ID
                  if (step.id >= 3) return selectedTargetColumns.length > 0 || currentStep >= 2; // Later steps require target columns or current progress
                  return false;
                })();
                
                return (
                  <div
                    key={step.id}
                    className={`flex flex-col items-center space-y-2 ${
                      index < WIZARD_STEPS.length - 1 ? 'flex-1' : ''
                    }`}
                  >
                    <div
                      onClick={() => handleStepClick(step.id)}
                      title={
                        isClickable 
                          ? `Go to ${step.title}` 
                          : step.id === 2 
                            ? "Complete file upload first"
                            : "Complete previous steps first"
                      }
                      className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all ${
                        isClickable ? 'cursor-pointer hover:scale-105 hover:shadow-md' : 'cursor-not-allowed opacity-60'
                      } ${
                        status === "completed"
                          ? "bg-green-100 border-green-600 " + (isClickable ? "hover:bg-green-200" : "")
                          : status === "current"
                          ? "bg-blue-100 border-blue-600 " + (isClickable ? "hover:bg-blue-200" : "")
                          : "bg-gray-100 border-gray-300 " + (isClickable ? "hover:bg-gray-200" : "")
                      }`}
                    >
                      {status === "completed" ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <StepIcon
                          className={`h-5 w-5 ${
                            status === "current" ? "text-blue-600" : "text-gray-400"
                          }`}
                        />
                      )}
                    </div>
                    <div className="text-center">
                      <div
                        onClick={() => handleStepClick(step.id)}
                        className={`text-sm font-medium transition-colors ${
                          isClickable ? 'cursor-pointer hover:underline' : 'cursor-not-allowed'
                        } ${
                          status === "current" ? "text-blue-600" : "text-gray-500"
                        }`}
                      >
                        {step.title}
                      </div>
                      <Badge
                        variant={
                          status === "completed"
                            ? "default"
                            : status === "current"
                            ? "secondary"
                            : "outline"
                        }
                        className="text-xs mt-1"
                      >
                        {status === "completed" ? "Done" : status === "current" ? "Active" : "Pending"}
                      </Badge>
                    </div>
                    {index < WIZARD_STEPS.length - 1 && (
                      <div
                        className={`hidden md:block h-0.5 w-full ${
                          status === "completed" ? "bg-green-600" : "bg-gray-300"
                        }`}
                        style={{ 
                          position: 'absolute', 
                          top: '20px', 
                          left: '50%', 
                          right: '-50%', 
                          width: `calc(100% / ${WIZARD_STEPS.length} - 40px)`,
                          marginLeft: '20px'
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {renderStepContent()}

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={!canGoPrevious()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Previous</span>
        </Button>
        
        <Button
          onClick={handleNext}
          disabled={!canGoNext()}
          className="flex items-center space-x-2"
        >
          <span>
            {currentStep === WIZARD_STEPS.length ? 'Finish' : 'Next'}
          </span>
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Rename Dialog */}
      <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Import Job</DialogTitle>
            <DialogDescription>
              Enter a new name for this import job.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="jobName">Job Name</Label>
              <Input
                id="jobName"
                value={newJobName}
                onChange={(e) => setNewJobName(e.target.value)}
                placeholder="Enter job name"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRenameDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleRename}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Confirmation Dialog */}
      <Dialog open={showResetDialog} onOpenChange={setShowResetDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Import Wizard</DialogTitle>
            <DialogDescription>
              Are you sure you want to reset the wizard? This will clear all progress and return you to the beginning.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowResetDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleReset}>
              Reset Wizard
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Conflict Resolution Dialog */}
      <Dialog open={showConflictDialog} onOpenChange={setShowConflictDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              <span>Progress Conflict</span>
            </DialogTitle>
            <DialogDescription>
              Going back to step {pendingStepNavigation ? STEP_NAMES[pendingStepNavigation] : ''} will override your progress in the following steps:
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <h4 className="font-medium text-amber-800 mb-2">Steps that will be reset:</h4>
              <ul className="space-y-1">
                {conflictingSteps.map((step) => (
                  <li key={step} className="flex items-center space-x-2 text-sm text-amber-700">
                    <CheckCircle className="h-4 w-4" />
                    <span>{STEP_NAMES[step]}</span>
                  </li>
                ))}
              </ul>
            </div>
            <p className="text-sm text-gray-600">
              Do you want to proceed and lose your progress in these steps?
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => handleConflictResolution(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => handleConflictResolution(true)}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Reset Progress</span>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 