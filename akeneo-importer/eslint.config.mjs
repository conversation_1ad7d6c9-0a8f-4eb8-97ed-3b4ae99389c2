import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Disable unused variable warnings
      "@typescript-eslint/no-unused-vars": "off",
      "no-unused-vars": "off",
      
      // Disable any type warnings
      "@typescript-eslint/no-explicit-any": "off",
      
      // Disable empty function warnings
      "@typescript-eslint/no-empty-function": "off",
      
      // Disable non-null assertion warnings
      "@typescript-eslint/no-non-null-assertion": "off",
      
      // Disable require await warnings
      "@typescript-eslint/require-await": "off",
      
      // Disable no-var-requires for dynamic imports
      "@typescript-eslint/no-var-requires": "off",
      
      // Disable prefer-const warnings
      "prefer-const": "off",
      
      // Disable console warnings
      "no-console": "off",
      
      // Disable React hooks exhaustive deps
      "react-hooks/exhaustive-deps": "off",
      
      // Disable React display name requirement
      "react/display-name": "off",
      
      // Disable React prop types requirement
      "react/prop-types": "off",
      
      // Disable Next.js image optimization warnings
      "@next/next/no-img-element": "off",
      
      // Disable HTML validation warnings
      "@next/next/no-html-link-for-pages": "off",
      
      // Disable React unescaped entities (quotes, apostrophes)
      "react/no-unescaped-entities": "off",
      
      // Disable TypeScript require imports warnings
      "@typescript-eslint/no-require-imports": "off",
      
      // Disable TypeScript ban ts-comment warnings
      "@typescript-eslint/ban-ts-comment": "off",
      
      // Disable empty object type warnings
      "@typescript-eslint/no-empty-object-type": "off",
    }
  }
];

export default eslintConfig;
