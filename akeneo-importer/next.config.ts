/** @type {import('next').NextConfig} */
const nextConfig = {
  // ESLint rules are configured in eslint.config.mjs
  // Enable standalone output for Docker production builds
  output: 'standalone',
  // Optimize for production
  serverExternalPackages: ['ioredis'],
  // Image optimization for production
  images: {
    unoptimized: process.env.NODE_ENV === 'production',
  },
};

export default nextConfig;
