FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for better compatibility
RUN apk add --no-cache libc6-compat

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Create .next directory and set permissions
RUN mkdir -p .next && chown -R node:node .next
RUN chown -R node:node /app

# Switch to node user for security
USER node

# Expose port
EXPOSE 3000

# Set environment variables for development
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true
ENV WATCHPACK_POLLING=true

# Start server (can be overridden with docker-compose command)
CMD ["npm", "start"] 