{"name": "akeneo-importer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.80.7", "ag-grid-community": "^33.3.2", "ag-grid-react": "^33.3.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "ioredis": "^5.6.1", "lucide-react": "^0.515.0", "next": "15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "openai": "^5.3.0", "papaparse": "^5.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.7", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "dotenv": "^16.6.0", "eslint": "^9", "eslint-config-next": "15.3.3", "puppeteer": "^24.10.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typescript": "^5"}}