# Akeneo Importer

A modern, AI-powered product data import wizard for Akeneo PIM built with Next.js 14, TypeScript, and shadcn/ui.

## 🚀 Features

- **AI-Powered Column Mapping**: Automatically map CSV/Excel columns to Akeneo attributes using LLM
- **Multi-Format Support**: Import from CSV, Excel files, or Google Sheets
- **Real-time Processing**: Stream large datasets with progress tracking
- **Smart Data Transformation**: Apply business logic with natural language instructions
- **Akeneo Integration**: Seamless integration with Akeneo PIM API
- **Performance Optimized**: Memory-efficient processing with Redis caching
- **Modern UI**: Beautiful, responsive interface built with shadcn/ui

## 🛠️ Tech Stack

- **Frontend**: Next.js 14 (App Router), TypeScript, Tailwind CSS
- **UI Components**: shadcn/ui, Lucide Icons
- **State Management**: React Query, Zustand
- **Authentication**: NextAuth.js
- **Database**: Redis (multi-database architecture)
- **File Processing**: <PERSON> (CSV), SheetJS (Excel)
- **AI Integration**: OpenRouter API
- **External APIs**: Akeneo PIM, Google Sheets

## 📋 Prerequisites

- Node.js 18+ 
- Redis server
- Akeneo PIM instance
- OpenRouter API key (for AI features)
- Google Sheets API credentials (optional)

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd akeneo-importer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   
   Copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```
   
   Configure your environment variables in `.env.local`:
   ```bash
   # Authentication Configuration
   AUTH_USERNAME=your_username
   AUTH_PASSWORD=your_password
   NEXTAUTH_SECRET=your_nextauth_secret_here
   NEXTAUTH_URL=http://localhost:3000

   # Redis Configuration
   REDIS_URL=redis://localhost:6379

   # Akeneo API Configuration
   AKENEO_ENDPOINT=https://your-akeneo-instance.com/
   AKENEO_CLIENT_ID=your_client_id
   AKENEO_CLIENT_SECRET=your_client_secret
   AKENEO_USERNAME=your_username
   AKENEO_PASSWORD=your_password

   # OpenRouter LLM Configuration
   OPENROUTER_API_KEY=your_openrouter_api_key

   # Google Sheets Configuration (Optional)
   GSHEET_SECRET={"web":{"client_id":"...","client_secret":"..."}}
   GOOGLE_MAPPING_SHEET_ID=your_google_sheet_id
   GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback
   ```

4. **Start Redis Server**
   ```bash
   # Using Docker
   docker run -d -p 6379:6379 redis:alpine

   # Or using local Redis installation
   redis-server
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔑 Authentication

The application uses basic authentication. Use the credentials you configured in your environment variables:

- **Username**: Value from `AUTH_USERNAME`
- **Password**: Value from `AUTH_PASSWORD`

## 🏗️ Architecture

### Redis Database Structure

The application uses 4 separate Redis databases:

- **DB 0**: Jobs & Data (job metadata, paginated data storage)
- **DB 1**: LLM Cache (cached AI responses with 7-day TTL)
- **DB 2**: Sessions (user session data)
- **DB 3**: Background Tasks (job processing queue)

### File Processing Pipeline

1. **Upload**: Files are processed and validated
2. **Parsing**: CSV/Excel data is normalized and cleaned
3. **Storage**: Data is stored in Redis with pagination (20 rows per page)
4. **Mapping**: AI suggests column mappings to Akeneo attributes
5. **Transformation**: Apply business logic and data transformations
6. **Export**: Generate Excel/CSV or push to Akeneo

### Performance Features

- **Memory Management**: Efficient pagination and cleanup
- **Caching**: Multi-layer caching strategy
- **Virtual Scrolling**: Handle large datasets in UI
- **Request Deduplication**: Prevent duplicate API calls
- **Background Processing**: Queue system for long-running tasks

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── import-wizard/     # Import wizard pages
│   ├── jobs/              # Job management
│   └── settings/          # Configuration pages
├── components/            # React components
│   ├── ui/                # shadcn components
│   ├── auth/              # Authentication components
│   ├── layout/            # Layout components
│   └── providers/         # Context providers
├── lib/                   # Utilities and configurations
│   ├── redis/             # Redis clients and managers
│   ├── auth/              # Authentication config
│   ├── file-processing/   # File processing utilities
│   ├── akeneo/            # Akeneo API client
│   ├── llm/               # LLM integration
│   └── google-sheets/     # Google Sheets service
└── types/                 # TypeScript definitions
```

## 🚀 Usage

### Starting a New Import

1. **Navigate** to the Import Wizard
2. **Upload** your CSV/Excel file or connect Google Sheets
3. **Review** detected columns and data preview
4. **Map** columns using AI suggestions or manual selection
5. **Transform** data using natural language instructions
6. **Export** to Excel or push directly to Akeneo

### Managing Jobs

- **View** all import jobs in the Jobs section
- **Monitor** progress of running imports
- **Download** processed data in various formats
- **Retry** failed imports with error details

### Akeneo Integration

- **Search** existing products in your Akeneo instance
- **Browse** families, attributes, and categories
- **Export** search results for analysis

## 🔧 Configuration

### LLM Settings

Configure AI models and prompts in the Settings section:
- Choose from available OpenRouter models
- Adjust temperature and token limits
- Customize system prompts for better results

### Supplier Configuration

Set up supplier-specific attribute mappings:
- Define which attributes suppliers should manage
- Configure family-based attribute responsibilities
- Import configurations from Google Sheets

## LLM Configuration

### Default Model: qwen/qwen3-32b

The system now uses **qwen/qwen3-32b** as the standard model, which provides:

- **Advanced Reasoning**: Built-in reasoning capabilities with step-by-step analysis
- **Structured Output**: Native support for JSON schema and structured responses  
- **Response Format Control**: Supports `response_format` parameter for consistent output formatting
- **High Performance**: 32B parameter model optimized for complex data transformations

#### Supported Response Formats

1. **JSON Schema**: `{ "type": "json_schema", "json_schema": {...} }`
   - Ensures strict adherence to your JSON schema
   - Preferred for models that support it (including qwen/qwen3-32b)

2. **JSON Object**: `{ "type": "json_object" }`  
   - Ensures valid JSON output
   - Fallback for older models

3. **Reasoning Format**: Built-in reasoning capabilities
   - `reasoning_format: "parsed"` - Shows structured reasoning
   - `reasoning_format: "hidden"` - Hides reasoning process
   - `reasoning_format: "raw"` - Shows unstructured reasoning

### Environment Variables

```bash
# Primary model configuration
DEFAULT_MODEL=qwen/qwen3-32b
GROQ_DEFAULT_MODEL=qwen/qwen3-32b
LLM_PROVIDER=groq

# API Keys (choose your provider)
GROQ_API_KEY=your_groq_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
```

### Model Capabilities

The system automatically detects and uses advanced capabilities:

```typescript
// Qwen 3 32B capabilities
{
  supportsJsonSchema: true,     // ✅ Full JSON schema support
  supportsJsonObject: true,     // ✅ JSON object formatting
  supportsReasoningFormat: true // ✅ Built-in reasoning
}
```

## 🧪 Testing

```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run e2e tests
npm run test:e2e
```

## 📦 Deployment

### Docker Deployment

1. **Build the Docker image**
   ```bash
   docker build -t akeneo-importer .
   ```

2. **Run with Docker Compose**
   ```yaml
   version: '3.8'
   services:
     app:
       build: .
       ports:
         - "3000:3000"
       environment:
         - REDIS_URL=redis://redis:6379
         - NEXTAUTH_SECRET=your_secret
       depends_on:
         - redis
     
     redis:
       image: redis:alpine
       ports:
         - "6379:6379"
   ```

### Production Environment

- Set `NODE_ENV=production`
- Configure proper `NEXTAUTH_SECRET`
- Use a production Redis instance
- Set up proper monitoring and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review existing issues
- Create a new issue with detailed information

## 🔮 Roadmap

- [ ] Advanced AI transformations
- [ ] Real-time collaboration features
- [ ] Advanced analytics and reporting
- [ ] Multi-tenant support
- [ ] API webhook integrations
- [ ] Advanced validation rules
- [ ] Bulk operations dashboard

## LLM Logging System

The application features a sophisticated logging system that reduces terminal spam while providing detailed logs for debugging:

### Terminal Output
- **Clean Interface**: Instead of verbose prompts, you'll see simple messages like:
  ```
  🔄 [A1B2C3D4] Starting LLM call to GROQ (qwen/qwen3-32b)
  🤖 [A1B2C3D4] LLM call to GROQ (1,234ms)
  💾 [A1B2C3D4] LLM call to GROQ (45ms)  # Cache hit
  ```
- **Unique IDs**: Each LLM call gets a unique 8-character ID for easy tracking

### Detailed File Logs
- **Location**: All detailed logs are stored in `/logs/llm-calls-YYYY-MM-DD.jsonl`
- **Content**: Full system prompts, user prompts, responses, reasoning, metadata, and timing
- **Format**: JSONL (JSON Lines) for easy parsing and analysis

### Inspecting Logs

#### Command Line Tool
```bash
# View recent LLM calls
cd akeneo-importer/scripts
node inspect-llm-logs.js --recent 10

# View detailed logs for a specific call ID
node inspect-llm-logs.js A1B2C3D4

# Show help
node inspect-llm-logs.js --help
```

#### API Endpoint
```bash
# Get recent calls
curl "http://localhost:3000/api/debug/llm-logs?limit=20"

# Get logs for specific call ID
curl "http://localhost:3000/api/debug/llm-logs?callId=A1B2C3D4"
```

#### Manual Log Inspection
Logs are stored as JSONL files in the `/logs` directory:
```bash
# View today's LLM logs
tail -f logs/llm-calls-2024-01-15.jsonl

# Search for specific call ID
grep "A1B2C3D4" logs/llm-calls-*.jsonl
```

### Log Structure
Each log entry includes:
- **id**: Unique 8-character identifier
- **timestamp**: ISO timestamp
- **model**: LLM model used
- **provider**: LLM provider (groq, openrouter)
- **messages**: Full conversation array
- **response**: LLM response
- **reasoning**: LLM reasoning (if available)
- **fromCache**: Whether result came from cache
- **duration**: Processing time in milliseconds
- **error**: Error details (if any)
- **metadata**: Additional context

## Development

### Running the Application
```bash
# Start all services
./dev.sh full

# Or start individual components
npm run dev        # Next.js app only
docker-compose up  # All services
```

### Environment Variables
```bash
# Copy example environment file
cp .env.example .env.local

# Required variables
GROQ_API_KEY=your_groq_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
REDIS_URL=redis://localhost:6379
```

## Architecture

- **Frontend**: Next.js with TypeScript
- **Backend**: Node.js API routes
- **Database**: Redis for caching and data storage
- **LLM Integration**: Groq and OpenRouter APIs
- **File Processing**: Support for Excel, CSV, Google Sheets
- **Logging**: File-based logging with unique ID tracking

## License

[License information here]
