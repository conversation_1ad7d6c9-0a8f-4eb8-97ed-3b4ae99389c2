version: '3.8'

services:
  # Next.js Application
  app:
    build:
      context: ./akeneo-importer
      dockerfile: Dockerfile.prod
    environment:
      # Redis connection using service discovery
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      # Production environment
      - NODE_ENV=production
      # LLM Cache Service configuration
      - LLM_CACHE_SERVICE_URL=${LLM_CACHE_SERVICE_URL:-}
      - LLM_CACHE_AUTH_TOKEN=${LLM_CACHE_AUTH_TOKEN:-}
      # Coolify magic environment variables for domain generation
      - SERVICE_FQDN_APP=
    labels:
      # Coolify will automatically add these, but explicit for clarity
      - coolify.managed=true
      - coolify.type=application
      # Traefik labels for reverse proxy and domain routing
      - traefik.enable=true
      - traefik.http.routers.pim-tool-app.rule=Host(`${SERVICE_FQDN_APP}`)
      - traefik.http.routers.pim-tool-app.entryPoints=http,https
      - traefik.http.routers.pim-tool-app.tls=true
      - traefik.http.services.pim-tool-app.loadbalancer.server.port=3000
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Database
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
      - type: bind
        source: ./redis.conf
        target: /etc/redis/redis.conf
        read_only: true
    command: redis-server /etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - coolify.managed=true
      - coolify.type=service
    # Internal service - no external ports needed
    expose:
      - "6379"

  # Redis Commander (Optional monitoring tool)
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=redis:redis:6379
      - HTTP_USER=admin
      # Use Coolify's password generation
      - HTTP_PASSWORD=${SERVICE_PASSWORD_REDIS_ADMIN}
    labels:
      - coolify.managed=true
      - coolify.type=service
      - coolify.exclude_from_hc=true
      - traefik.enable=true
      # Mount Redis Commander under /redis-admin path
      - traefik.http.routers.pim-tool-redis.rule=Host(`${SERVICE_FQDN_APP}`) && PathPrefix(`/redis-admin`)
      - traefik.http.routers.pim-tool-redis.entryPoints=http,https
      - traefik.http.routers.pim-tool-redis.tls=true
      - traefik.http.services.pim-tool-redis.loadbalancer.server.port=8081
      # Strip /redis-admin prefix when forwarding to container
      - traefik.http.middlewares.pim-tool-redis-strip.stripprefix.prefixes=/redis-admin
      - traefik.http.routers.pim-tool-redis.middlewares=pim-tool-redis-strip
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - tools  # Only deploy when tools profile is enabled

volumes:
  redis_data:
    driver: local

# Network will be automatically created by Coolify
# networks:
#   default:
#     name: coolify 