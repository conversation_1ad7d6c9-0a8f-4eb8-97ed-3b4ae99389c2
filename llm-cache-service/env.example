# LLM Cache Service Configuration

# Auth token for API access (change this in production!)
LLM_CACHE_AUTH_TOKEN=llm-cache-default-token-2024

# Server configuration
NODE_ENV=production
PORT=3001

# Redis configuration
REDIS_HOST=redis
REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password  # uncomment if Red<PERSON> has password

# Security settings
ALLOWED_ORIGINS=*  # Comma-separated list of allowed origins, or * for all
RATE_LIMIT=1000    # Requests per 15 minutes per IP

# Optional: Logging level
LOG_LEVEL=info 