version: '3.8'

services:
  llm-cache-service:
    build: .
    container_name: llm-cache-service
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - REDIS_HOST=redis
      - REDIS_PORT=6379

      - LLM_CACHE_AUTH_TOKEN=${LLM_CACHE_AUTH_TOKEN:-llm-cache-default-token-2024}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-*}
      - RATE_LIMIT=${RATE_LIMIT:-1000}
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - llm-cache-network
    healthcheck:
      test: ["CMD", "node", "-e", "const http = require('http'); const options = { host: 'localhost', port: 3001, path: '/api/health', timeout: 2000 }; const req = http.request(options, (res) => { process.exit(res.statusCode === 200 ? 0 : 1); }); req.on('error', () => { process.exit(1); }); req.end();"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: llm-cache-redis
    restart: unless-stopped
    ports:
      - "6380:6379"  # Different port to avoid conflicts with main app Redis
    volumes:
      - redis_data:/data
    networks:
      - llm-cache-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
    command: >
      redis-server
      --appendonly no
      --save ""
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru

networks:
  llm-cache-network:
    driver: bridge

volumes:
  redis_data: 