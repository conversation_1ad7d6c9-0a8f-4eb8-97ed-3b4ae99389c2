# LLM Cache Service

A standalone pure caching service for Large Language Model (LLM) responses using Redis.

## Features

- **Pure Cache**: Simple get/set/clear cache operations without LLM integration
- **Token Authentication**: Simple token-based API security  
- **Rate Limiting**: Built-in rate limiting to prevent abuse
- **Health Monitoring**: Health checks and detailed statistics
- **RESTful API**: Clean REST endpoints for cache operations
- **Persistent Storage**: Redis-based storage with configurable TTL

## Quick Start

1. **Copy environment file**:
   ```bash
   cp .env.example .env
   ```

2. **Configure authentication**:
   ```bash
   # Edit .env file and set your auth token
   LLM_CACHE_AUTH_TOKEN=your_secure_token_here
   ```

3. **Start the service**:
   ```bash
   docker-compose up -d
   ```

4. **Verify it's running**:
   ```bash
   curl http://localhost:3001/api/health
   ```

## API Endpoints

### Authentication
All API endpoints (except `/api/health`) require authentication via:
- **Header**: `Authorization: Bearer YOUR_TOKEN`
- **Or Header**: `X-Auth-Token: YOUR_TOKEN`

### Main Endpoints

#### GET `/api/cache/get/:key`
Retrieve cached data by key.

#### POST `/api/cache/set`
Store data in cache.

```json
{
  "key": "your-cache-key",
  "data": {"any": "data structure"},
  "ttl": 3600
}
```

#### GET `/api/stats`
Get cache statistics.

#### DELETE `/api/cache/clear`
Clear cache entries (supports pattern matching).

#### GET `/api/health`
Health check endpoint (no auth required).

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LLM_CACHE_AUTH_TOKEN` | Auth token for API access | `llm-cache-default-token-2024` |
| `PORT` | Service port | `3001` |
| `REDIS_HOST` | Redis host | `redis` |
| `REDIS_PORT` | Redis port | `6379` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `*` |
| `RATE_LIMIT` | Rate limit per 15 minutes | `1000` |

## Usage Examples

### Store Data in Cache
```bash
curl -X POST http://localhost:3001/api/cache/set \
  -H "Authorization: Bearer llm-cache-default-token-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "my-cache-key",
    "data": {"result": "some data"},
    "ttl": 3600
  }'
```

### Retrieve Data from Cache
```bash
curl http://localhost:3001/api/cache/get/my-cache-key \
  -H "Authorization: Bearer llm-cache-default-token-2024"
```

### Get Statistics
```bash
curl http://localhost:3001/api/stats \
  -H "Authorization: Bearer llm-cache-default-token-2024"
```

### Clear Cache
```bash
curl -X DELETE http://localhost:3001/api/cache/clear \
  -H "Authorization: Bearer llm-cache-default-token-2024"
```

## Architecture

```
┌─────────────────┐                 ┌─────────────────┐                 ┌─────────────────┐
│   Client App    │ ───────────────► │  Cache Service  │                 │   OpenRouter    │
│   (Next.js)     │  check cache    │   (Express)     │                 │      API        │
│                 │                 │                 │                 │                 │
│                 │ ◄─────────────── │                 │                 │                 │
│                 │  cache miss     │                 │                 │                 │
│                 │                 │                 │                 │                 │
│                 │ ───────────────────────────────────────────────────► │                 │
│                 │              direct LLM call                        │                 │
│                 │                 │                 │                 │                 │
│                 │ ◄─────────────────────────────────────────────────── │                 │
│                 │              LLM response                           │                 │
│                 │                 │                 │                 │                 │
│                 │ ───────────────► │                 │                 │                 │
│                 │  store result   │                 │                 │                 │
└─────────────────┘                 └─────────────────┘                 └─────────────────┘
                                            │
                                            ▼
                                    ┌─────────────────┐
                                    │     Redis       │
                                    │    (Cache)      │
                                    └─────────────────┘
```

## Cache Strategy

- **Cache Key**: SHA256 hash of `{messages, model, temperature, responseFormat}`
- **TTL**: 7 days (configurable)
- **Storage**: Redis with LRU eviction
- **Bypass**: `bypassCache: true` forces fresh LLM call but still stores result

## Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Run with Docker Compose
docker-compose up --build
```

## Security Notes

- Change the default `AUTH_TOKEN` in production
- Use HTTPS in production deployments
- Configure `ALLOWED_ORIGINS` to restrict CORS
- Monitor rate limits and adjust as needed
- Consider IP whitelisting for additional security

## Monitoring

The service provides comprehensive monitoring:
- Health checks at `/api/health`
- Detailed statistics at `/api/stats`
- Built-in logging for all operations
- Docker health checks included 