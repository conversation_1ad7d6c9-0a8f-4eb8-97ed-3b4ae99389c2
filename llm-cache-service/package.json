{"name": "llm-cache-service", "version": "1.0.0", "description": "Standalone LLM response caching service", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"express": "^4.18.2", "redis": "^4.6.5", "cors": "^2.8.5", "helmet": "^6.1.5", "express-rate-limit": "^6.7.0", "dotenv": "^16.0.3"}, "devDependencies": {"nodemon": "^2.0.22"}, "keywords": ["llm", "cache", "redis", "api"], "author": "Product Importer Team", "license": "MIT"}