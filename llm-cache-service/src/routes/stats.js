const express = require('express');
const router = express.Router();
const cacheManager = require('../cache/cache-manager');

// GET /api/stats - Get cache statistics
router.get('/', async (req, res) => {
  try {
    const stats = await cacheManager.getStats();
    
    res.json({
      success: true,
      stats: {
        ...stats,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get statistics',
      message: error.message
    });
  }
});

// GET /api/stats/detailed - Get detailed cache information
router.get('/detailed', async (req, res) => {
  try {
    const stats = await cacheManager.getStats();
    const health = await cacheManager.healthCheck();
    
    res.json({
      success: true,
      service: {
        name: 'LLM Cache Service',
        version: '1.0.0',
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
      },
      cache: stats,
      health,
      system: {
        memory: process.memoryUsage(),
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Detailed stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get detailed statistics',
      message: error.message
    });
  }
});

module.exports = router; 