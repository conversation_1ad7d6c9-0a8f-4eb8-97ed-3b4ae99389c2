const express = require('express');
const router = express.Router();
const cacheManager = require('../cache/cache-manager');

// GET /api/cache/get/:key - Get cached result
router.get('/get/:key', async (req, res) => {
  try {
    const { key } = req.params;
    
    if (!key) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'Cache key is required'
      });
    }

    const cached = await cacheManager.get(key);
    
    if (!cached) {
      return res.status(404).json({
        success: false,
        message: 'Cache entry not found',
        fromCache: false
      });
    }

    res.json({
      success: true,
      data: cached,
      fromCache: true,
      cacheKey: key.substring(0, 8) + '...'
    });
  } catch (error) {
    console.error('Cache get error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get cache entry',
      message: error.message
    });
  }
});

// POST /api/cache/set - Store data in cache
router.post('/set', async (req, res) => {
  try {
    const { key, data, ttl } = req.body;

    if (!key || !data) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'key and data are required'
      });
    }

    const stored = await cacheManager.store(key, data, ttl);
    
    if (!stored) {
      return res.status(500).json({
        success: false,
        error: 'Failed to store data in cache'
      });
    }

    res.json({
      success: true,
      message: 'Data stored in cache',
      cacheKey: key.substring(0, 8) + '...'
    });
  } catch (error) {
    console.error('Cache set error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to store data in cache',
      message: error.message
    });
  }
});

// DELETE /api/cache/clear - Clear cache
router.delete('/clear', async (req, res) => {
  try {
    const { pattern } = req.query;
    const clearedCount = await cacheManager.clearCache(pattern);
    
    res.json({
      success: true,
      message: `Cleared ${clearedCount} cache entries`,
      clearedCount
    });
  } catch (error) {
    console.error('Cache clear error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear cache',
      message: error.message
    });
  }
});

// GET /api/cache/history - Get cache history
router.get('/history', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    console.log(`📊 Fetching cache history with limit: ${limit}`);
    
    const history = await cacheManager.getCacheHistory(limit);
    console.log(`📊 Retrieved ${history.length} history entries`);
    
    res.json({
      success: true,
      history: history,
      totalEntries: history.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Cache history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get cache history',
      message: error.message
    });
  }
});

// POST /api/cache/populate-history - Populate initial history from existing cache
router.post('/populate-history', async (req, res) => {
  try {
    console.log('🔄 Starting cache history population...');
    const populatedCount = await cacheManager.populateInitialHistory();
    
    res.json({
      success: true,
      message: `Populated ${populatedCount} initial history entries`,
      populatedCount
    });
  } catch (error) {
    console.error('Cache history population error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to populate cache history',
      message: error.message
    });
  }
});

module.exports = router; 