const express = require('express');
const router = express.Router();
const cacheManager = require('../cache/cache-manager');

// GET /api/health - Basic health check (no auth required)
router.get('/', async (req, res) => {
  try {
    const health = await cacheManager.healthCheck();
    const isHealthy = health.redis === 'healthy';
    
    res.status(isHealthy ? 200 : 503).json({
      status: isHealthy ? 'healthy' : 'unhealthy',
      service: 'LLM Cache Service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks: {
        redis: health.redis
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      service: 'LLM Cache Service',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

module.exports = router; 