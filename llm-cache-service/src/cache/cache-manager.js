const redis = require('redis');
const crypto = require('crypto');

class CacheManager {
  constructor() {
    this.client = null;
    this.cacheTimeout = 7 * 24 * 3600; // 7 days in seconds
    this.historyLimit = 100; // Keep last 100 cache operations
    this.init();
  }

  async init() {
    try {
      // Initialize Redis client
      const redisUrl = `redis://${process.env.REDIS_HOST || 'redis'}:${process.env.REDIS_PORT || 6379}`;
      this.client = redis.createClient({
        url: redisUrl,
        password: process.env.REDIS_PASSWORD || undefined
      });

      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err);
      });

      this.client.on('connect', () => {
        console.log('✅ Connected to Redis');
      });

      await this.client.connect();
      console.log('✅ Cache Manager initialized');
    } catch (error) {
      console.error('Failed to initialize CacheManager:', error);
      throw error;
    }
  }

  generateCacheKey(messages, model, temperature = 0.1, responseFormat = 'json_object') {
    const content = JSON.stringify({ messages, model, temperature, responseFormat });
    const hash = crypto.createHash('sha256').update(content).digest('hex');
    
    // Enhanced logging for debugging cache collisions
    console.log(`🔑 Cache key generated: ${hash.substring(0, 8)}... for model ${model}`);
    console.log(`🔍 Cache key input:`, {
      model,
      temperature,
      responseFormat,
      messagesCount: messages.length,
      firstMessageLength: messages[0]?.content?.length || 0,
      lastMessageLength: messages[messages.length - 1]?.content?.length || 0,
      contentHash: crypto.createHash('md5').update(JSON.stringify(messages)).digest('hex').substring(0, 8)
    });
    
    return hash;
  }

  async get(cacheKey) {
    try {
      const cached = await this.client.get(`llm:${cacheKey}`);
      if (cached) {
        const parsedData = JSON.parse(cached);
        console.log(`✅ Cache hit for key: ${cacheKey.substring(0, 8)}...`);
        return parsedData;
      }
      return null;
    } catch (error) {
      console.error('Cache read error:', error);
      return null;
    }
  }

  async set(cacheKey, data) {
    try {
      await this.client.setEx(`llm:${cacheKey}`, this.cacheTimeout, JSON.stringify(data));
      
      // Store cache history
      await this.storeCacheHistory(cacheKey, data);
      
      console.log(`✅ Cache stored for key: ${cacheKey.substring(0, 8)}...`);
    } catch (error) {
      console.error('Cache write error:', error);
    }
  }

  async store(cacheKey, data, customTtl = null) {
    try {
      const ttl = customTtl || this.cacheTimeout;
      await this.client.setEx(`llm:${cacheKey}`, ttl, JSON.stringify(data));
      
      // Store cache history
      await this.storeCacheHistory(cacheKey, data);
      
      console.log(`✅ Cache stored for key: ${cacheKey.substring(0, 8)}...`);
      return true;
    } catch (error) {
      console.error('Cache write error:', error);
      return false;
    }
  }

  async storeCacheHistory(cacheKey, data) {
    try {
      console.log(`💾 Storing cache history for key: ${cacheKey.substring(0, 8)}...`);
      const timestamp = new Date().toISOString();
      const historyEntry = {
        cacheKey: cacheKey,
        timestamp: timestamp,
        request: data.request || null,
        response: data.response || data,
        model: data.model || 'unknown',
        size: JSON.stringify(data).length
      };

      console.log(`💾 History entry: ${JSON.stringify(historyEntry).substring(0, 200)}...`);

      // Store in a sorted set with timestamp as score for easy retrieval
      await this.client.zAdd('cache:history', [
        {
          score: Date.now(),
          value: JSON.stringify(historyEntry)
        }
      ]);

      // Keep only the last N entries
      const totalEntries = await this.client.zCard('cache:history');
      console.log(`💾 Total history entries after storage: ${totalEntries}`);
      if (totalEntries > this.historyLimit) {
        const removeCount = totalEntries - this.historyLimit;
        await this.client.zRemRangeByRank('cache:history', 0, removeCount - 1);
        console.log(`💾 Removed ${removeCount} old entries`);
      }
    } catch (error) {
      console.error('Cache history storage error:', error);
    }
  }

  async getCacheHistory(limit = 50) {
    try {
      console.log(`🔍 Getting cache history with limit: ${limit}`);
      // Get recent cache history entries (most recent first)
      const entries = await this.client.zRange('cache:history', 0, limit - 1, { REV: true });
      console.log(`🔍 Found ${entries.length} raw history entries`);
      
      const parsedEntries = entries.map(entry => {
        try {
          return JSON.parse(entry);
        } catch (parseError) {
          console.error('Error parsing cache history entry:', parseError);
          return null;
        }
      }).filter(entry => entry !== null);
      
      console.log(`🔍 Successfully parsed ${parsedEntries.length} history entries`);
      return parsedEntries;
      
    } catch (error) {
      console.error('Cache history retrieval error:', error);
      return [];
    }
  }

  async populateInitialHistory() {
    try {
      console.log('�� Populating initial cache history from existing entries...');
      
      // Get all existing cache keys
      const keys = await this.client.keys('llm:*');
      console.log(`Found ${keys.length} existing cache keys`);
      
      let populatedCount = 0;
      const batchSize = 10;
      
      for (let i = 0; i < Math.min(keys.length, 20); i += batchSize) {
        const batch = keys.slice(i, i + batchSize);
        
        for (const key of batch) {
          try {
            const data = await this.client.get(key);
            if (data) {
              const parsedData = JSON.parse(data);
              const cacheKey = key.replace('llm:', '');
              
              // Create a mock history entry for existing cache data
              const historyEntry = {
                cacheKey: cacheKey,
                timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(), // Random timestamp within last day
                request: parsedData.request || null,
                response: parsedData.response || parsedData,
                model: parsedData.model || 'unknown',
                size: JSON.stringify(parsedData).length
              };

                             // Store in cache history
               await this.client.zAdd('cache:history', [
                 {
                   score: Date.now() - Math.random() * 86400000, // Random score within last day
                   value: JSON.stringify(historyEntry)
                 }
               ]);
              
              populatedCount++;
            }
          } catch (err) {
            console.error(`Error processing cache key ${key}:`, err);
          }
        }
      }
      
      console.log(`✅ Populated ${populatedCount} initial history entries`);
      return populatedCount;
      
    } catch (error) {
      console.error('Error populating initial history:', error);
      return 0;
    }
  }

  async clearCache(pattern = 'llm:*', clearHistory = true) {
    try {
      const keys = await this.client.keys(pattern);
      let clearedKeys = 0;
      
      if (keys.length > 0) {
        await this.client.del(keys);
        clearedKeys = keys.length;
        console.log(`🗑️ Cleared ${keys.length} cache entries`);
      }
      
      // If clearing all cache entries (default pattern), also clear cache history
      if (clearHistory && (pattern === 'llm:*' || pattern === '*')) {
        const historyEntries = await this.client.zCard('cache:history');
        if (historyEntries > 0) {
          await this.client.del('cache:history');
          console.log(`🗑️ Cleared ${historyEntries} cache history entries`);
        }
      }
      
      return clearedKeys;
    } catch (error) {
      console.error('Cache clear error:', error);
      throw new Error(`Failed to clear cache: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getStats() {
    try {
      const keys = await this.client.keys('llm:*');
      const memoryInfo = await this.client.info('memory');
      const serverInfo = await this.client.info('server');
      const statsInfo = await this.client.info('stats');
      
      // Parse memory info
      const usedMemoryMatch = memoryInfo.match(/used_memory:(\d+)/);
      const usedMemoryHumanMatch = memoryInfo.match(/used_memory_human:([^\r\n]+)/);
      const usedMemory = usedMemoryMatch ? parseInt(usedMemoryMatch[1]) : 0;
      const usedMemoryHuman = usedMemoryHumanMatch ? usedMemoryHumanMatch[1] : '0B';

      // Parse stats info
      const keyspaceHitsMatch = statsInfo.match(/keyspace_hits:(\d+)/);
      const keyspaceMissesMatch = statsInfo.match(/keyspace_misses:(\d+)/);
      const connectedClientsMatch = statsInfo.match(/connected_clients:(\d+)/);
      
      // Parse server info
      const uptimeMatch = serverInfo.match(/uptime_in_seconds:(\d+)/);
      
      // Group keys by pattern
      const patterns = {};
      keys.forEach(key => {
        const pattern = key.startsWith('llm:') ? 'llm:*' : 'other:*';
        patterns[pattern] = (patterns[pattern] || 0) + 1;
      });

      return {
        redis_info: {
          used_memory: usedMemory,
          used_memory_human: usedMemoryHuman,
          keyspace_hits: keyspaceHitsMatch ? parseInt(keyspaceHitsMatch[1]) : 0,
          keyspace_misses: keyspaceMissesMatch ? parseInt(keyspaceMissesMatch[1]) : 0,
          connected_clients: connectedClientsMatch ? parseInt(connectedClientsMatch[1]) : 1,
          uptime_in_seconds: uptimeMatch ? parseInt(uptimeMatch[1]) : 0
        },
        cache_keys: {
          total: keys.length,
          patterns: patterns
        },
        service_info: {
          status: this.client.isOpen ? 'connected' : 'disconnected',
          cache_timeout: this.cacheTimeout
        }
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        redis_info: {
          used_memory: 0,
          used_memory_human: '0B',
          keyspace_hits: 0,
          keyspace_misses: 0,
          connected_clients: 0,
          uptime_in_seconds: 0
        },
        cache_keys: {
          total: 0,
          patterns: {}
        },
        service_info: {
          status: 'error',
          error: error.message
        }
      };
    }
  }

  async healthCheck() {
    try {
      await this.client.ping();
      return { redis: 'healthy' };
    } catch (error) {
      return { redis: 'unhealthy', error: error.message };
    }
  }
}

module.exports = new CacheManager(); 