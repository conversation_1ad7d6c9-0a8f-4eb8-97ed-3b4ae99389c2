const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  // Create a unique request ID for tracking
  const requestId = Math.random().toString(36).substr(2, 9);
  req.requestId = requestId;
  
  // Skip verbose logging for health check endpoints
  const isHealthCheck = req.originalUrl === '/api/health';
  
  // Log incoming request (skip for health checks unless it's an error)
  const requestInfo = {
    requestId,
    timestamp,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent') || 'unknown',
    contentType: req.get('Content-Type') || 'none',
    contentLength: req.get('Content-Length') || '0'
  };
  
  // Log request body for POST/PUT requests (but limit size for safety)
  if ((req.method === 'POST' || req.method === 'PUT') && req.body) {
    const bodyString = JSON.stringify(req.body);
    requestInfo.bodySize = bodyString.length;
    
    // Only log first 500 chars of body to avoid overwhelming logs
    if (bodyString.length > 500) {
      requestInfo.body = bodyString.substring(0, 500) + '... (truncated)';
    } else {
      requestInfo.body = req.body;
    }
  }
  
  // Only log health checks if they're not routine successful checks
  if (!isHealthCheck) {
    // Simplified logging - just show method, URL, and ID
    console.log(`📥 [${requestId}] ${req.method} ${req.originalUrl}`);
  }
  
  // Override res.json to log responses
  const originalJson = res.json;
  res.json = function(data) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const responseInfo = {
      requestId,
      timestamp: new Date().toISOString(),
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: JSON.stringify(data).length
    };
    
    // Log response data (limit size for safety)
    const responseString = JSON.stringify(data);
    if (responseString.length > 1000) {
      responseInfo.response = responseString.substring(0, 1000) + '... (truncated)';
    } else {
      responseInfo.response = data;
    }
    
    // Use different log levels based on status code
    if (res.statusCode >= 400) {
      console.error(`📤 [${requestId}] ERROR RESPONSE:`, JSON.stringify(responseInfo, null, 2));
    } else {
      // For health checks, only log errors or if the response indicates unhealthy status
      if (isHealthCheck) {
        // Only log health checks if they indicate an unhealthy status
        if (data && (data.status === 'unhealthy' || res.statusCode !== 200)) {
          console.warn(`🏥 [${requestId}] HEALTH CHECK ISSUE:`, JSON.stringify(responseInfo, null, 2));
        }
        // Otherwise, silent success for health checks
      } else {
        // Simplified success logging
        console.log(`📤 [${requestId}] ${res.statusCode} (${duration}ms)`);
      }
    }
    
    return originalJson.call(this, data);
  };
  
  // Override res.status to catch status changes
  const originalStatus = res.status;
  res.status = function(statusCode) {
    if (statusCode >= 400) {
      console.warn(`⚠️  [${requestId}] Status code set to: ${statusCode}`);
    }
    return originalStatus.call(this, statusCode);
  };
  
  // Handle unexpected errors
  res.on('error', (error) => {
    console.error(`💥 [${requestId}] Response error:`, error);
  });
  
  next();
};

module.exports = requestLogger; 