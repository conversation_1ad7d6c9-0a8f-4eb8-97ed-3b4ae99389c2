const DEFAULT_TOKEN = 'llm-cache-default-token-2024';

const authMiddleware = (req, res, next) => {
  // Skip auth for health checks and root endpoint
  if (req.path === '/api/health' || req.path === '/') {
    return next();
  }

  const token = req.headers.authorization?.replace('Bearer ', '') || req.headers['x-auth-token'];
  const expectedToken = process.env.AUTH_TOKEN || DEFAULT_TOKEN;

  if (!token) {
    return res.status(401).json({
      error: 'Authorization required',
      message: 'Please provide an authorization token'
    });
  }

  if (token !== expectedToken) {
    return res.status(403).json({
      error: 'Invalid token',
      message: 'The provided authorization token is invalid'
    });
  }

  next();
};

module.exports = authMiddleware; 