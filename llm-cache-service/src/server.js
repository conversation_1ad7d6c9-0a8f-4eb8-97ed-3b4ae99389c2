const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const cacheRoutes = require('./routes/cache');
const statsRoutes = require('./routes/stats');
const healthRoutes = require('./routes/health');
const authMiddleware = require('./middleware/auth');
const requestLogger = require('./middleware/request-logger');

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT || 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware (add after body parsing but before auth)
app.use(requestLogger);

// Auth middleware for protected routes (but not health)
app.use('/api/cache', authMiddleware);
app.use('/api/stats', authMiddleware);

// Routes
app.use('/api/cache', cacheRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/health', healthRoutes);

// Basic info endpoint (no auth required)
app.get('/', (req, res) => {
  res.json({
    service: 'LLM Cache Service',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 LLM Cache Service running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔐 Auth token: ${process.env.AUTH_TOKEN ? 'configured' : 'NOT SET - using default'}`);
  console.log(`🌐 CORS origins: ${process.env.ALLOWED_ORIGINS || 'all origins allowed'}`);
  console.log(`📝 Request logging: ENABLED - all requests will be logged with details`);
  console.log(`⚡ Rate limit: ${process.env.RATE_LIMIT || 1000} requests per 15 minutes`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
}); 