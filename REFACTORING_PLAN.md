# Import Wizard Refactoring Plan

## Overview
This document outlines a comprehensive refactoring plan to reduce code duplication between the two main import wizard components: `ai-transform-mapping-step.tsx` (2,113 lines) and `data-preview-step.tsx` (1,978 lines). These components share significant amounts of duplicate logic (~4,091 lines total with massive overlap).

## Current State Analysis

### Phase 1: Shared Hooks and Components ✅ COMPLETED
Created reusable hooks and components:
- **`use-source-data.ts`** (365 lines): Consolidates source data, job notes, and worksheet data loading
- **`use-transformation-state.ts`** (420 lines): Unifies transformation operations and progress tracking
- **`RowNavigation.tsx`** (240 lines): Full and compact navigation components

### Phase 2: Initial Integration ✅ COMPLETED
Successfully integrated the first reusable component:
- Replaced 60+ lines of custom navigation code in `data-preview-step.tsx` with the new `RowNavigation` component
- Build passes successfully (exit code 0)
- Maintained all existing functionality while using cleaner, reusable code
- Demonstrated proof-of-concept for further integration

## Detailed Duplication Analysis

## Frontend Refactoring Todo

### Successfully Refactored:
- ✅ New custom hooks: `use-data-pagination`, `use-data-transformation`, `use-ai-transform-column`
- ✅ Modular components: `SourceDataPreview`, `LLMTester`, `ColumnProgressTracker`
- ✅ Unified backend: `TransformationEngine`, `ResponseParser`
- ✅ Fixed build errors and import paths

### **NEW: Phase 1 High Priority Completed ✅**
- ✅ **`use-source-data.ts` hook** - Consolidates all source data loading logic (365 lines)
- ✅ **`use-transformation-state.ts` hook** - Unifies transformation state management (420 lines)
- ✅ **`RowNavigation.tsx` component** - Standardizes row navigation UI (240 lines)

### Remaining Issues:
- 🔴 **Duplicate data loading logic** across `data-preview-step.tsx` (1978 lines) and `ai-transform-mapping-step.tsx` (2113 lines)
- 🔴 **Inconsistent state management** between the two steps
- 🔴 **Similar UI patterns** implemented differently in each component
- 🔴 **Redundant API calls** for the same data

## Frontend Refactoring Todo

### ✅ Phase 1: Shared Data Management (COMPLETED)

#### ✅ 1. `use-source-data.ts` hook - DONE
**Location:** `src/hooks/use-source-data.ts` (365 lines)
**Purpose:** Centralize all source data loading logic

**Features Implemented:**
- Unified source data, job notes, and worksheet data loading
- Enhanced error handling with user-friendly messages
- Row navigation state management
- Auto-loading with configurable options
- Markdown formatting utilities
- Consistent API error handling

#### ✅ 2. `use-transformation-state.ts` hook - DONE
**Location:** `src/hooks/use-transformation-state.ts` (420 lines)
**Purpose:** Unify transformation state management

**Features Implemented:**
- Column model mappings management
- Transformation results tracking
- Progress tracking with timer management
- Transformation operations (single row, multiple rows, all rows)
- Cancellation support with proper cleanup
- Export functionality
- Duration formatting utilities

#### ✅ 3. `RowNavigation` component - DONE
**Location:** `src/components/import-wizard/steps/components/RowNavigation.tsx` (240 lines)
**Purpose:** Standardize row navigation across components

**Features Implemented:**
- Full navigation component with jump-to-row functionality
- Compact navigation component for smaller spaces
- Size variants (sm, md, lg)
- Configurable features (first/last buttons, jump input)
- Proper accessibility with titles and disabled states

### 🔄 Phase 2: Component Integration (NEXT - HIGH PRIORITY)

#### 4. Update `data-preview-step.tsx` to use new hooks
**Target:** Replace ~400 lines of duplicate logic with the new hooks
**Benefits:** Reduce component from 1978 lines to ~1200-1400 lines

#### 5. Update `ai-transform-mapping-step.tsx` to use new hooks
**Target:** Replace ~500 lines of duplicate logic with the new hooks
**Benefits:** Reduce component from 2113 lines to ~1400-1600 lines

#### 6. Create unified `DataGridPreview` component
**Location:** `src/components/import-wizard/steps/components/DataGridPreview.tsx`
**Purpose:** Unified data preview for both steps

### Phase 3: UI Component Consolidation (Medium Priority)

#### 7. Standardize `TransformationTester` component
**Location:** `src/components/import-wizard/steps/components/TransformationTester.tsx`
**Purpose:** Unified testing interface for both single-column and batch operations

#### 8. Create `ColumnConfigurationManager` component
**Location:** `src/components/import-wizard/steps/components/ColumnConfigurationManager.tsx`
**Purpose:** Centralize column mapping, validation, and configuration

### Phase 4: Advanced Optimizations (Lower Priority)

#### 9. Implement `use-column-config.ts` hook
Advanced column configuration management with validation and caching.

#### 10. Create `ProgressTracker` component
Unified progress tracking across all transformation operations.

## Backend Refactoring Todo

### Phase 1: API Consolidation (High Priority)

#### 1. Unify data loading endpoints
**Current:** Multiple endpoints (`/api/import/grid-data`, `/api/import/source-preview`, etc.)
**Target:** Single `/api/import/data` with query parameters for different views

#### 2. Standardize transformation APIs
**Current:** Scattered across multiple `/api/import/` endpoints
**Target:** Consolidated under `/api/transform/` with consistent interfaces

### Phase 2: Service Layer Enhancement (Medium Priority)

#### 3. Create shared `DataService` class
**Location:** `src/lib/services/data-service.ts`
**Purpose:** Centralize all data operations with consistent caching and error handling

#### 4. Enhance `TransformationEngine`
- Add real-time progress updates via WebSockets
- Implement better error recovery
- Unified logging across all operations

## **Updated Success Metrics**

### ✅ Phase 1 Results:
- **New Hooks Created:** 1,025 lines of reusable logic
- **Components Created:** 240 lines of reusable UI
- **Build Status:** ✅ Compiles successfully
- **Ready for Integration:** All hooks and components tested

### Before Integration (Current):
- `ai-transform-mapping-step.tsx`: **2,113 lines**
- `data-preview-step.tsx`: **1,978 lines**
- **Total:** 4,091 lines with significant duplication

### After Phase 2 Integration (Target):
- `ai-transform-mapping-step.tsx`: **~1,400-1,600 lines** (-25-35% reduction)
- `data-preview-step.tsx`: **~1,200-1,400 lines** (-25-30% reduction)
- **Shared Logic:** ~1,025 lines in reusable hooks
- **Total:** ~3,600-4,000 lines with minimal duplication
- **Net Benefit:** Cleaner code + better maintainability + consistent behavior

## **Next Actions (Priority Order)**

### **Immediate Next Steps:**
1. **Integrate `use-source-data` hook into `data-preview-step.tsx`**
   - Replace source data loading logic
   - Replace job notes management
   - Replace row navigation state
   - **Expected:** ~200-300 line reduction

2. **Integrate `use-transformation-state` hook into `data-preview-step.tsx`**
   - Replace transformation logic
   - Replace progress tracking
   - Replace column mappings
   - **Expected:** ~300-400 line reduction

3. **Update `RowNavigation` usage in both components**
   - Replace custom navigation with `RowNavigation` component
   - **Expected:** ~50-100 line reduction per component

4. **Repeat integration for `ai-transform-mapping-step.tsx`**

### **This Week Target:**
- Complete Phase 2 integration for both components
- Test functionality remains identical
- Achieve target line count reductions

### **Success Criteria:**
- Both main components reduced by 25-35%
- No functionality regression
- Consistent behavior between components
- Build continues to pass

This refactoring has successfully created the foundation for major code deduplication. The next phase will integrate these shared utilities into the main components to achieve the target reductions. 