# Development Commands

Run these commands in separate terminals for hot reloading development:

## Terminal 1: Redis DB
```bash
docker run --rm -it \
  --name redis-dev \
  -p 6379:6379 \
  -v redis_data:/data \
  -v $(pwd)/redis.conf:/etc/redis/redis.conf:ro \
  redis:7-alpine \
  redis-server /etc/redis/redis.conf
```

## Terminal 2: LLM Cache Service
```bash
cd llm-cache-service && \
docker build -t llm-cache-dev . && \
docker run --rm -it \
  --name llm-cache-dev \
  -p 3001:3001 \
  -e PORT=3001 \
  -e REDIS_HOST=host.docker.internal \
  -e REDIS_PORT=6379 \
  -e LLM_CACHE_AUTH_TOKEN=llm-cache-default-token-2024 \
  -e ALLOWED_ORIGINS="*" \
  -e RATE_LIMIT=1000 \
  -e NODE_ENV=development \
  llm-cache-dev
```

## Terminal 3: Web App (Hot Reloading)
```bash
cd akeneo-importer && \
npm install && \
npm run dev
```

## Environment Variables
Make sure your `akeneo-importer/.env` file contains:
```env
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
NODE_ENV=development
LLM_CACHE_SERVICE_URL=http://localhost:3001
LLM_CACHE_AUTH_TOKEN=llm-cache-default-token-2024
```

## URLs
- **Web App**: http://localhost:3000
- **LLM Cache**: http://localhost:3001
- **LLM Cache Health**: http://localhost:3001/api/health
- **LLM Cache Stats**: http://localhost:3001/api/stats
- **Redis**: localhost:6379

## Optional: Redis Commander (Database GUI)
```bash
docker run --rm -it \
  --name redis-commander \
  -p 8081:8081 \
  -e REDIS_HOSTS=redis:host.docker.internal:6379 \
  -e HTTP_USER=admin \
  -e HTTP_PASSWORD=admin123 \
  rediscommander/redis-commander:latest
```
Then visit: http://localhost:8081

## Stopping Services
Just press `Ctrl+C` in each terminal to stop the services. 